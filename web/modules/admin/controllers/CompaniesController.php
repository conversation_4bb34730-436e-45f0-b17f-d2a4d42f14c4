<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Throwable;
use yii\db\Query;
use yii\web\Request;
use yii\web\Response;
use yii\helpers\Json;
use yii\db\Expression;
use frontend\traits\Access;
use common\models\DevCompany;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\web\BadRequestHttpException;
use frontend\modules\admin\AdminController;
use frontend\modules\admin\models\GameSearchForm;

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class CompaniesController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $companiesDataProvider = null;
    $searchModel = new GameSearchForm();
    $get = $this->request->get();

    if (isset($get['term']) && !empty(\trim($get['term']))) {
      $term = \trim($get['term']);
      $searchModel->title = $term;
      $companiesDataProvider = new ActiveDataProvider([
        'query' => DevCompany::find()
          ->where(['like', 'comp_name', $get['term']])
          ->orderBy(new Expression('CHAR_LENGTH(comp_name) ASC')),
      ]);
    }

    return $this->render('index', \compact('companiesDataProvider'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionAdd(): string
  {
    $company = new DevCompany();

    return $this->render('editor', \compact('company'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $company = DevCompany::findOne($id);

    if (!$company) {
      throw new NotFoundHttpException('Компания не найдена');
    }

    return $this->render('editor', \compact('company'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['games_access'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    $company = new DevCompany();
    $post = $this->request->post();
    if (isset($post['DevCompany']['comp_id'])) {
      $company = DevCompany::findOne($post['DevCompany']['comp_id']);
      if (!$company) {
        throw new BadRequestHttpException('Не удалось сохранить — подборка не существует');
      }
    }
    $company->load($this->request->post());
    if ($company->save()) {
      return $this->redirect("/admin/companies?term=$company->comp_name");
    }
    return $this->render('editor', \compact('company'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET', 'POST'])]
  public function actionMerge(string $id): Response|string
  {
    $company = DevCompany::findOne($id);
    if (!$company) {
      throw new NotFoundHttpException('Компания не найдена');
    }

    if ($this->request->isPost) {
      $newId = $this->request->post('newId');

      if ($newId === $id) {
        Yii::$app->session->setFlash('error', 'Нельзя объединить компанию саму с собой');
        goto end;
      }

      $newCompany = DevCompany::findOne($newId);

      if ($newCompany) {
        $db = Yii::$app->db;
        // Убеждаемся, что к одной игре в одно и то же поле не будет привязана одна и та же компания
        // Без этого легко нарваться на unique key constrain violation
        $subQuery = new Query()->select('*')
          ->from('`games_company` d')
          ->where('d.GameId = o.GameId')
          ->andWhere('d.comp_type = o.comp_type')
          ->andWhere(['d.comp_id' => $newId]);
        // Меняем ID у всех записей со старой компанией на новую (кроме тех случаев, когда такая запись уже есть)
        $db->createCommand()
          ->update('`games_company` o', ['o.comp_id' => $newId], [
            'AND',
            ['o.comp_id' => $id],
            ['NOT', ['EXISTS', $subQuery]],
          ])
          ->execute();
        // Подчищаем оставшиеся записи (те, что не поменяли, потому что они бы стали дубликатами существующих)
        $db->createCommand()
          ->delete('games_company', ['comp_id' => $id])
          ->execute();
        $company->delete();
        $alternativeTitles = $newCompany->alternative_titles ?? '[]';
        $alternativeTitles = Json::decode($alternativeTitles);
        $alternativeTitles[] = $company->comp_name;
        $newCompany->alternative_titles = Json::encode($alternativeTitles);
        $newCompany->save();
        Yii::$app->session->setFlash(
          'success',
          "Компания «{$company->comp_name}» объединена с «{$newCompany->comp_name}»",
        );
        return $this->redirect("/admin/companies?term=$newCompany->comp_name");
      }
      Yii::$app->session->setFlash('error', 'Компания не найдена');
    }

    end:
    return $this->render('merge', \compact('company'));
  }
}
