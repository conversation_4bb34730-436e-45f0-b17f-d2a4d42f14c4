<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Throwable;
use yii\web\Response;
use frontend\traits\Access;
use frontend\components\View;
use yii\caching\TagDependency;
use common\models\{User, Comment};
use yii\web\NotFoundHttpException;
use yii\base\InvalidConfigException;
use frontend\modules\admin\AdminController;

/**
 * @noinspection PhpUnused
 * @property-read View $view
 */
class RbacController extends AdminController
{
  /** @var string[] */
  protected static array $order = [
    'Общие',
    'Модерация',
    'Новости',
    'Статьи',
    'Видео',
    'Трейлеры',
    'Прохождения',
    'Советы и тактика',
    'Раздачи ключей',
    'Анонсер',
    'Стримы',
    'База игр',
    'Факты',
    'Скриншоты',
    'Читы',
    'Трейнеры',
    'Администрирование',
  ];

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionIndex(): string
  {
    return $this->render('index', ['order' => self::$order]);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $rbac = Yii::$app->authManager;
    if (!$rbac) {
      throw new InvalidConfigException('Менеджер авторизации не настроен');
    }
    $role = $rbac->getRole($id);

    if (!$role) {
      throw new NotFoundHttpException('Роль не найдена');
    }

    $assignments = $rbac->getChildren($role->name);

    return $this->render('edit', [
      'role' => $role,
      'assignments' => $assignments,
      'order' => self::$order,
    ]);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionUsers(string $id): string
  {
    $rbac = Yii::$app->authManager;
    if (!$rbac) {
      throw new InvalidConfigException('Менеджер авторизации не настроен');
    }
    $role = $rbac->getRole($id);

    if (!$role) {
      throw new NotFoundHttpException('Роль не найдена');
    }

    return $this->render('users', \compact('role'));
  }

  /** @noinspection PhpUnused */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionLoginAs(string $id): Response
  {
    if (\is_numeric($id)) {
      $user = User::findOne($id);
    } else {
      $user = User::findOne(['user_name' => $id]);
    }

    if (!$user) {
      return $this->redirect('/');
    }

    Yii::$app->session->set('original_user', Yii::$app->user->id);
    Yii::$app->user->login($user);
    return $this->redirect('/');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionPlates(): string
  {
    $this->view->title = 'Титулы пользователей ⋅ Админка';
    $users = User::find()
      ->where(['NOT', ['team_name' => null]])
      ->andWhere(['NOT', ['team_name' => '']])
      ->orderBy(['show_name' => 'ASC'])
      ->all();

    return $this->render('plates', \compact('users'));
  }

  /**
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['moderate_user_bans'], methods: ['GET', 'POST'])]
  public function actionDeleteComments(string $id): Response
  {
    /** @var ?User $user */
    $user = User::find()
      ->where(['user_name' => $id])
      ->one();

    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    Comment::updateAll(['comment_delete' => 1], ['user_id' => $user->id]);
    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, ['comment_claims']);
    }
    return $this->redirect("/user/$id/comments");
  }
}
