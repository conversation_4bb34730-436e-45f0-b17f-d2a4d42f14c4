<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use frontend\traits\Access;
use frontend\components\View;
use yii\base\InvalidConfigException;
use common\components\db\ActiveQuery;
use yii\data\{Sort, ActiveDataProvider};
use frontend\modules\admin\AdminController;
use common\exceptions\ControlFlowException;
use common\helpers\{Sg<PERSON>elper, SgStringHelper};
use yii\db\{ActiveQueryInterface, Expression, StaleObjectException};
use common\models\{Article, ArticleGame, GameTitle, GameRequest, GameRequestAttachment};
use yii\web\{Request, Response, UploadedFile, NotFoundHttpException, ForbiddenHttpException};

use function Sentry\captureException;

/**
 * @property-read View $view
 * @property-read Request $request
 * @property-read ActiveQuery<Article> $trainersQuery См. {@see trainersController::getTrainersQuery}
 */
class TrainersController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trainers_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $query = $this->trainersQuery;
    $query->andWhere([
      'data_active' => 1,
    ]);
    $this->view->title = 'Трейнеры ⋅ Админка';

    return $this->renderTrainersTable($query);
  }

  /* @noinspection PhpUnused */
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trainers_access'], methods: ['GET'])]
  public function actionReadyToPublish(): string
  {
    $this->view->title = 'Трейнеры ⋅ Админка';
    $query = $this->trainersQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ]);

    return $this->renderTrainersTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trainers_access'], methods: ['GET'])]
  public function actionDrafts(): string
  {
    $this->view->title = 'Трейнеры ⋅ Админка';
    $user = Yii::$app->user;
    $query = $this->trainersQuery
      ->andWhere([
        'data_active' => false,
        'ready_to_publish' => false,
      ]);

    if (!$user->can('trainers_manage')) {
      $query->andWhere(['user_id' => $user->id]);
    }

    return $this->renderTrainersTable($query);
  }

  /**
   * @param ActiveQuery<Article> $query
   * @throws Throwable
   */
  protected function renderTrainersTable(ActiveQuery $query): string
  {
    $term = $this->request->get('term', '');
    $term = \trim($term);
    if ($term !== '') {
      $term = SgHelper::stripThe($term);
      $gameIds = GameTitle::find()
        ->select('GameId')
        ->andWhere([
          'OR',
          ['LIKE', 'GameName', $term],
          ['LIKE', 'GameKeywords', $term],
        ])
        ->groupBy(['GameId'])
        ->column();
      $query->andWhere(['game_id' => $gameIds]);
    }

    $dataProvider = new ActiveDataProvider(\compact('query'));
    $dataProvider->setSort(
      new Sort([
        'defaultOrder' => ['data_add' => \SORT_DESC],
      ]),
    );

    $readyCount = $this->trainersQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ])->count();

    if ($readyCount === '0') {
      $readyCount = null;
    }

    return $this->render('index', \compact('dataProvider', 'readyCount', 'term'));
  }

  /**
   * @return ActiveQuery<Article>
   * @throws InvalidConfigException
   */
  public function getTrainersQuery(): ActiveQueryInterface
  {
    return Article::find()
      ->joinWith('articleGames')
      ->with('user', 'gameTitle', 'announce')
      ->where(['data_type' => 'trainers']);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trainers_create'], methods: ['GET'])]
  public function actionAdd(): string
  {
    $this->view->title = 'Новый тренер ⋅ Админка';
    $article = new Article();
    $article->user_id = (int)Yii::$app->user->id;
    $gameTitle = new GameTitle();
    $article->setScenario(Article::SCENARIO_DRAFT);

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trainers_create'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Трейнер не найден');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя редактировать чужого тренера');
    }

    if (!$article->data_active && !$article->ready_to_publish) {
      $article->setScenario(Article::SCENARIO_DRAFT);
    }

    $gameTitle = $article->gameTitle ?? new GameTitle();
    $this->view->title = "$article->title ⋅ Админка";

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trainers_create'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    $post = $this->request->post();

    $article = new Article();

    $id = $post['Article']['base_id'];
    if ($id) {
      $article = Article::findOne($id);

      if (!$article) {
        throw new NotFoundHttpException('Трейнер не найден');
      }

      if (!$article->canEdit) {
        throw new ForbiddenHttpException('Нельзя редактировать чужого тренера');
      }
    }

    $article->data_type = 'trainers';
    $article->data_title = 'трейнер';

    if (\is_numeric($post['options_count']) && ($post['options_count'] > 0)) {
      $article->data_title = "+{$post['options_count']} трейнер";
    }

    if (!empty($post['game_version'])) {
      $versionInfo = " (для версии {$post['game_version']})";
    } else {
      $versionInfo = '';
    }

    $gameTitle = new GameTitle();
    $isNew = $article->isNewRecord;
    $articleGames = [];

    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $gameId = $post['GameTitle']['GameId'] ?? null;
      if (\is_array($gameId)) {
        $gameId = $gameId[0] ?? null;
      }
      $article->load($this->request->post());

      $skipValidation = false;
      if (isset($post['submit_article_draft'])) {
        $skipValidation = true;
        $article->setScenario(Article::SCENARIO_DRAFT);
      }

      if (empty($gameId)) {
        $gameTitle->addError('GameId', 'Игра не указана');
      } else {
        $gameTitle = GameTitle::findOne(['GameId' => $gameId]);

        if ($gameTitle) {
          $articleGames[] = $gameTitle->GameId;

          $trainersExistsQuery = Article::find()
            ->joinWith('articleGames')
            ->where([
              'game_id' => $gameTitle->GameId,
              'CommentId' => null,
              'data_type' => 'trainers',
              'data_title' => $article->data_title,
            ]);

          if (!$article->isNewRecord) {
            $trainersExistsQuery->andWhere(['NOT', ['base_id' => $article->base_id]]);
          }

          if (empty($article->CommentId) && $trainersExistsQuery->exists()) {
            $trainersParentId = (int)$trainersExistsQuery->select('base_id')->scalar();
            $article->CommentId = $trainersParentId;
          }
        } elseif (!$skipValidation) {
          $gameTitle = new GameTitle(['GameId' => $gameId]);
          $gameTitle->addError('GameId', 'Игра с таким названием не найдена в базе');
        }
      }

      $article->setScenario(Article::SCENARIO_TRAINER);
      if (!$article->data_active) {
        $article->data_active = 0;
        $article->ready_to_publish = (int)isset($post['submit_article_publish']);
      }

      if (!$article->user_id && $article->isNewRecord && ($article->user_id !== 0)) {
        $article->user_id = (int)Yii::$app->user->id;
      }

      if ($article->user_id === 0) {
        $article->user_id = null;
      }

      if ($article->data_text === null) {
        $article->data_text = '';
      }

      $article->data_text = SgStringHelper::nl2br(\htmlentities($article->data_text));

      $textAddon = '';
      if (mb_strlen($article->data_text) > 5) {
        $textAddon = "<br><br>Опции:<br><br>$article->data_text";
      }

      $article->data_text = SgStringHelper::ucfirst($article->data_title)
        . "$versionInfo.<br>Распакуй все файлы из архива.<br>Запусти трейнер.<br>Запусти игру, не закрывая трейнер.<br>Во время игры нажимай на клавиши, указанные в трейнере.$textAddon";

      $file = UploadedFile::getInstanceByName('file');

      if (!$file && empty($article->data_file)) {
        $article->addError('GameAttach', 'Не загружен файл');
      } elseif ($file && $gameTitle) {
        $article->GameAttach = (int)Article::find()
          ->select(new Expression('MAX(GameAttach)'))
          ->scalar();
        $article->GameAttach++;

        $baseUrl = SgStringHelper::urlToBase($article->data_title);
        $translit = SgStringHelper::translit($gameTitle->title);
        $time = \time();
        $fileName = \strtolower(\stripslashes("$translit.$baseUrl.stopgame.ru.$time.$file->extension"));
        $filePath = "/home/<USER>/tools/help/uploads/$fileName";
        \move_uploaded_file($file->tempName, $filePath);
        $article->data_file = $fileName;
        $article->GameAttachSize = (int)\filesize($filePath);
      }

      if ($article->hasErrors() || (!empty($gameTitle) && $gameTitle->hasErrors())
        || !$article->validate() || !$article->save()
      ) {
        throw new ControlFlowException('Не удалось сохранить тренер');
      }

      $article->refresh();

      $requestIds = $post['game_request_id'] ?? [];
      $requestTitles = $post['game_request_title'] ?? [];

      GameRequestAttachment::deleteAll([
        'target_type' => 'show',
        'target_id' => $article->base_id,
      ]);

      ArticleGame::deleteAll([
        'article_id' => $article->base_id,
      ]);

      // Привязываем запросы на добавление игр
      foreach ($requestTitles as $requestTitle) {
        $foundGame = GameTitle::findOne(['GameName' => $requestTitle]);

        // Если уже есть игра с таким названием, то просто привязываем игру
        if ($foundGame) {
          $articleGames[] = $foundGame->GameId;
        } else {
          $foundRequest = GameRequest::findOne(['title' => $requestTitle]);

          // Если уже есть запрос с таким названием
          if ($foundRequest) {
            // И он уже обработан — привязываем игру
            if ($foundRequest->game_id) {
              $articleGames[] = $foundRequest->game_id;
              // И он ещё не обработан — привязываем материал к запросу
            } else {
              $requestIds[] = $foundRequest->id;
            }
            // Если запроса нет — создаём его и привязываем материал к запросу
          } else {
            $request = new GameRequest([
              'title' => $requestTitle,
              'user_id' => Yii::$app->user->id,
            ]);
            $request->save();
            $request->user?->recalculateCredibility();
            $requestIds[] = $request->id;
          }
        }
      }

      $requestIds = \array_unique($requestIds);
      foreach ($requestIds as $requestId) {
        $attachment = new GameRequestAttachment([
          'request_id' => $requestId,
          'target_type' => 'show',
          'target_id' => $article->base_id,
        ]);
        $attachment->save();
      }

      $articleGames = \array_unique($articleGames);
      foreach ($articleGames as $gameId) {
        $articleGame = new ArticleGame([
          'article_id' => $article->base_id,
          'game_id' => $gameId,
        ]);
        $articleGame->save();
      }

      if (\count($article->dirtyAttributes) > 0) {
        $article->save();
      }

      $article->clearShowCache();
      $article->clearUnpublishedCache();

      $url = '/admin/trainers/drafts';
      if ($article->ready_to_publish) {
        $url = '/admin/trainers/ready-to-publish';
      } elseif ($article->data_active) {
        $url = '/admin/trainers';
      }
      $autosaveId = $isNew ? 'new' : $article->base_id;
      Yii::$app->session->setFlash('remove-autosave', 'trainers-form-' . $autosaveId);
      return $this->redirect($url);
    } catch (Exception $exception) {
      captureException($exception);
      $this->view->title = "$article->title ⋅ Админка";
      $article->setScenario(Article::SCENARIO_DRAFT);
      $url = '/admin/trainers/add';
      if (!$isNew) {
        $url = "/admin/trainers/edit/$article->base_id";
      }

      $message = $exception->getMessage();
      if (!empty($message)) {
        $article->addError('data_id', $message);
      }

      $this->view->registerJs("window.history.replaceState({}, '$article->title ⋅ Админка', '$url')");
      if (!$gameTitle) {
        $gameTitle = new GameTitle();
      }
      return $this->renderEditor($article, $gameTitle);
    }
  }

  /**
   * @throws Throwable
   */
  protected function renderEditor(Article $article, GameTitle $gameTitle): string
  {
    $article->data_text = \html_entity_decode(SgStringHelper::br2nl($article->data_text ?? ''));
    $gameTitle->setScenario(GameTitle::SCENARIO_ATTACH);

    $optionsCount = 0;
    $gameVersion = '';

    if (!empty($article->data_title)) {
      $optionsCount = (int)\preg_replace('/\D/', '', $article->data_title);
      if (empty($optionsCount)) {
        $optionsCount = 0;
      }
    }

    if (!empty($article->data_text) && \preg_match('/\(для версии (.*)\)/u', $article->data_text, $result)) {
      $gameVersion = $result[1];
    }

    $article->data_text = \preg_replace(
      "/((\+\d+ )?трейнер( \(для версии .*\))?\.\n)?Распакуйте все файлы из архива\.\nЗапустите трейнер\.\nЗапустите игру, не закрывая трейнер\.\nВо время игры нажимайте на клавиши, указанные в трейнере\.(\n\nОпции:\n\n)?/mu",
      '',
      $article->data_text,
    );

    $viewParams = \compact('article', 'gameTitle', 'optionsCount', 'gameVersion');

    return $this->render('editor', $viewParams);
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trainers_manage'], methods: ['POST', 'GET'])]
  public function actionPublish(string $id): Response
  {
    $article = Article::findOne($id);
    if (!$article) {
      throw new NotFoundHttpException('Страница не найдена');
    }
    try {
      $article->publish();
      $article->notify();
    } catch (Exception $e) {
      Yii::$app->session->setFlash('error', $e->getMessage());
    }

    return $this->redirect('/admin/trainers/ready-to-publish');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trainers_manage'], methods: ['POST', 'GET'])]
  public function actionUnpublish(string $id): Response
  {
    $article = Article::findOne($id);
    if (!$article) {
      throw new NotFoundHttpException('страница не найдена');
    }
    $article->setScenario(Article::SCENARIO_TRAINER);
    $article->ready_to_publish = 0;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/trainers/drafts');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trainers_create'], methods: ['POST', 'GET'])]
  public function actionToReview(string $id): Response
  {
    $article = Article::findOne($id);
    if (!$article) {
      throw new NotFoundHttpException('Страница не найдена');
    }
    $article->setScenario(Article::SCENARIO_TRAINER);
    $article->ready_to_publish = 1;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/trainers/ready-to-publish');
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @throws NotFoundHttpException
   * @throws ForbiddenHttpException
   */
  #[Access(permissions: ['trainers_create'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Трейнер не найден');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя удалить чужой тренер');
    }

    if ($article->data_active || $article->ready_to_publish) {
      Yii::$app->session->setFlash('error', 'Нельзя удалить статью не являющуюся черновиком');
      return $this->redirect('/admin/trainers');
    }

    $article->delete();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/trainers/drafts');
  }
}
