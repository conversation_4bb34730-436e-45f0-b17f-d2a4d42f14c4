<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use frontend\traits\Access;
use common\models\StaticPage;
use frontend\components\View;
use yii\data\ActiveDataProvider;
use yii\base\InvalidConfigException;
use frontend\modules\admin\AdminController;
use yii\web\{Request, Response, HttpException, NotFoundHttpException};

/**
 * @property-read View $view
 * @property-read Request $request
 *
 * @noinspection PhpUnused
 */
class StaticPagesController extends AdminController
{
  /**
   * @throws Throwable
   * @throws InvalidConfigException
   */
  #[Access(permissions: ['static_page_edit'], methods: ['GET'])]
  public function actionIndex(): Response|string
  {
    $this->view->title = 'Статические страницы ⋅ Админка';

    $query = StaticPage::find()
      ->orderBy(['title' => \SORT_ASC]);

    $dataProvider = new ActiveDataProvider(\compact('query'));

    return $this->render('index', \compact('dataProvider'));
  }

  /**
   * @throws InvalidConfigException
   * @throws HttpException
   * @throws Throwable
   */
  #[Access(permissions: ['static_page_edit'], methods: ['GET'])]
  public function actionEdit(int $id): Response|string
  {
    $pageInfo = StaticPage::findOne($id);

    if (empty($pageInfo)) {
      throw new NotFoundHttpException('Статическая страница не найдена');
    }

    $this->view->title = 'Редактирование страницы ⋅ Админка';

    return $this->render('editor', \compact('pageInfo'));
  }

  /**
   * @throws InvalidConfigException
   * @throws HttpException
   * @throws Throwable
   */
  #[Access(permissions: ['static_page_edit'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    $data = $this->request->post('StaticPage');
    if (\is_numeric($data['id'])) {
      $pageInfo = StaticPage::findOne($data['id']);
    } else {
      $pageInfo = new StaticPage();
    }

    if (empty($pageInfo)) {
      throw new NotFoundHttpException('Нельзя изменить несуществующую страницу');
    }

    $pageInfo->load($data, '');

    if (!$pageInfo->save()) {
      $this->view->title = 'Редактирование страницы ⋅ Админка';
      return $this->render('editor', \compact('pageInfo'));
    }

    return $this->redirect('/admin/static-pages');
  }
}
