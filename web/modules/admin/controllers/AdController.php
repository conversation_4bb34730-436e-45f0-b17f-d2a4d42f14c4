<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Throwable;
use yii\web\Request;
use yii\helpers\Json;
use common\models\{Banner, Config};
use frontend\traits\Access;
use frontend\modules\admin\AdminController;
use yii\base\{Model, InvalidConfigException};

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class AdController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access_rbac'])]
  public function actionIndex(): string
  {
    $config = Config::findOne(['config_name' => 'googleAdsBannedPages']);

    if (!$config) {
      throw new InvalidConfigException('Поле конфигурации googleAdsBannedPages не существует!');
    }

    if ($this->request->isPost) {
      $value = $this->request->post('pages');
      $value = \explode("\n", $value);
      $value = \array_map(static fn($string) => \trim($string, "\r"), $value);
      $value = Json::encode($value);
      $config->config_value = $value;
      $config->save();
      Yii::$app->session->setFlash('success', 'Изменения сохранены');
    }

    $urls = Json::decode($config->config_value ?? '[]');

    return $this->render('adsense', \compact('urls'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'])]
  public function actionBanners(): string
  {
    $banners = Banner::find()->all();

    /** @noinspection NotOptimalIfConditionsInspection */
    if ($this->request->isPost
      && Model::loadMultiple($banners, $this->request->post())
      && Model::validateMultiple($banners)
    ) {
      foreach ($banners as $banner) {
        $banner->save();
      }
      Yii::$app->session->addFlash('success', 'Баннеры обновлены');
    }

    return $this->render('banners', \compact('banners'));
  }
}
