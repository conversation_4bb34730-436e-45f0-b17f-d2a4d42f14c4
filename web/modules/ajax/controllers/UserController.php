<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use Throwable;
use JsonException;
use BaconQrCode\Writer;
use common\widgets\SVG;
use ReflectionException;
use common\models\{Game,
  User,
  Blog,
  Topic,
  Article,
  NewsData,
  GameTitle,
  UserReview,
  UserSocial,
  GameRequest,
  Compilation,
  UserActivity,
  UserValidate,
  UserMailChange,
  BlogSubscription
};
use common\jobs\SendMail;
use frontend\traits\Access;
use frontend\components\View;
use frontend\widgets\CssModule;
use common\helpers\{SteamHelper,
  SgHelper,
  ImageHelper,
  SgImageHelper,
  SvgQr<PERSON>enderer,
  SgDateTimeHelper,
  NotificationsHelper
};
use PragmaRX\Google2FA\Google2FA;
use common\enums\UserActivityType;
use yii\validators\EmailValidator;
use frontend\modules\ajax\ApiController;
use yii\helpers\{<PERSON><PERSON>, Html, ArrayHelper};
use common\exceptions\ControlFlowException;
use yii\db\{Expression, StaleObjectException};
use common\components\{ModelLoader, EmailMessage};
use yii\base\{InvalidConfigException, ErrorException};
use frontend\components\ActivityFeed\{ActivityFeedRendererFactory, ActivityFeedTransformerFactory};
use yii\web\{Response, BadRequestHttpException, UploadedFile, NotFoundHttpException, ForbiddenHttpException};

/**
 * API для пользователя
 * @property-read View $view
 * @noinspection PhpUnused
 */
class UserController extends ApiController
{
  /**
   * @return true[]
   * @noinspection PhpUnused
   * @noinspection PhpMethodMayBeStaticInspection
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionHideFriendsBlock(): array
  {
    Yii::$app->user->settings->hide_friends_statuses_on_game_page = 'true';
    return ['success' => true];
  }

  /**
   * @return mixed[]
   * @noinspection PhpUnused
   * @throws ForbiddenHttpException
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionSetTopGames(): array
  {
    $post = $this->request->post();
    if (!$post['games']) {
      $post['games'] = [];
    }
    $user = Yii::$app->user->identity;
    if (!$user) {
      throw new ForbiddenHttpException('Ошибка авторизации');
    }
    $user->settings->set('topGames', Json::encode($post['games']));
    return ['status' => 'success'];
  }

  /**
   * @return mixed[]
   * @throws Throwable
   * @throws StaleObjectException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionSubscribe(): array
  {
    $userId = $this->request->post('userId');
    $currentUser = Yii::$app->user;
    $user = User::findOne($userId);
    if (!$user) {
      return ['success' => false, 'message' => 'Пользователь не найден'];
    }

    /** @var ?Blog $personalBlog */
    $personalBlog = $user->getOwnedBlogs()
      ->where(['blog_type' => Blog::TYPE_PERSONAL])
      ->one();

    if (!$personalBlog) {
      $personalBlog = new Blog([
        'blog_type' => Blog::TYPE_PERSONAL,
        'blog_title' => "Блог $user->userName",
        'blog_avatar' => $user->getAvatar(48),
        'user_owner_id' => $user->id,
        'blog_description' => "Персональный блог пользователя $user->userName",
        'blog_limit_rating_topic' => -1000,
        'blog_closed' => 0,
        'blog_company' => 0,
      ]);
      $personalBlog->save();
    }

    $subscription = BlogSubscription::findOne([
      'blog_id' => $personalBlog->blog_id,
      'user_id' => $currentUser->id,
    ]);

    if ($subscription) {
      if (!$subscription->delete()) {
        return ['success' => false, 'message' => 'Подписка не была отменена'];
      }
      return [
        'success' => true,
        'message' => 'Подписка отменена',
        'icon' => SVG::ai('sg-new/user-plus'),
        'total' => BlogSubscription::find()
          ->where(['blog_id' => $personalBlog->blog_id])
          ->count(),
      ];
    }

    $subscription = new BlogSubscription([
      'blog_id' => $personalBlog->blog_id,
      'user_id' => $currentUser->id,
    ]);

    if (!$subscription->save()) {
      return ['success' => false, 'message' => 'Подписка не была оформлена'];
    }

    return [
      'success' => true,
      'message' => 'Подписка оформлена',
      'icon' => SVG::ai('sg-new/user-ok'),
      'total' => BlogSubscription::find()
        ->where(['blog_id' => $personalBlog->blog_id])
        ->count(),
    ];
  }

  /**
   * @return mixed[]
   * @noinspection PhpUnused
   * @throws NotFoundHttpException|\yii\db\Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionSetAvatar(): array
  {
    /** @var ?User $user */
    $user = Yii::$app->user->identity;
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    try {
      $newAvatar = $this->uploadImage($user, 'user_avatar', 500, 500);
      $user->user_avatar = $newAvatar;
    } catch (Exception $e) {
      return ['status' => 'error', 'message' => $e->getMessage()];
    }

    if (!$user->hasErrors() && $user->save()) {
      return ['status' => 'success'];
    }

    return ['status' => 'error', 'errors' => $user->getErrors()];
  }

  /**
   * @return mixed[]
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionSetPoster(): array
  {
    /** @var ?User $user */
    $user = Yii::$app->user->identity;
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    try {
      $newPoster = $this->uploadImage($user, 'user_poster', 1280, 198);
      $user->user_poster = $newPoster;
    } catch (Exception $e) {
      return ['status' => 'error', 'message' => $e->getMessage()];
    }

    if (!$user->hasErrors() && $user->save()) {
      return ['status' => 'success'];
    }

    return ['status' => 'error', 'errors' => $user->getErrors()];
  }

  /**
   * @throws ControlFlowException
   * @throws ErrorException
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  protected function uploadImage(User $user, string $attr, int $width, int $height): string
  {
    $file = UploadedFile::getInstance($user, $attr);

    if (!$file) {
      // Ничего не делаем, скипаем дальше
      throw new ControlFlowException('Изображение не загружено');
    }

    if (!\in_array($file->extension, ['jpg', 'jpeg', 'png', 'gif'])) {
      throw new ErrorException('Неверный формат изображения. Поддерживаемые форматы: JPG, PNG, GIF');
    }

    if ($file->size > 1048576) { // 1000kb
      throw new ErrorException('Изображение слишком большое. Максимальный размер — 1000kb');
    }

    ImageHelper::open($file->tempName)
      ->resize($width, $height)
      ->save($file->tempName);

    $paths = SgHelper::getUploadPaths('avatars');
    $file->saveAs($paths['path']);
    return $paths['url'];
  }

  /**
   * @return mixed[]
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionSetSetting(): array
  {
    $post = $this->request->post();
    $userId = Yii::$app->user->id;
    if (isset($post['userId'])) {
      if (($post['userId'] !== $userId) && !Yii::$app->user->can('admin_access_rbac')) {
        return ['status' => 'error', 'message' => 'Недостаточно прав'];
      }
      $userId = $post['userId'];
    }
    $user = User::findOne($userId);
    if (!$user) {
      return ['status' => 'error', 'message' => 'Пользователь не найден'];
    }
    $user->settings->set($post['name'], $post['value']);
    return ['status' => 'success'];
  }

  /**
   * @return array|string[]
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionGetSetting(): array
  {
    $post = $this->request->post();
    $userId = Yii::$app->user->id;
    if (isset($post['userId'])) {
      if (($post['userId'] !== $userId) && !Yii::$app->user->can('admin_access_rbac')) {
        return ['status' => 'error', 'message' => 'Недостаточно прав'];
      }
      $userId = $post['userId'];
    }
    $user = User::findOne($userId);
    if (!$user) {
      return ['status' => 'error', 'message' => 'Пользователь не найден'];
    }
    return ['value' => $user->settings->get($post['name'])];
  }

  /**
   * @return mixed[]
   * @noinspection PhpUnused
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException|InvalidConfigException
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionGetNotificationSettings(): array
  {
    /** @var ?User $user */
    $user = Yii::$app->user->identity;
    $post = $this->request->post();

    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    if ($post['userId']) {
      if (($post['userId'] !== $user->id) && !Yii::$app->user->can('admin_access_rbac')) {
        throw new ForbiddenHttpException('Недостаточно прав');
      }
      $user = User::findOne($post['userId']);
    }

    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $settings = $user->settings->getAllItems();
    $result = [];

    foreach ($settings as $settingName => $settingValue) {
      if (\is_string($settingName)
        && (($settingName === 'digest_period') || str_starts_with($settingName, 'notify_'))
      ) {
        $result[$settingName] = $settingValue;
      }
    }

    $result['has_news'] = NewsData::find()->where(['user_id' => $user->id])->exists();
    $result['has_materials'] = Article::find()
      ->where(['user_id' => $user->id])
      ->andWhere(['NOT', ['data_type' => ['live', 'cheats', 'trainers', 'trailers']]])
      ->exists();
    $result['has_streams'] = Article::find()->where(['user_id' => $user->id, 'data_type' => 'live'])->exists();
    $result['has_cheats'] = Article::find()->where(['user_id' => $user->id, 'data_type' => ['cheats', 'trainers']],
    )->exists();
    $result['has_trailers'] = Article::find()->where(['user_id' => $user->id, 'data_type' => 'trailers'])->exists();

    return $result;
  }

  /**
   * @return mixed[]
   * @noinspection PhpUnused
   * @throws ForbiddenHttpException
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function action2faEnableInfo(): array
  {
    $user = Yii::$app->user->identity;

    if (!$user) {
      throw new ForbiddenHttpException('Ошибка авторизации');
    }

    $accountName = $user->userName;
    $secret = $user->mfaSecret;
    $googleQr = new Google2FA();
    $qrUrl = $googleQr->getQRCodeUrl('StopGame.ru', $accountName, $secret);

    return [
      'qr' => $this->generate2faQR($qrUrl),
      'url' => $qrUrl,
    ];
  }

  /**
   * @return mixed[]
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function action2faEnable(): array
  {
    $user = Yii::$app->user->identity;

    if (!$user) {
      throw new ForbiddenHttpException('Ошибка авторизации');
    }

    $secret = $user->mfaSecret ?? '';
    $otp = $this->request->post('otp');
    $valid = Yii::$app->twoFa->checkCode($secret, $otp);
    if (!$valid) {
      return ['success' => false];
    }
    $user->activateMfa();
    return ['success' => true];
  }

  /**
   * @return mixed[]
   * @noinspection PhpUnused
   * @throws ForbiddenHttpException
   * @throws \yii\db\Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function action2faDisable(): array
  {
    $user = Yii::$app->user->identity;
    if (!$user) {
      throw new ForbiddenHttpException('Ошибка авторизации');
    }
    $secret = $user->mfaSecret ?? '';
    $otp = $this->request->post('otp');
    $valid = Yii::$app->twoFa->checkCode($secret, $otp);
    if (!$valid) {
      return ['success' => false];
    }
    $user->disableMfa();
    return ['success' => true];
  }

  public function generate2faQR(string $url): string
  {
    $renderer = new SvgQrRenderer();
    $renderer->setMargin(0);
    $renderer->setWidth(250);
    $renderer->setHeight(250);

    return new Writer($renderer)->writeString($url, 'utf-8');
  }

  /**
   * @return array{
   *     friends: array<array{
   *         id: int,
   *         user_name: string,
   *         show_name: string,
   *         user_avatar: string,
   *         team_name: string,
   *         team_ex: bool,
   *         status?: int
   *     }>
   * }
   * @noinspection PhpUnused
   * @throws InvalidConfigException
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionFriends(): array
  {
    $user = Yii::$app->user->identity;

    $select = [
      'itaf_user.id',
      'itaf_user.user_name',
      'itaf_user.show_name',
      'itaf_user.user_avatar',
      'user_team_info.team_name',
      'team_ex' => 'user_team_info.ex',
    ];
    $friendsQuery = BlogSubscription::find()
      ->joinWith(['blog', 'blog.userOwner', 'blog.userOwner.teamInfo'])
      ->where(['blog_subscription.user_id' => $user->id])
      ->andWhere(['NOT', ['itaf_user.id' => Yii::$app->params['robot_user_id']]]);

    if ($this->request->get('gameId')) {
      $friendsQuery->leftJoin(
        'track',
        'track.user_id = itaf_user.id AND track.gameId = :gameId',
        [':gameId' => $this->request->get('gameId')],
      );
      $select[] = 'track.status';
    }

    /**
     * @var array<array{
     *     id: int,
     *     user_name: string,
     *     show_name: string,
     *     user_avatar: string,
     *     team_name: string,
     *     team_ex: bool,
     *     status?: int
     * }> $friends
     */
    $friends = $friendsQuery
      ->select($select)
      ->asArray()
      ->all();

    return \compact('friends');
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionInfo(int $id): string
  {
    $user = User::findOne($id);
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $this->format = Response::FORMAT_HTML;

    return $this->renderAjax('info', \compact('user'));
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionWarns(int $id): string
  {
    $user = User::findOne($id);
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $this->format = Response::FORMAT_HTML;

    $warns = $user->warns;

    return $this->renderAjax('warns', \compact('warns', 'user'));
  }

  /**
   * @return array{
   *     head: string|false,
   *     content: string,
   *     offset: int,
   *     hasMore: bool
   * }
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionActivities(int $id, string $type = 'all', int $offset = 0, int $limit = 10): array
  {
    [$html, $hasMore] = self::activityFeed($id, $type, $offset);

    \ob_start();
    $this->view->head();
    $this->view->endPage();
    $head = \ob_get_clean();

    \ob_start();
    SVG::renderSprite();
    $html .= \ob_get_clean();

    return [
      'head' => $head,
      'content' => $html,
      'offset' => $offset + $limit,
      'hasMore' => $hasMore,
    ];
  }

  /**
   * @return array{string, bool}
   * @throws Throwable
   */
  public static function activityFeed(int $userId, string $type = 'all', int $offset = 0, int $pageSize = 10): array
  {
    $user = User::findOne($userId);
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $commonCss = CssModule::register('v9/common');
    CssModule::register('v9/pages/user-profile');

    $rateActivityName = UserActivityType::RATED_GAME->value;
    $nonGroupableTypes = [
      UserActivityType::NEW_REVIEW->value,
      UserActivityType::NEW_BLOG_POST->value,
    ];
    $groupByCompilation = [
      UserActivityType::ADD_GAME_TO_COMPILATION->value,
    ];

    $nonGroupableTypes = \implode(',', \array_map(static fn($item) => "'$item'", $nonGroupableTypes));
    $groupByCompilation = \implode(',', \array_map(static fn($item) => "'$item'", $groupByCompilation));

    $activityQuery = UserActivity::find()
      ->select([
        'type' => 'action',
        'date' => new Expression('FROM_UNIXTIME(MAX(user_activity_feed.created_at))'),
        'real_date' => new Expression('FROM_UNIXTIME(MAX(user_activity_feed.created_at))'),
        'models' => new Expression(
          "JSON_MERGE_PRESERVE(
                        JSON_ARRAY(
                            JSON_OBJECT('class', :userClass4, 'id', user_id)
                        ),
                        IF (game_id IS NOT NULL, JSON_ARRAYAGG(
                            JSON_OBJECT('class', :gameClass4, 'id', game_id)
                        ), JSON_ARRAY()),
                        IF (JSON_VALUE(payload, '\$.compilationId') IS NOT NULL, JSON_OBJECT('class', :compilationClass4, 'id', JSON_VALUE(payload, '\$.compilationId')), JSON_ARRAY()),
                        IF (JSON_VALUE(payload, '\$.reviewId') IS NOT NULL, JSON_OBJECT('class', :reviewClass4, 'id', JSON_VALUE(payload, '\$.reviewId')), JSON_ARRAY()),
                        IF (JSON_VALUE(payload, '\$.topicId') IS NOT NULL, JSON_OBJECT('class', :topicClass4, 'id', JSON_VALUE(payload, '\$.topicId')), JSON_ARRAY())
                    )",
          [
            ':userClass4' => User::class,
            ':gameClass4' => Game::class,
            ':compilationClass4' => Compilation::class,
            ':reviewClass4' => UserReview::class,
            ':topicClass4' => Topic::class,
          ],
        ),
        'group_type' => new Expression(
          "IF(action IN ($nonGroupableTypes), concat(action, '_', user_activity_feed.id), IF(action IN ($groupByCompilation), concat(action, '_', JSON_VALUE(payload, '\$.compilationId'), '_', date), concat(action, '_', user_activity_feed.user_id, '_', date)))",
        ),
        'payload' => new Expression(
          "IF (action = '$rateActivityName', JSON_ARRAYAGG(
                        JSON_OBJECT('game_id', game_id, 'rating', JSON_VALUE(payload, '\$.rating'))
                    ), payload)",
        ),
      ])
      ->joinWith('game')
      ->where([
        'user_id' => $user->id,
      ])
      ->andWhere([
        'OR',
        ['user_activity_feed.game_id' => null],
        ['NOT', ['GameId' => null]],
      ])
      ->orderBy([
        'user_activity_feed.created_at' => \SORT_DESC,
      ])
      ->groupBy('group_type');

    if ($type === 'games') {
      $activityQuery->andWhere([
        'action' => [
          UserActivityType::ADD_TO_WISHLIST->value,
          UserActivityType::ADD_TO_PLAYING->value,
          UserActivityType::ADD_TO_BEATEN->value,
          UserActivityType::ADD_TO_TRASHED->value,
          UserActivityType::RATED_GAME->value,
          UserActivityType::LIKED_GAME->value,
          UserActivityType::ADD_GAME_TO_COMPILATION->value,
        ],
      ]);
    } elseif ($type === 'materials') {
      $activityQuery->andWhere([
        'action' => [
          UserActivityType::NEW_REVIEW->value,
          UserActivityType::NEW_BLOG_POST->value,
        ],
      ]);
    }

    $count = (clone $activityQuery)->count();

    if ($offset > 0) {
      $activityQuery->offset($offset);
    }

    $grouppedActivities = $activityQuery
      ->asArray()
      ->limit($pageSize)
      ->all();

    $hasMore = $count > $offset + $pageSize;

    /** @noinspection AlterInForeachInspection */
    foreach ($grouppedActivities as &$item) {
      $item['models'] = Json::decode($item['models']);
      $item['payload'] = Json::decode($item['payload']);
      $item['date'] = SgDateTimeHelper::convertDbToPHP($item['date']);
      if ($item['real_date']) {
        $item['real_date'] = SgDateTimeHelper::convertDbToPHP($item['real_date']);
      }
      $gamesCount = 0;
      $newModels = [];
      /** @noinspection AlterInForeachInspection */
      foreach ($item['models'] as &$model) {
        if (\is_string($model)) {
          $model = Json::decode($model);
        }

        if ($model['class'] === Game::class) {
          if (empty($item['games_count'])) {
            $itemGames = \array_filter($item['models'], static fn($model2) => $model2['class'] === Game::class);
            /** @noinspection PhpConditionAlreadyCheckedInspection */
            $item['games_count'] = \count($itemGames ?? []);
          }
          $gamesCount++;
          if ($gamesCount > 100) {
            continue;
          }
        }

        ModelLoader::push($model['class'], $model['id']);
        $newModels[] = $model;
      }
      $item['models'] = $newModels;
    }

    ModelLoader::populateAll();

    $html = '';

    foreach ($grouppedActivities as $feedItem) {
      $itemInfo = ActivityFeedTransformerFactory::create($feedItem['type'])->transform($feedItem);
      $itemHtml = ActivityFeedRendererFactory::create($feedItem['type'])->render($itemInfo, true);
      if (!empty($itemHtml)) {
        $html .= $itemHtml;
        $hrStyle = 'margin: 0;';
        $html .= Html::tag('hr', '', [
          'data-key' => $feedItem['group_type'] . '_hr',
          'class' => $commonCss['separator'],
          'style' => $hrStyle,
        ]);
      }
    }
    return [$html, $hasMore];
  }

  /**
   * @return array{
   *     status: string,
   *     message?: string
   * }
   * @throws \yii\base\Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionChangeMail(): array
  {
    $email = $this->request->post('user_mail');
    $password = $this->request->post('password');
    $user = Yii::$app->user->identity;

    if (empty($email)) {
      return ['status' => 'emailError', 'message' => 'Почта не может быть пустой'];
    }

    if (empty($password)) {
      return ['status' => 'passwordError', 'message' => 'Пароль не может быть пустым'];
    }

    $password = \md5($password);

    if ($password !== $user->user_pass) {
      return ['status' => 'passwordError', 'message' => 'Неверный пароль'];
    }

    if (!new EmailValidator()->validate($email)) {
      return ['status' => 'emailError', 'message' => 'Неверный формат email'];
    }

    $mailExists = User::find()->where(['user_mail' => $email])->exists();
    $mailExistsUnverified = UserValidate::find()->where(['user_mail' => $email])->exists();
    $mailExistsUnverified2 = UserMailChange::find()->where(['new_email' => $email])->exists();

    if ($mailExists
      || $mailExistsUnverified
      || $mailExistsUnverified2
    ) {
      return ['status' => 'emailError', 'message' => 'Эта почта уже занята'];
    }

    $token = Yii::$app->security->generateRandomString();

    /** @var EmailMessage $mail */
    $mail = Yii::$app->mailer->compose('change-mail', \compact('user', 'token'));
    Yii::$app->queue->push(
      new SendMail([
        'mail' => [
          'to' => \trim($email),
          'subject' => 'Смена почты на StopGame.ru',
          'textBody' => $mail->getTextBody() ?? '',
          'htmlBody' => $mail->getHtmlBody() ?? '',
        ],
      ])
    );

    $model = new UserMailChange([
      'user_id' => Yii::$app->user->id,
      'new_email' => $email,
      'token' => $token,
    ]);
    $model->save();

    return ['status' => 'ok'];
  }

  /**
   * @return array{
   *     status: string,
   *     message?: string
   * }
   * @noinspection PhpUnused
   * @throws \yii\db\Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionChangePassword(): array
  {
    $pass1 = $this->request->post('user_pass1');
    $pass2 = $this->request->post('user_pass2');

    if (\strlen($pass1) < 5) {
      return ['status' => 'error', 'message' => 'Пароль должен быть не менее 5 символов'];
    }

    if ($pass1 !== $pass2) {
      return ['status' => 'error', 'message' => 'Введённые пароли не совпадают'];
    }

    $user = Yii::$app->user->identity;
    $user->user_pass = \md5($pass1);

    if (!$user->save()) {
      return ['status' => 'error', 'message' => \array_values($user->errors)[0]];
    }

    // Потому что у нас в сессии лежит пароль и когда он меняется, меняется ключ авторизации.
    // Зато решает проблему авторазлогина остальных сессий.
    Yii::$app->user->login($user);
    return ['status' => 'ok'];
  }

  /**
   * @throws InvalidConfigException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST', 'GET'])]
  public function actionReadNotificationsByTarget(string $targetType, int $targetId): string
  {
    $notifications = NotificationsHelper::byTarget($targetType, $targetId);
    foreach ($notifications as $notification) {
      $notification->read();
    }
    return 'ok';
  }

  /**
   * @noinspection PhpMethodMayBeStaticInspection
   * @noinspection PhpUnused
   * @throws InvalidConfigException
   * @throws JsonException
   * @throws ReflectionException
   * @throws BadRequestHttpException
   * @throws Exception
   * @throws Throwable
   */
  #[Access(requiresAuth: true, methods: ['POST', 'GET'])]
  public function actionSteamImport(): void
  {
    /** @var UserSocial[] $accounts */
    $accounts = UserSocial::find()
      ->where(['user_id' => Yii::$app->user->id, 'network' => 'steam'])
      ->all();

    if (empty($accounts)) {
      throw new BadRequestHttpException('Не указан Steam ID!');
    }

    $steamGames = [];
    $user = Yii::$app->user->identity;

    foreach ($accounts as $account) {
      echo "message:::Смотрим библиотеку;;;\n";
      SgHelper::doFlush();

      $info = SteamHelper::requestSteamApi('IPlayerService/GetOwnedGames/v0001', [
        'steamid' => $account->uid,
        'format' => 'json',
        'include_appinfo' => 'true',
        'include_played_free_games' => 'true'
      ]);

      $steamGames = ArrayHelper::merge($steamGames, $info['response']['games'] ?? []);

      echo "message:::Проверяем твой вишлист;;;\n";
      SgHelper::doFlush();

      $info = SteamHelper::requestSteamApi('IWishlistService/GetWishlist/v1', [
        'steamid' => $account->uid,
        'format' => 'json'
      ]);

      $items = $info['response']['items'] ?? [];
      $items = \array_map(static function ($item) {
        $item['from_wishlist'] = true;
        $item['playtime_forever'] = 0;
        $meiliSearchResult = Yii::$app->meili->index('steam')->search('', [
          'limit' => 1,
          'filter' => 'appid = ' . $item['appid']
        ]);
        $meiliItem = $meiliSearchResult->getHit(0);
        if (!$meiliItem || !isset($meiliItem['name'])) {
          return null;
        }
        $item['name'] = $meiliItem['name'];
        return $item;
      }, $items);
      $items = \array_filter($items, static fn($item) => $item !== null);
      $steamGames = ArrayHelper::merge($steamGames, $items);

      echo "message:::Догружаем DLC;;;\n";
      SgHelper::doFlush();

      try {
        $info = SteamHelper::requestSteamApi('ISteamUser/GetPlayerSummaries/v2', [
          'steamids' => $account->uid,
          'format' => 'json'
        ]);

        $profileUrl = $info['response']['players'][0]['profileurl'];

        $slash = !str_ends_with($profileUrl, '/') ? '/' : '';

        $url = "$profileUrl{$slash}games?tab=all&xml=1";
        $content = \file_get_contents($url);
        if (!$content) {
          continue;
        }
        $xml = \simplexml_load_string($content);
        if (!$xml) {
          continue;
        }
      } catch (Throwable) {
        continue;
      }
      /** @noinspection ProperNullCoalescingOperatorUsageInspection */
      $games = $xml->games->game ?? [];

      foreach ($games as $game) {
        if (\in_array((int)$game->appID, ArrayHelper::getColumn($steamGames, 'appid'))) {
          continue;
        }
        $steamGames[] = [
          'appid' => (int)$game->appID,
          'name' => (string)$game->name,
          'playtime_forever' => $game->hoursOnRecord * 60,
          'from_wishlist' => false,
        ];
      }
    }

    $steamIds = ArrayHelper::getColumn($steamGames, 'appid');
    $steamIds = \array_unique($steamIds);

    /** @var Game[] $games */
    $games = Game::find()
      ->joinWith('steam')
      ->where(['store_id' => $steamIds])
      ->all();
    /** @var array<int, Game[]> $groupedGames */
    $groupedGames = [];

    foreach ($games as $game) {
      foreach ($game->steam as $steamGame) {
        $groupedGames[$steamGame->store_id][] = $game;
      }
    }
    unset($games);

    echo 'userName:::' . $user->user_name . ';;;';
    echo 'total:::' . \count($steamGames) . ';;;';
    SgHelper::doFlush();

    foreach ($steamGames as $steamGameInfo) {
      if (isset($groupedGames[$steamGameInfo['appid']])) {
        $games = $groupedGames[$steamGameInfo['appid']];
        $isTracked = $user->getTrackedGames()
            ->andWhere(['GameId' => ArrayHelper::getColumn($games, 'GameId')])
            ->count() === \count($games);

        if (!$isTracked) {
          $this->sendGameInfo('needs_track', $steamGameInfo, $games);
        } else {
          echo "already_tracked:::{$steamGameInfo['appid']};;;";
        }
      } else {
        $name = SgHelper::fixThe($steamGameInfo['name']);
        /** @var ?GameTitle $title */
        $title = GameTitle::find()
          ->where(['like', 'GameName', $name, false])
          ->one();

        if (!$title || !$title->info) {
          $gameRequestExists = GameRequest::find()
            ->leftJoin([
              'stores' => new Expression(
                "JSON_TABLE(game_requests.stores, '$[*]' COLUMNS (store VARCHAR(255) PATH '$.store', store_id VARCHAR(255) PATH '$.store_id'))"
              ),
            ], '1 = 1')
            ->where(['store' => 'steam', 'store_id' => $steamGameInfo['appid']])
            ->andWhere(['game_id' => null, 'closed_by' => null])
            ->exists();

          if ($gameRequestExists) {
            $this->sendGameInfo('has_request', $steamGameInfo, []);
          } else {
            $this->sendGameInfo('no_requests', $steamGameInfo, []);
          }
        } else {
          $game = $title->info;
          $this->sendGameInfo('found_by_title', $steamGameInfo, [$game]);
        }
      }
      SgHelper::doFlush();
    }
    exit;
  }

  /**
   * @return array{
   *   accounts: UserSocial[]
   * }
   * @throws InvalidConfigException
   * @noinspection PhpMethodMayBeStaticInspection
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionSteamAccounts(): array
  {
    $accounts = UserSocial::find()
      ->where(['user_id' => Yii::$app->user->id, 'network' => 'steam'])
      ->all();

    return \compact('accounts');
  }

  /**
   * @param array{
   *   appid: number,
   *   name: string,
   *   playtime_forever: number,
   *   from_wishlist?: bool
   * } $steamGameInfo
   * @param Game[] $games
   * @throws JsonException
   * @throws ReflectionException
   * @throws Exception
   */
  protected function sendGameInfo(string $status, array $steamGameInfo, array $games): void
  {
    $playtime = \round($steamGameInfo['playtime_forever'] / 60);
    $ourGames = [];
    foreach ($games as $game) {
      $mainTitle = $game->mainTitle;
      \assert(!!$mainTitle);
      $poster = $game->square_poster ?? $mainTitle->poster;
      $poster = SgImageHelper::thumb($poster, 224, 224, 'c', 1, false);
      $ourGames[] = [
        'id' => $game->GameId,
        'poster' => $poster,
        'title' => SgHelper::theFix($mainTitle->GameName),
        'url' => $mainTitle->fullUrl,
        'date' => $game->releaseDate,
        'steam_id' => $steamGameInfo['appid'],
        'status' => $game->currentUserTrack->status ?? -1,
        'gameIsReleased' => $game->isReleased
      ];
    }
    $info = \json_encode([
      'foreign' => [
        'store' => 'steam',
        'foreignId' => $steamGameInfo['appid'],
        'name' => $steamGameInfo['name'],
        'image' => "https://shared.cloudflare.steamstatic.com/store_item_assets/steam/apps/{$steamGameInfo['appid']}/library_600x900.jpg",
        'playtime' => $playtime,
        'url' => "https://store.steampowered.com/app/{$steamGameInfo['appid']}",
        'searchStatus' => $status,
        'from_wishlist' => $steamGameInfo['from_wishlist'] ?? false
      ],
      'ours' => $ourGames,
    ], \JSON_THROW_ON_ERROR);
    echo "$status:::$info;;;";
  }
}
