<?php

namespace frontend\controllers;

use Yii;
use Throwable;
use yii\helpers\Url;
use yii\web\Response;
use yii\db\Exception;
use yii\db\Expression;
use yii\web\Controller;
use common\models\Topic;
use common\models\Claim;
use frontend\traits\Access;
use common\models\ActionLog;
use yii\caching\TagDependency;
use common\models\UserActivity;
use common\helpers\SgTextHelper;
use yii\web\NotFoundHttpException;
use common\enums\UserActivityType;
use yii\base\InvalidConfigException;
use common\notifications\NewTopicNotification;
use common\notifications\WeeklyBlogNotification;
use common\notifications\game_updates\GameTopicNotification;

/**
 * @noinspection PhpUnused
 */

class TopicController extends Controller
{
  public $enableCsrfValidation = false;

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'])]
  public function actionPromote(string $id): Response
  {
    $topic = Topic::findOne($id);

    if (!$topic) {
      throw new NotFoundHttpException('Пост не найден');
    }

    $topic->topic_publish_index = 1;
    /** @noinspection ProperNullCoalescingOperatorUsageInspection */
    $topic->topic_date_on_main = $topic->topic_date_on_main ?? new Expression('NOW()');
    $topic->save();

    ActionLog::log(ActionLog::BLOG_PROMOTE, ['topic_id' => $topic->topic_id]);

    Yii::$app->session->addFlash('success', 'Пост опубликован на главной');

    return $this->redirect($topic->fullUrl);
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'])]
  public function actionAnnounce(int $id): Response
  {
    $topic = Topic::findOne($id);

    if (!$topic) {
      throw new NotFoundHttpException('Пост не найден');
    }

    $topic->announce();

    Yii::$app->session->addFlash('success', 'Пост добавлен в анонсер');

    return $this->redirect($topic->fullUrl);
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['promote_blogs'])]
  public function actionAddToTop(string $id): Response
  {
    $topic = Topic::findOne($id);

    if (!$topic) {
      throw new NotFoundHttpException('Пост не найден');
    }

    $topic->topic_on_main = 1;
    /** @noinspection ProperNullCoalescingOperatorUsageInspection */
    $topic->topic_date_on_main = $topic->topic_date_on_main ?? new Expression('NOW()');
    $topic->topic_date_rewarded = new Expression('NOW()');
    $topic->save();
    $topic->announce();

    new WeeklyBlogNotification(['topicId' => $topic->topic_id])->send();

    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, ["topic_$topic->topic_id", 'topics_on_main']);
      Yii::$app->cache->delete('blog_winners_users');
    }

    ActionLog::log(ActionLog::BLOG_REWARD, ['topic_id' => $topic->topic_id]);

    Yii::$app->session->addFlash('success', 'Пост добавлен в «Блоги недели»');

    return $this->redirect($topic->fullUrl);
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['promote_blogs'])]
  public function actionRemoveFromTop(string $id): Response
  {
    $topic = Topic::findOne($id);

    if (!$topic) {
      throw new NotFoundHttpException('Пост не найден');
    }

    $topic->topic_on_main = 0;
    $topic->topic_date_rewarded = null;
    $topic->save();

    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, "topic_$id");
    }

    Yii::$app->session->addFlash('success', 'Пост удалён из «Блогов недели»');

    return $this->redirect($topic->fullUrl);
  }

  /**
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @throws Exception
   * @return Response|array{success: bool, message?: string}
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionPublish(string $id): Response|array
  {
    $user = Yii::$app->user;

    $topic = Topic::findOne($id);

    $showError = function (string $message, Topic $topic) {
      Yii::$app->session->addFlash('error', 'Пост удалён модератором и не может быть опубликован.');
      return $this->redirect($topic->fullUrl);
    };
    $sucessfullyExit = function (Topic $topic) {
      Yii::$app->session->addFlash('success', 'Пост опубликован');
      return $this->redirect($topic->fullUrl);
    };

    if (!\array_key_exists('text/html', $this->request->acceptableContentTypes)) {
      Yii::$app->response->format = Response::FORMAT_JSON;
      $showError = static fn(string $message, Topic $topic) => ['success' => false, 'message' => $message];
      $sucessfullyExit = static fn(Topic $topic) => ['success' => true];
    }

    if (!$topic || !$topic->topicContent
      || $user->isGuest || !$user->identity->isTopicModer($topic)
    ) {
      throw new NotFoundHttpException('Пост не найден');
    }

    if (($topic->deleted_final === 1) && ($topic->topic_deleted_user_id !== $user->id)) {
      return $showError('Пост удалён модератором и не может быть опубликован.', $topic);
    }

    if ($user->identity->isBanned) {
      return $showError('Ты в бане и не можешь публиковать блоги.', $topic);
    }

    $userRegisteredDate = $user->identity->user_register;
    $userRegisteredDeltaDays = (\time() - \strtotime($userRegisteredDate)) / 86400;

    if ((empty($topic->blog) || ($topic->blog->blog_type !== 'stopgame'))
      && !Yii::$app->user->can('moderate_blogs')
      && (SgTextHelper::testForBanwords($topic->title)
        || SgTextHelper::testForBanwords($topic->topicContent->editor_js_content ?? '')
        || SgTextHelper::testForCyrillic($topic->topicContent->topic_text ?? '')
        || (($userRegisteredDeltaDays < 30) && SgTextHelper::hasLinks($topic->topicContent->editor_js_content ?? ''))
      )
    ) {
      $topic->topic_publish = 0;
      $topic->premoderate = 1;
      $topic->save();
      return $showError('Пост выглядит подозрительно и был отправлен на премодерацию.', $topic);
    }

    $topic->topic_publish = 1;
    $topic->topic_deleted = 0;
    $topic->premoderate = 0;
    $topic->topic_deleted_user_id = null;
    /* Нужно ли отправлять уведомление о новом посте */
    $notificate = false;
    if (empty($topic->topic_date_published)) {
      $topic->topic_date_published = new Expression('NOW()');
      $notificate = true;
    }
    $topic->save();
    $topic->refresh();

    if ($notificate) {
      new NewTopicNotification(['topicId' => $topic->topic_id])->send();

      if (!empty($topic->games)) {
        foreach ($topic->games as $game) {
          new GameTopicNotification([
            'gameId' => $game->GameId,
            'topicId' => $topic->topic_id,
          ])->send();
        }
      }

      UserActivity::register(UserActivityType::NEW_BLOG_POST, null, [
        'topicId' => $topic->topic_id,
      ], $topic->userId);
    }

    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, "topic_$id");
    }

    return $sucessfullyExit($topic);
  }

  /**
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionDraft(string $id): Response
  {
    $user = Yii::$app->user;
    $topic = Topic::findOne($id);

    if (!$topic || $user->isGuest || !$user->identity->isTopicModer($topic)
      || (($topic->topic_deleted === 1)
        && !$user->can('moderate_blogs')
        && ($topic->topic_deleted_user_id !== $user->id))
    ) {
      throw new NotFoundHttpException('Пост не найден');
    }

    $topic->topic_publish = 0;
    $topic->topic_deleted = 0;
    $topic->topic_deleted_user_id = null;
    $topic->save();
    $topic->unnotify();

    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, ["topic_$topic->topic_id", 'topics_on_main']);
    }

    if (($topic->user_id !== $user->id) && $user->can('moderate_blogs')) {
      ActionLog::log(ActionLog::BLOG_DRAFT, ['topic_id' => $topic->topic_id]);
    }

    Yii::$app->session->addFlash('success', 'Пост отправлен в черновики');

    return $this->redirect($topic->fullUrl);
  }

  /**
   * @throws Throwable
   */
  #[Access(requiresAuth: true)]
  public function actionDelete(string $id): Response
  {
    $user = Yii::$app->user;
    $topic = Topic::findOne($id);

    $allow = !!$topic && !$user->isGuest
      && (($user->id === $topic->user_id)
        || (!!$topic->topic_deleted && $user->can('moderate_blogs'))
      );

    if (!$allow) {
      throw new NotFoundHttpException('Пост не найден');
    }

    $topic->unnotify();

    if ($topic->topic_deleted === 1) {
      Claim::deleteAll([
        'target_type' => $topic->targetType,
        'target_id' => $topic->targetId,
      ]);
      $topic->delete();

      if (Yii::$app->cache) {
        TagDependency::invalidate(Yii::$app->cache, ["topic_$topic->topic_id", 'topics_on_main']);
      }

      if (!$topic->user) {
        throw new NotFoundHttpException('Пользователь не найден');
      }

      return $this->redirect(Url::to(['user/blogs', 'userName' => $topic->user->user_name, 'section' => 'deleted']));
    }

    $topic->topic_publish = 0;
    $topic->topic_deleted = 1;
    $topic->topic_deleted_user_id = (int)$user->id;
    $topic->save();

    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, ["topic_$topic->topic_id", 'topics_on_main']);
    }

    Yii::$app->session->addFlash('success', 'Пост перемещён в корзину');

    return $this->redirect($topic->fullUrl);
  }
}
