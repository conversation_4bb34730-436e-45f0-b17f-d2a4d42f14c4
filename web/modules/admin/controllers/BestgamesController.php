<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Throwable;
use yii\web\Request;
use yii\web\Response;
use yii\db\Exception;
use yii\db\Expression;
use common\models\Config;
use frontend\traits\Access;
use common\models\Bestgames;
use frontend\components\View;
use yii\db\StaleObjectException;
use yii\web\NotFoundHttpException;
use common\models\BestgamesNominee;
use yii\base\InvalidConfigException;
use common\models\BestgamesNomination;
use frontend\modules\admin\AdminController;
use common\models\BestgamesNominationNominee;

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class BestgamesController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    return $this->actionNominations();
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionNominations(): string
  {
    $nominations = BestgamesNomination::find()
      ->orderBy(['order' => \SORT_ASC])
      ->all();

    return $this->render('nominations', \compact('nominations'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionNominees(): string
  {
    $nominees = BestgamesNominee::find()
      ->orderBy(['title' => \SORT_ASC])
      ->all();

    return $this->render('nominees', \compact('nominees'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionSettings(): string
  {
    /** @var Config[] $settings */
    $settings = Config::find()
      ->where([
        'config_name' => [
          'bestgames_year',
          'bestgames_start_date',
          'bestgames_end_date',
        ],
      ])
      ->indexBy('config_name')
      ->all();

    if ($this->request->isPost) {
      $post = Yii::$app->request->post();
      foreach ($post as $fieldName => $value) {
        if (!empty($settings[$fieldName])) {
          $settings[$fieldName]->config_value = $value;
          $settings[$fieldName]->save();
        }
      }
    }

    return $this->render('settings', \compact('settings'));
  }


  /**
   * @throws Exception
   * @throws InvalidConfigException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionReset(): Response
  {
    Yii::$app->db->createCommand()->truncateTable(Bestgames::tableName())->execute();
    Yii::$app->db->createCommand()->truncateTable(BestgamesNominationNominee::tableName())->execute();
    Yii::$app->db->createCommand()->truncateTable(BestgamesNominee::tableName())->execute();
    /** @var Config $year */
    $year = Config::find()
      ->where(['config_name' => 'bestgames_year'])
      ->one();
    $year->config_value = (string)((int)$year->config_value + 1);
    $year->save();

    return $this->redirect('/admin/bestgames/settings');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionResult(): string
  {
    $noUsers = false;
    if ($this->request->isPost && $this->request->post('no-users')) {
      $noUsers = true;
    }

    $nominations = BestgamesNomination::find()
      ->with('nominees')
      ->orderBy(['order' => \SORT_ASC])
      ->all();

    /**
     * @var array<array{
     *     cats: string,
     *     nominees: string,
     *     total: int,
     *     names: string
     * }> $ourResults
     */
    $ourResults = Bestgames::find()
      ->select([
        'cats',
        'nominees',
        'total' => new Expression('count(*)'),
        'names' => new Expression("group_concat(show_name separator ', ')"),
      ])
      ->leftJoin('itaf_user', 'itaf_user.id = bestgames.user_id')
      ->leftJoin('user_team_info', 'user_team_info.user_id = bestgames.user_id')
      ->where(['user_team_info.ex' => 0])
      ->groupBy(['cats', 'nominees'])
      ->asArray()
      ->all();

    $ours = [];
    $sgTotal = [];

    foreach ($ourResults as $ourResult) {
      if (!isset($ours[$ourResult['cats']])) {
        $ours[$ourResult['cats']] = [];
      }
      if (!isset($sgTotal[$ourResult['cats']])) {
        $sgTotal[$ourResult['cats']] = 0;
      }

      $ours[$ourResult['cats']][$ourResult['nominees']] = [
        'total' => $ourResult['total'],
        'names' => $ourResult['names'],
      ];
      $sgTotal[$ourResult['cats']] += $ourResult['total'];
    }
    foreach ($ours as $cat => $games) {
      $total = $sgTotal[$cat];
      foreach ($games as $gameKey => $game) {
        $ours[$cat][$gameKey]['percent'] = \number_format((100 * $game['total']) / $total, 1);
      }
      \uasort($ours[$cat], static fn($item1, $item2) => $item2['total'] <=> $item1['total']);
    }

    /**
     * @var array<array{
     *     cats: string,
     *     nominees: string,
     *     total: int
     * }> $allResults
     */
    $allResults = Bestgames::find()
      ->select([
        'cats',
        'nominees',
        'total' => new Expression('count(*)'),
      ])
      ->leftJoin('user_team_info', 'user_team_info.user_id = bestgames.user_id')
      ->where([
        'OR',
        ['user_team_info.user_id' => null],
        ['ex' => 1],
      ])
      ->groupBy(['cats', 'nominees'])
      ->asArray()
      ->all();

    $theirs = [];
    $userTotal = [];

    foreach ($allResults as $result) {
      if (!isset($theirs[$result['cats']])) {
        $theirs[$result['cats']] = [];
      }
      if (!isset($userTotal[$result['cats']])) {
        $userTotal[$result['cats']] = 0;
      }
      $theirs[$result['cats']][$result['nominees']] = [
        'total' => $result['total'],
      ];
      $userTotal[$result['cats']] += $result['total'];
    }
    foreach ($theirs as $cat => $games) {
      $total = $userTotal[$cat];
      foreach ($games as $gameKey => $game) {
        $theirs[$cat][$gameKey]['percent'] = \number_format((100 * $game['total']) / $total, 1);
      }
      \uasort($theirs[$cat], static fn($item1, $item2) => $item2['total'] <=> $item1['total']);
    }

    $this->view->title = 'Итоги года';

    return $this->render('result', \compact('nominations', 'ours', 'theirs', 'sgTotal', 'userTotal', 'noUsers'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionDeleteNominee(int $id): Response
  {
    $nominee = BestgamesNominee::findOne($id);

    if (!$nominee) {
      throw new NotFoundHttpException('Номинант не найден');
    }

    $nominee->delete();

    return $this->redirect('/admin/bestgames/nominees');
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionDeleteNomination(int $id): Response
  {
    $nomination = BestgamesNomination::findOne($id);

    if (!$nomination) {
      throw new NotFoundHttpException('Номинация не найдена');
    }

    $nomination->delete();

    return $this->redirect('/admin/bestgames');
  }
}
