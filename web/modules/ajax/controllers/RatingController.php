<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use DateTime;
use Exception;
use Throwable;
use DateInterval;
use yii\db\Expression;
use common\models\Vote;
use common\models\Topic;
use common\models\Comment;
use frontend\traits\Access;
use common\models\UserReview;
use common\models\Compilation;
use yii\db\StaleObjectException;
use yii\base\InvalidConfigException;
use frontend\modules\ajax\ApiController;

/** @noinspection PhpUnused */

class RatingController extends ApiController
{
  /**
   * @return array{
   *     success: bool,
   *     message: string
   * }
   * @noinspection PhpUnused
   * @throws Exception
   * @throws Throwable
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionVote(): array
  {
    $user = Yii::$app->user;
    $sign = $this->request->post('value');
    $voteValue = 1;
    if ($sign === '-') {
      $voteValue = -1;
    }

    if ($user->identity->isBanned) {
      return ['success' => false, 'message' => 'Твой аккаунт забанен, ты не можешь голосовать.'];
    }

    $targetType = $this->request->post('target_type');
    $targetId = $this->request->post('target_id');

    if (!$targetId || !$sign || !\is_numeric($targetId)) {
      return ['success' => false, 'message' => 'Ошибка при голосовании'];
    }

    return match ($targetType) {
      'comment' => $this->voteComment((int)$targetId, $voteValue),
      'topic' => $this->voteTopic((int)$targetId, $voteValue),
      'user_review' => $this->voteUserReview((int)$targetId, $voteValue),
      'compilation' => $this->voteCompilation((int)$targetId, $voteValue),
      default => ['success' => false, 'message' => 'Неверный тип материала.'],
    };
  }

  /**
   * @return array{
   *     success: bool,
   *     message: string,
   *     value?: float,
   *     voteDirection?: ?string
   * }
   * @throws \yii\db\Exception
   * @throws InvalidConfigException
   */
  public function voteComment(int $commentId, int $voteValue): array
  {
    $user = Yii::$app->user;

    /** @var ?Comment $comment */
    $comment = Comment::findOne($commentId);

    if (!$comment) {
      return ['success' => false, 'message' => 'Невозможно проголосовать за несуществующий комментарий.'];
    }

    if ($comment->isOwner) {
      return ['success' => false, 'message' => 'Нельзя голосовать за свой комментарий.'];
    }

    /** @var ?Vote $vote */
    $vote = Vote::findOne([
      'target_type' => 'comment',
      'target_id' => $commentId,
      'user_voter_id' => Yii::$app->user->id,
    ]);

    if ($vote && ($vote->vote_direction === $voteValue)) {
      $direction = ($voteValue > 0) ? 'плюс' : 'минус';
      return ['success' => false, 'message' => "У этого комментария уже стоит твой $direction."];
    }

    if (!$vote) {
      $vote = new Vote([
        'target_id' => $commentId,
        'comment_id' => $commentId,
        'target_type' => 'comment',
        'user_voter_id' => $user->id,
      ]);
    }

    $shouldIncrementUserRating = false;
    $shouldIncrementVoteCount = true;
    if ($vote->vote_direction === -$voteValue) {
      $shouldIncrementVoteCount = false;
      if ($comment->user) {
        $comment->user->user_count_vote--;
      }
      $message = 'Твоя оценка аннулирована';
      if ($voteValue < 0) {
        $shouldIncrementUserRating = true;
      }
    } else {
      if ($comment->user) {
        $comment->user->user_count_vote++;
      }
      $message = 'Твоя оценка принята';
      if ($voteValue > 0) {
        $shouldIncrementUserRating = true;
      }
    }

    $vote->vote_direction += $voteValue;
    $vote->vote_value += $voteValue;
    $vote->vote_date = new Expression('NOW()');
    if ($shouldIncrementUserRating && $comment->user) {
      $comment->user->user_rating += 0.1 * $voteValue;
    }
    $vote->save();
    if (\strtotime($comment->comment_date) + Yii::$app->params['comments.rating_time'] < \time()) {
      $comment->comment_rating += $voteValue;
      if ($shouldIncrementVoteCount) {
        $comment->comment_count_vote++;
      }
    } else {
      $comment->comment_rating = Vote::find()
        ->where(['target_type' => 'comment', 'target_id' => $commentId])
        ->sum('vote_value');
      $comment->comment_count_vote = (int)Vote::find()
        ->where(['target_type' => 'comment', 'target_id' => $commentId])
        ->count();
    }
    $comment->save();
    $comment->user?->save();

    $voteDirection = null;

    if ($vote->vote_direction > 0) {
      $voteDirection = '+';
    } elseif ($vote->vote_direction < 0) {
      $voteDirection = '-';
    }

    return [
      'success' => true,
      'value' => $comment->comment_rating,
      'voteDirection' => $voteDirection,
      'message' => $message,
    ];
  }

  /**
   * @return array{
   *     success: bool,
   *     message: string,
   *     value?: float,
   *     voteDirection?: ?string
   * }
   * @throws Exception
   */
  public function voteTopic(int $topicId, int $voteValue): array
  {
    $user = Yii::$app->user;
    $params = Yii::$app->params;

    $topic = Topic::findOne($topicId);

    if (!$topic) {
      return ['success' => false, 'message' => 'Пост не найден.'];
    }

    if ($topic->userId === $user->id) {
      return ['success' => false, 'message' => 'Ты не можешь голосовать за свои посты.'];
    }

    $vote = Vote::findOne([
      'target_type' => 'topic',
      'target_id' => $topicId,
      'user_voter_id' => Yii::$app->user->id,
    ]);

    if ($vote && ($vote->vote_direction === $voteValue)) {
      $direction = ($voteValue > 0) ? 'плюс' : 'минус';
      return ['success' => false, 'message' => "У этого поста уже стоит твой $direction."];
    }

    if (!$vote) {
      $vote = new Vote([
        'target_id' => $topicId,
        'comment_id' => null,
        'target_type' => 'topic',
        'user_voter_id' => $user->id,
      ]);
    }

    $shouldIncrementUserRating = false;
    if ($vote->vote_direction === -$voteValue) {
      if ($topic->user) {
        $topic->user->user_count_vote--;
      }
      $message = 'Твоя оценка аннулирована';
      if ($voteValue < 0) {
        $shouldIncrementUserRating = true;
      }
    } else {
      if ($topic->user) {
        $topic->user->user_count_vote++;
      }
      $message = 'Твоя оценка принята';
      if ($voteValue > 0) {
        $shouldIncrementUserRating = true;
      }
    }

    $vote->vote_direction += $voteValue;
    $vote->vote_value += $voteValue;
    $vote->vote_date = new Expression('NOW()');
    $vote->save();
    if ($topic->user && $shouldIncrementUserRating) {
      $topic->user->user_rating += 0.1 * $voteValue;
    }
    $topic->topic_rating = Vote::find()
      ->where(['target_type' => 'topic', 'target_id' => $topicId])
      ->sum('vote_value');
    $topic->topic_count_vote = (int)Vote::find()
      ->where(['target_type' => 'topic', 'target_id' => $topicId])
      ->count();

    $now = new DateTime();
    $publishedDatePlusWeek = !empty($topic->topic_date_published) ? new DateTime(
      $topic->topic_date_published,
    ) : new DateTime();
    $publishedDatePlusWeek = $publishedDatePlusWeek->add(DateInterval::createFromDateString('+1 week'));

    if (($topic->topic_rating >= $params['blogs.rating_for_main']) &&
      (($topic->topic_date_on_main === $topic->topic_date_published) || ($topic->topic_date_on_main === $topic->topic_date_add) || ($topic->topic_date_on_main === null))
      && ($now <= $publishedDatePlusWeek)
    ) {
      $topic->topic_date_on_main = new Expression('NOW()');
    }

    $topic->save();
    $topic->user?->save();

    $voteDirection = null;
    if ($vote->vote_direction > 0) {
      $voteDirection = '+';
    } elseif ($vote->vote_direction < 0) {
      $voteDirection = '-';
    }

    return [
      'success' => true,
      'value' => $topic->topic_rating,
      'voteDirection' => $voteDirection,
      'message' => $message,
    ];
  }


  /**
   * @return array{
   *     success: bool,
   *     message: string,
   *     value?: float,
   *     voteDirection?: ?string
   * }
   * @throws InvalidConfigException
   * @throws Throwable
   * @throws \yii\db\Exception
   * @throws StaleObjectException
   */
  public function voteUserReview(int $reviewId, int $voteValue): array
  {
    $user = Yii::$app->user;

    $topic = UserReview::findOne($reviewId);

    if (!$topic) {
      return ['success' => false, 'message' => 'Отзыв не найден.'];
    }

    if ($topic->userId === $user->id) {
      return ['success' => false, 'message' => 'Ты не можешь голосовать за свои отзывы.'];
    }

    $vote = Vote::findOne([
      'target_type' => 'user_review',
      'target_id' => $reviewId,
      'user_voter_id' => Yii::$app->user->id,
    ]);

    if ($vote && ($vote->vote_direction === $voteValue)) {
      $vote->delete();
      $topic->rating = Vote::find()
        ->where(['target_type' => 'user_review', 'target_id' => $reviewId])
        ->sum('vote_value');

      $topic->save();
      return [
        'success' => true,
        'value' => $topic->rating ?? 0,
        'voteDirection' => null,
        'message' => 'Твоя оценка аннулирована',
      ];
    }

    if (!$vote) {
      $vote = new Vote([
        'target_id' => $reviewId,
        'comment_id' => null,
        'target_type' => 'user_review',
        'user_voter_id' => $user->id,
      ]);
    }

    if ($topic->user) {
      $topic->user->user_count_vote++;
    }
    $message = 'Твоя оценка принята';

    $vote->vote_direction += $voteValue;
    $vote->vote_value += $voteValue;
    $vote->vote_date = new Expression('NOW()');
    $vote->save();
    if ($topic->user) {
      $topic->user->user_rating += 0.1 * $voteValue;
    }
    $topic->rating = Vote::find()
      ->where(['target_type' => 'user_review', 'target_id' => $reviewId])
      ->sum('vote_value');

    $topic->save();
    $topic->user?->save();

    $voteDirection = null;
    if ($vote->vote_direction > 0) {
      $voteDirection = '+';
    }

    return ['success' => true, 'value' => $topic->rating, 'voteDirection' => $voteDirection, 'message' => $message];
  }

  /**
   * @return array{
   *     success: bool,
   *     message: string,
   *     value?: float,
   *     voteDirection?: ?string
   * }
   * @throws \yii\db\Exception
   * @throws InvalidConfigException
   */
  public function voteCompilation(int $compilationId, int $voteValue): array
  {
    $user = Yii::$app->user;

    $compilation = Compilation::findOne($compilationId);

    if (!$compilation) {
      return ['success' => false, 'message' => 'Подборка не найдена'];
    }

    if ($compilation->user_id === $user->id) {
      return ['success' => false, 'message' => 'Ты не можешь голосовать за свою подборку'];
    }

    $vote = Vote::findOne([
      'target_type' => 'compilation',
      'target_id' => $compilationId,
      'user_voter_id' => Yii::$app->user->id,
    ]);

    if ($vote && ($vote->vote_direction === $voteValue)) {
      $direction = ($voteValue > 0) ? 'плюс' : 'минус';
      return ['success' => false, 'message' => "У этой подборки уже стоит твой $direction"];
    }

    if (!$vote) {
      $vote = new Vote([
        'target_id' => $compilationId,
        'comment_id' => null,
        'target_type' => 'compilation',
        'user_voter_id' => $user->id,
      ]);
    }

    $shouldIncrementUserRating = false;
    if ($vote->vote_direction === -$voteValue) {
      if ($compilation->user) {
        $compilation->user->user_count_vote--;
      }
      $message = 'Твоя оценка аннулирована';
      if ($voteValue < 0) {
        $shouldIncrementUserRating = true;
      }
    } else {
      if ($compilation->user) {
        $compilation->user->user_count_vote++;
      }
      $message = 'Твоя оценка принята';
      if ($voteValue > 0) {
        $shouldIncrementUserRating = true;
      }
    }

    $vote->vote_direction += $voteValue;
    $vote->vote_value += $voteValue;
    $vote->vote_date = new Expression('NOW()');
    $vote->save();
    if ($compilation->user && $shouldIncrementUserRating) {
      $compilation->user->user_rating += 0.1 * $voteValue;
    }

    $totalVoteValue = Vote::find()
      ->where(['target_type' => $compilation->targetType, 'target_id' => $compilationId])
      ->sum('vote_value');

    if ($totalVoteValue <= -5) {
      $totalVoteValue = 0;
      $compilation->public = 2;
      Vote::deleteAll([
        'target_type' => $compilation->targetType,
        'target_id' => $compilationId,
      ]);
      $message = 'Твоя оценка принята.<br>Подборка скрыта из публичных, рейтинг отключен';
    }

    $compilation->rating = $totalVoteValue;

    $compilation->save();
    $compilation->user?->save();

    $voteDirection = null;
    if ($vote->vote_direction > 0) {
      $voteDirection = '+';
    } elseif ($vote->vote_direction < 0) {
      $voteDirection = '-';
    }

    return [
      'success' => true,
      'value' => $compilation->rating,
      'voteDirection' => $voteDirection,
      'message' => $message,
    ];
  }
}
