<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use RuntimeException;
use frontend\traits\Access;
use yii\helpers\{<PERSON><PERSON>, ArrayHelper};
use yii\base\InvalidConfigException;
use yii\db\{Expression, Transaction};
use common\components\db\ActiveQuery;
use frontend\modules\ajax\ApiController;
use common\helpers\{SgHelper, SearchHelper};
use common\models\{User, Warn, ActionLog, UserContact};

use function Sentry\captureException;

/**
 * @noinspection PhpUnused
 */

class UsersController extends ApiController
{
  /**
   * @return array{
   *     success: bool,
   *     message: string
   * }
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['moderate_user_bans'], methods: ['POST'])]
  public function actionWarn(): array
  {
    $user = Yii::$app->user;
    $userId = $this->request->post('user_id');
    $warnText = $this->request->post('warn_text');
    $ban = $this->request->post('ban');
    $banTime = $this->request->post('ban_time');

    if (!$userId || !$warnText) {
      return ['success' => false, 'message' => 'Недостаточно данных для выполнения запроса'];
    }

    $warnUser = User::findOne($userId);

    if (!$warnUser) {
      return ['success' => false, 'message' => 'Пользователь не найден'];
    }

    $authManager = Yii::$app->authManager;

    if (
      ($warnUser->inTeam || $authManager?->checkAccess($warnUser->id, 'admin_access'))
      && $user->can('admin_access_rbac')
    ) {
      return ['success' => false, 'message' => 'Нельзя выдать предупреждение/бан привелигированному пользвателю'];
    }

    /** @var Transaction $transaction */
    $transaction = Yii::$app->db->beginTransaction();
    /** @noinspection BadExceptionsProcessingInspection */
    try {
      if ($ban) {
        $warnUser->user_banned = 1;
        switch ($banTime) {
          case '5 days':
            $warnUser->user_banned_final = (string)(\time() + (5 * 24 * 60 * 60)); // 5 дней
            $banTimeHuman = 'на 5 дней';
            $warnType = 'timed';
            $warnEnd = $warnUser->user_banned_final;
            break;
          case '1 month':
            $warnUser->user_banned_final = (string)(\time() + (30 * 24 * 60 * 60)); // 1 месяц
            $banTimeHuman = 'на 1 месяц';
            $warnType = 'timed';
            $warnEnd = $warnUser->user_banned_final;
            break;
          default:
            $warnUser->user_about = '';
            $warnUser->show_name = null;
            $warnUser->changed_show_name = 1;
            $warnUser->user_banned_final = '0000000000';
            $banTimeHuman = 'навсегда';
            $warnType = 'forever';
            $warnEnd = '';
            UserContact::deleteAll(['user_id' => $userId]);
            break;
        }
        if (!$warnUser->save()) {
          throw new RuntimeException(Json::encode($warnUser->errors));
        }

        $warn = new Warn([
          'user_id' => $warnUser->id,
          'warn_action' => 'ban',
          'warn_text' => $warnText,
          'warn_date' => (string)\time(),
          // @phpstan-ignore-next-line
          'warn_date2' => new Expression('NOW()'),
          'warn_who' => $user->id,
          'warn_type' => $warnType,
          'warn_end' => $warnEnd,
        ]);

        if (!$warn->save()) {
          throw new RuntimeException(Json::encode($warn->errors));
        }

        ActionLog::log(ActionLog::USER_BAN, [
          'user_id' => $warnUser->id,
          'message' => $warnText,
        ]);

        // Отправляем письмо
        $talkTitle = 'Твой аккаунт заблокирован';
        $talkText = "Твой аккаунт на сайте StopGame.ru был заблокирован $banTimeHuman. Причину бана ты можешь посмотреть в истории замечаний на странице своего <a href=\"$warnUser->profileUrl\">профиля</a>.";
        SgHelper::newPm($warnUser->id, null, $talkTitle, $talkText, false, Yii::$app->params['robot_user_id']);
        $transaction->commit();

        return ['success' => true, 'message' => 'Как жестоко! Пользователь в «бане».'];
      }

      $warnUser->user_warn++;

      if (!$warnUser->save()) {
        throw new RuntimeException(Json::encode($warnUser->errors));
      }

      $warn = new Warn([
        'user_id' => $warnUser->id,
        'warn_action' => 'add',
        'warn_text' => $warnText,
        'warn_date' => (string)\time(),
        // @phpstan-ignore-next-line
        'warn_date2' => new Expression('NOW()'),
        'warn_who' => $user->id,
        'warn_type' => 'forever',
        'warn_end' => '',
      ]);

      if (!$warn->save()) {
        throw new RuntimeException(Json::encode($warn->errors));
      }

      ActionLog::log(ActionLog::USER_WARN, [
        'user_id' => $warnUser->id,
        'message' => $warnText,
      ]);

      // Отправляем письмо
      $talkTitle = 'Тебе вынесено замечание';
      $talkText = "Твоему аккаунту на сайте StopGame.ru было вынесено замечание. Его причину ты можешь посмотреть в истории замечаний на странице своего <a href=\"$warnUser->profileUrl\">профиля</a>.";
      SgHelper::newPm($warnUser->id, null, $talkTitle, $talkText, false, Yii::$app->params['robot_user_id']);
      $transaction->commit();

      return ['success' => true, 'message' => 'Пользователю выставлено замечание.'];
    } catch (Exception $e) {
      $transaction->rollBack();
      captureException($e);
      return ['success' => false, 'message' => 'Неизвестная ошибка, программист уже уведомлён'];
    }
  }

  /**
   * @return array<array{
   *     id: int,
   *     value: string,
   *     label: string,
   *     avatar: string
   * }>
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionAutocomplete(): array
  {
    $term = $this->request->post('term');

    $usersIds = SearchHelper::searchUsers($term, [
      'limit' => 20,
    ]);

    if ($usersIds['total_found'] === 0) {
      return [];
    }

    $usersIds = ArrayHelper::getColumn($usersIds['matches'], 'id');

    $usersQuery = User::find()
      ->where(['id' => $usersIds])
      ->orderBy(new Expression('FIELD(id, ' . \implode(', ', $usersIds) . ')'));

    return $this->renderAutocomplete($usersQuery);
  }

  /**
   * @return array<array{
   *      id: int,
   *      value: string,
   *      label: string,
   *      avatar: string
   *  }>
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionAutocompleteTeam(int $ex = 0): array
  {
    $term = $this->request->post('term');

    $query = User::find()
      ->joinWith('teamInfo')
      ->where([
        'OR',
        ['like', 'user_name', $term],
        ['like', 'show_name', $term],
      ]);

    if ($ex === 0) {
      $query->andWhere(['ex' => 0]);
    }

    return $this->renderAutocomplete($query);
  }

  /**
   * @param ActiveQuery<User> $query
   * @return array<array{
   *     id: int,
   *     value: string,
   *     label: string,
   *     avatar: string
   * }>
   * @throws Exception
   */
  public function renderAutocomplete(ActiveQuery $query): array
  {
    /** @var User[] $users */
    $users = $query
      ->limit(20)
      ->all();

    if (!$users) {
      return [];
    }

    $out = [];
    foreach ($users as $user) {
      $out[] = [
        'id' => $user->id,
        'value' => $user->user_name,
        'label' => $user->userName,
        'avatar' => $user->getAvatar(44),
      ];
    }

    return $out;
  }

  /**
   * @return array{
   *     success: int<0,1>
   * }
   * @noinspection PhpUnused
   */
  #[Access(methods: ['POST'])]
  public function actionSaveSetting(): array
  {
    if (Yii::$app->user->isGuest) {
      return ['success' => 1];
    }

    $body = $this->request->post();

    foreach ($body as $setting => $value) {
      Yii::$app->user->settings->set($setting, $value);
    }

    return ['success' => 1];
  }

  /**
   * @return array{
   *  warnsCount: int
   * }
   * @throws InvalidConfigException
   * @noinspection PhpUnused
   * @noinspection PhpMethodMayBeStaticInspection
   */
  #[Access(permissions: ['moderate_user_bans'], methods: ['GET'])]
  public function actionWarns(int $id): array
  {
    /** @var int $userWarns */
    $userWarns = Warn::find()
      ->where([
        'user_id' => $id,
      ])
      ->count();
    return [
      'warnsCount' => $userWarns,
    ];
  }
}
