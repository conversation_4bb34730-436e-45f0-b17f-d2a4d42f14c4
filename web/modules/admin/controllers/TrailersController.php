<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use RuntimeException;
use frontend\traits\Access;
use common\jobs\ConvertVideo;
use frontend\components\View;
use yii\base\InvalidConfigException;
use yii\data\{Sort, ActiveDataProvider};
use frontend\modules\admin\AdminController;
use common\exceptions\ControlFlowException;
use yii\db\{ActiveQuery, ActiveQueryInterface};
use common\widgets\formatters\PresentationFormatter;
use common\helpers\{SgHelper, ShellExecutor, SgStringHelper};
use common\models\{Article, GameTitle, ArticleGame, GameRequest, GameRequestAttachment};
use yii\web\{Request, Response, NotFoundHttpException, ForbiddenHttpException, BadRequestHttpException};

use function Sentry\captureException;

/**
 * @property-read View $view
 * @property-read Request $request
 * @property-read ActiveQuery<Article> $articlesQuery Смотри {@see TrailersController::getArticlesQuery}
 */
class TrailersController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trailers_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $query = $this->articlesQuery;
    $query->andWhere([
      'data_active' => 1,
    ]);
    $this->view->title = 'Трейлеры ⋅ Админка';

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trailers_access'], methods: ['GET'])]
  public function actionReadyToPublish(): string
  {
    $this->view->title = 'Трейлеры ⋅ Админка';
    $query = $this->articlesQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ]);

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trailers_access'], methods: ['GET'])]
  public function actionDrafts(): string
  {
    $this->view->title = 'Трейлеры ⋅ Админка';
    $user = Yii::$app->user;
    $query = $this->articlesQuery
      ->andWhere([
        'data_active' => false,
        'ready_to_publish' => false,
      ]);

    if (!$user->can('trailers_manage')) {
      $query->andWhere(['user_id' => $user->id]);
    }

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trailers_create'], methods: ['GET'])]
  public function actionAdd(): string
  {
    $this->view->title = 'Новый трейлер ⋅ Админка';
    $article = new Article();
    $article->user_id = (int)Yii::$app->user->id;
    $article->data_type = 'trailers';
    $gameTitle = new GameTitle();
    $article->setScenario(Article::SCENARIO_DRAFT);

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trailers_create'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Трейлер не найден');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя редактировать чужой трейлер');
    }

    if (!$article->data_active && !$article->ready_to_publish) {
      $article->setScenario(Article::SCENARIO_DRAFT);
    } else {
      $article->setScenario(Article::SCENARIO_VIDEO);
    }

    $gameTitle = $article->gameTitle ?? new GameTitle();
    $this->view->title = "$article->title ⋅ Админка";

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   * @throws NotFoundHttpException
   * @throws ForbiddenHttpException
   */
  #[Access(permissions: ['trailers_create'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    $post = $this->request->post();

    $article = new Article();
    $id = $post['Article']['base_id'];
    if ($id) {
      $article = Article::findOne($id);

      if (!$article) {
        throw new NotFoundHttpException('Трейлер не найден');
      }

      if (!$article->canEdit) {
        throw new ForbiddenHttpException('Нельзя редактировать чужой трейлер');
      }
    }

    $gameTitle = new GameTitle();
    $articleGames = [];

    $isNew = $article->isNewRecord;

    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $gameId = $post['GameTitle']['GameId'] ?? null;
      if (\is_array($gameId)) {
        $gameId = $gameId[0] ?? null;
      }
      $article->load($this->request->post());

      $article->setScenario(Article::SCENARIO_DRAFT);

      if (empty($gameId)) {
        if ($article->data_active || $article->ready_to_publish) {
          $gameTitle->addError('GameId', 'Игра не указана');
        }
      } else {
        $gameTitle = GameTitle::findOne(['GameId' => $gameId]);

        if ($gameTitle) {
          $articleGames[] = $gameTitle->GameId;
        } else {
          $gameTitle = new GameTitle(['GameId' => $gameId]);
          $gameTitle->addError('GameId', 'Игра с таким названием не найдена в базе');
        }
      }

      if (empty($article->video_external)) {
        if ($article->isNewRecord) {
          $article->addError('video_external', 'Не указана ссылка на внешнее видео');
        }
      } else {
        $link = $article->video_external;
        $article->video_attr = 'b:0;';
        $article->video_external = null;
      }

      if (!$article->data_active) {
        $article->data_active = 0;
        $article->ready_to_publish = (int)isset($post['submit_video_publish']);
      } else {
        $article->setScenario(Article::SCENARIO_TRAILER);
      }

      /* @phpstan-ignore-next-line */
      if (!$article->user_id && $article->isNewRecord && ($article->user_id !== '0')) {
        $article->user_id = (int)Yii::$app->user->id;
      }

      /* @phpstan-ignore-next-line */
      if ($article->user_id === '0') {
        $article->user_id = null;
      }

      $article->data_text = new PresentationFormatter([
        'title' => $article->data_title,
        'data' => $article->editor_js_content ?? '[]',
        'page' => 'all',
      ])();

      $article->data_type = 'trailers';

      if ($article->hasErrors() || $gameTitle->hasErrors()
        || !$article->validate() || !$article->save()
      ) {
        throw new ControlFlowException('Не удалось сохранить трейлер');
      }

      $article->refresh();

      // Если ссылка есть, то добавляем в очередь задачу на загрузку и конвертацию видео
      if (isset($link)) {
        Yii::$app->queue->push(
          new ConvertVideo([
            'baseId' => $article->base_id,
            'link' => $link,
          ]),
        );
      }

      $requestIds = $post['game_request_id'] ?? [];
      $requestTitles = $post['game_request_title'] ?? [];

      GameRequestAttachment::deleteAll([
        'target_type' => 'show',
        'target_id' => $article->base_id,
      ]);

      ArticleGame::deleteAll([
        'article_id' => $article->base_id,
      ]);

      // Привязываем запросы на добавление игр
      foreach ($requestTitles as $requestTitle) {
        $foundGame = GameTitle::findOne(['GameName' => $requestTitle]);

        // Если уже есть игра с таким названием, то просто привязываем игру
        if ($foundGame) {
          $articleGames[] = $foundGame->GameId;
        } else {
          $foundRequest = GameRequest::findOne(['title' => $requestTitle]);

          // Если уже есть запрос с таким названием
          if ($foundRequest) {
            // И он уже обработан — привязываем игру
            if ($foundRequest->game_id) {
              $articleGames[] = $foundRequest->game_id;
              // И он ещё не обработан — привязываем материал к запросу
            } else {
              $requestIds[] = $foundRequest->id;
            }
            // Если запроса нет — создаём его и привязываем материал к запросу
          } else {
            $request = new GameRequest([
              'title' => $requestTitle,
              'user_id' => Yii::$app->user->id,
            ]);
            $request->save();
            $request->user?->recalculateCredibility();
            $requestIds[] = $request->id;
          }
        }
      }

      $requestIds = \array_unique($requestIds);
      foreach ($requestIds as $requestId) {
        $attachment = new GameRequestAttachment([
          'request_id' => $requestId,
          'target_type' => 'show',
          'target_id' => $article->base_id,
        ]);
        $attachment->save();
      }

      $articleGames = \array_unique($articleGames);
      foreach ($articleGames as $gameId) {
        $articleGame = new ArticleGame([
          'article_id' => $article->base_id,
          'game_id' => $gameId,
        ]);
        $articleGame->save();
      }

      if (\count($article->dirtyAttributes) > 0) {
        $article->save();
      }

      $article->clearShowCache();
      $article->clearUnpublishedCache();

      $url = '/admin/trailers/drafts';
      if ($article->data_active) {
        $url = '/admin/trailers';
      } elseif ($article->ready_to_publish) {
        $url = '/admin/trailers/ready-to-publish';
      }
      $autosaveId = $isNew ? 'new' : $article->base_id;
      Yii::$app->session->setFlash('remove-autosave', 'trailers-form-' . $autosaveId);
      return $this->redirect($url);
    } catch (Exception $exception) {
      captureException($exception);
      $this->view->title = "$article->title ⋅ Админка";
      $article->setScenario(Article::SCENARIO_DRAFT);
      $url = '/admin/trailers/add';
      if (!$isNew) {
        $url = "/admin/trailers/edit/$article->base_id";
      }

      $message = $exception->getMessage();
      if (!empty($message)) {
        $article->addError('data_id', $message);
      }

      $this->view->registerJs("window.history.replaceState({}, '$article->title ⋅ Админка', '$url')");
      return $this->renderEditor($article, $gameTitle);
    }
  }

  /**
   * @throws Throwable
   * @throws ForbiddenHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trailers_create'], methods: ['POST', 'GET'])]
  public function actionPublish(string $id): Response|string
  {
    $params = Yii::$app->params;
    $user = Yii::$app->user;
    $tempDir = $params['video_temp_dir'];
    $article = Article::findOne($id);

    if (!$article || !$article->gameTitle) {
      throw new NotFoundHttpException('Трейлер не найден');
    }

    if (!$user->can('trailers_manage')) {
      throw new ForbiddenHttpException('Нельзя публиковать чужой трейлер');
    }

    if ($this->request->post()) {
      /** @noinspection BadExceptionsProcessingInspection */
      try {
        $image = $this->request->post('image');

        if ($image) {
          $imagePath = '/home/<USER>/images/temp/pics_get/' . \basename($image);
          if (\file_exists($imagePath)) {
            $paths = SgHelper::getUploadPaths('articles');
            \rename($imagePath, $paths['path']);
            $article->data_logo_original = $paths['url'];
            $article->data_logo = $paths['url'];
          } else {
            throw new RuntimeException(
              'Изображение не найдено, возможно, оно было удалено. Выбери обложку ещё раз.',
            );
          }
        }

        $article->setScenario(Article::SCENARIO_TRAILER);
        if ($article->validate()) {
          $tempName = $article->data_file;
          if (!$tempName) {
            throw new BadRequestHttpException('No file provided');
          }
          $gameName = \stripslashes(SgStringHelper::translit($article->gameTitle->GameName) . '.stopgame.ru');
          $pathinfo = \pathinfo($tempName);
          if (empty($pathinfo['extension'])) {
            throw new RuntimeException('Ошибка загрузки трейлера');
          }
          $extension = \strtolower($pathinfo['extension']);
          $time = Yii::$app->formatter->asTimestamp($article->data_add);

          $videoFiles = [
            'original' => "{$params['video_real_dir']}original/$gameName.$time.$extension",
          ];

          $dirname = \dirname($videoFiles['original']);
          if (!\is_dir($dirname) && !\mkdir($dirname, 0o775, true) && !\is_dir($dirname)) {
            throw new RuntimeException(\sprintf('Directory "%s" was not created', $dirname));
          }

          if (\file_exists($videoFiles['original'])) {
            // Переносим оригинал из временной папки в папку для переноса на другой сервер
            \rename("$tempDir$tempName", $videoFiles['original']);
          }

          // Переносим оригинал из временной папки в папку для переноса на другой сервер
          \rename("$tempDir$tempName", $videoFiles['original']);

          // Переносим перекодированные видео в папку для переноса на другой сервер
          /** @var string $quality */
          foreach ($params['video.quality'] as $quality) {
            $tempFile = "$tempDir$tempName.$quality";
            $newFile = "{$params['video_real_dir']}$quality/$gameName.$time.$quality.mp4";
            $dirname = \dirname($newFile);
            if (!\is_dir($dirname) && !\mkdir($dirname, 0o775, true) && !\is_dir($dirname)) {
              throw new RuntimeException(\sprintf('Directory "%s" was not created', $dirname));
            }
            if (\file_exists($tempFile)) {
              \rename($tempFile, $newFile);
              $videoFiles[$quality] = $newFile;
            }
          }

          $videoInfo = SgHelper::videoInfo($videoFiles);
          if (!$videoInfo) {
            throw new RuntimeException('Couldn\'t get video info');
          }
          $article->setScenario(Article::SCENARIO_DRAFT);
          $article->video_attr = \serialize($videoInfo);
          $article->data_file = \basename($videoFiles['original']);
          $article->save();
          $article->publish();
          $article->notify();
        } else {
          $errors = $article->getFirstErrors();
          Yii::$app->session->setFlash('error', \array_shift($errors));
        }

        $redirectUrl = '/admin/trailers/index';
        $hasUnpublished = $this->articlesQuery
          ->andWhere([
            'data_active' => 0,
            'ready_to_publish' => true,
          ])
          ->exists();

        if ($hasUnpublished) {
          $redirectUrl = '/admin/trailers/ready-to-publish';
        }

        return $this->redirect($redirectUrl);
      } catch (Exception $e) {
        Yii::$app->session->setFlash('error', $e->getMessage());
      }
    }

    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $original = $params['video_temp_dir'] . $article->data_file;

      if (empty($article->data_file) || !\file_exists($original)) {
        throw new RuntimeException('Видео не загружено!');
      }

      $glob = \glob('/home/<USER>/images/temp/pics_get/*.jpg');
      if (!$glob) {
        $glob = [];
      }

      foreach ($glob as $file) {
        \unlink($file);
      }

      $videoInfo = SgHelper::videoInfo(\compact('original'));
      if (!$videoInfo) {
        throw new RuntimeException('Ошибка при загрузке трейлера');
      }
      $seconds = $videoInfo['original']['time'];

      $start = \random_int(0, (int)($seconds / 9));
      $step = \ceil(($seconds - $start) / 9);

      $images = [];

      for ($i = $start; $i < $seconds; $i += $step) {
        $formattedTime = \sprintf('%02d:%02d:%02d', $i / 3600, ($i / 60) % 60, $i % 60);
        $filenameWithoutExtension = \pathinfo($article->data_file, \PATHINFO_FILENAME);
        $frameFileName = \sprintf('%s_%08d.jpg', $filenameWithoutExtension, $i);
        $filePath = '/home/<USER>/images/temp/pics_get/' . $frameFileName;

        ShellExecutor::runSilent(
          "/usr/bin/ffmpeg -ss \"$formattedTime\" -i $original -frames:v 1 $filePath",
        );

        if (!\file_exists($filePath)) {
          throw new RuntimeException('Ошибка при создании превью');
        }

        $images[] = "{$params['domain.images']}/temp/pics_get/" . $frameFileName;
      }

      return $this->render('publish', \compact('article', 'images'));
    } catch (Exception $e) {
      Yii::$app->session->addFlash('error', $e->getMessage());
      return $this->redirect('/admin/trailers/ready-to-publish');
    }
  }

  /**
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trailers_create'], methods: ['POST', 'GET'])]
  public function actionUnpublish(string $id): Response
  {
    $user = Yii::$app->user;
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Трейлер не найден');
    }

    if (!$user->can('trailers_manage')) {
      throw new ForbiddenHttpException('Нельзя скрывать чужой трейлер');
    }

    $article->ready_to_publish = 0;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/trailers/index');
  }

  /**
   * @throws Throwable
   */
  protected function renderEditor(Article $article, ?GameTitle $gameTitle): string
  {
    $gameTitle?->setScenario(GameTitle::SCENARIO_ATTACH);

    return $this->render('editor', \compact('article', 'gameTitle'));
  }

  /**
   * @return ActiveQuery<Article>
   * @throws InvalidConfigException
   * @noinspection PhpMethodMayBeStaticInspection
   */
  public function getArticlesQuery(): ActiveQueryInterface
  {
    return Article::find()
      ->with('user', 'gameTitle', 'announce')
      ->where(['data_type' => 'trailers']);
  }

  /**
   * @param ActiveQuery<Article> $query
   * @throws Throwable
   */
  protected function renderArticlesTable(ActiveQuery $query): string
  {
    $searchModel = new Article();
    $searchModel->setScenario(Article::SCENARIO_SEARCH);
    $searchModel->load($this->request->get());

    if (!empty($searchModel->data_type)) {
      $query->andWhere(['data_type' => $searchModel->data_type]);
    }

    if (!empty($searchModel->data_title)) {
      $query
        ->joinWith('gameTitle')
        ->andWhere([
          'or',
          ['LIKE', 'data_title', $searchModel->data_title],
          ['LIKE', 'stopgame_title.GameName', $searchModel->data_title],
        ]);
    }

    if (!empty($searchModel->data_add)) {
      $query->andWhere([
        'between',
        'data_add',
        "$searchModel->data_add 00:00:00",
        "$searchModel->data_add 23:59:59",
      ]);
    }

    if (!empty($searchModel->user_id)) {
      $query->andWhere(['user_id' => $searchModel->user_id]);
    }

    if (!empty($searchModel->data_active)) {
      $query->andWhere(['data_active' => $searchModel->data_active]);
    }

    $dataProvider = new ActiveDataProvider(\compact('query'));
    $dataProvider->setSort(
      new Sort([
        'defaultOrder' => ['data_add' => \SORT_DESC],
      ]),
    );

    $readyCount = $this->articlesQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ])->count();

    if ($readyCount === '0') {
      $readyCount = null;
    }

    return $this->render('index', \compact('dataProvider', 'searchModel', 'readyCount'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['trailers_create'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Трейлер не найден');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя удалить чужой трейлер');
    }

    if ($article->data_active || $article->ready_to_publish) {
      Yii::$app->session->setFlash('error', 'Нельзя удалить трейлер не являющийся черновиком');
      return $this->redirect('/admin/trailers');
    }

    $article->delete();
    $article->clearShowCache();
    $article->clearUnpublishedCache();
    return $this->redirect('/admin/trailers/drafts');
  }

  /**
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['trailers_create'], methods: ['POST', 'GET'])]
  public function actionToReview(string $id): Response
  {
    $article = Article::findOne($id);
    if (!$article) {
      throw new NotFoundHttpException('Трейлер не найден');
    }
    $article->ready_to_publish = 1;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/trailers/ready-to-publish');
  }
}
