<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use DateTime;
use Throwable;
use ReflectionClass;
use ReflectionProperty;
use ReflectionException;
use ReflectionNamedType;
use yii\web\UploadedFile;
use frontend\traits\Access;
use DateMalformedStringException;
use common\helpers\SgStringHelper;
use frontend\modules\ajax\ApiController;
use common\helpers\StreamGeneratorHelper;
use common\stream_generators\StreamImageGenerator;

use function Sentry\captureException;

/**
 * API для работы с генераторами стримов
 * @noinspection PhpUnused
 */
class StreamsController extends ApiController
{
  /**
   * @return array<string, array<string, mixed>>
   * @throws ReflectionException
   * @noinspection PhpMethodMayBeStaticInspection
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionPosterGenerators(): array
  {
    return StreamGeneratorHelper::getAvailableGenerators();
  }

  /**
   * @return array<string, string>|array{error: string}
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['POST'])]
  public function actionGeneratePoster(): array
  {
    $post = $this->request->post();
    $generatorName = $post['generator'];
    /** @var class-string<StreamImageGenerator> $className */
    $className = 'common\stream_generators\\' . \mb_ucfirst($generatorName) . 'Generator';
    try {
      $preparedPost = self::preparePost($className, $post);
      if (!isset($post['Stream']['title'])) {
        return ['error' => 'Не указано название стрима'];
      }
      if (!isset($preparedPost['dateTime']) || !($preparedPost['dateTime'] instanceof DateTime)) {
        return ['error' => 'Не указана дата начала стрима'];
      }
      $streamName = SgStringHelper::translit($post['Stream']['title']);
      $generator = new $className([
        'path' => Yii::getAlias(
          '@root/images/streams/' . $preparedPost['dateTime']->format('Y/m/d') . "/$streamName.jpg"
        ),
        ...$preparedPost
      ]);
      $generator->run();
      return $generator->imageUrls;
    } catch (\InvalidArgumentException $e) {
      return ['error' => $e->getMessage()];
    } catch (Throwable $e) {
      // @phpstan-ignore-next-line
      if (\ENV === 'dev') {
        throw $e;
      }
      // @phpstan-ignore-next-line
      captureException($e);
      return ['error' => 'Во время генерации произошла ошибка'];
    }
  }

  /**
   * @param class-string<StreamImageGenerator> $className
   * @param array<string, mixed> $post
   * @return array<string, mixed>
   * @throws ReflectionException
   * @throws DateMalformedStringException
   */
  public static function preparePost(string $className, array $post): array
  {
    $reflection = new ReflectionClass($className);
    $publicProperties = $reflection->getProperties(ReflectionProperty::IS_PUBLIC);
    $preparedPost = [];
    foreach ($publicProperties as $property) {
      if ($property->name === 'path') {
        continue;
      }
      $propertyName = $property->getName();
      if (isset($post[$propertyName])) {
        $value = $post[$propertyName];
        $propertyType = $property->getType();
        if ($propertyType instanceof ReflectionNamedType) {
          $type = $propertyType->getName();
          if ($type === 'int') {
            $value = (int)$value;
          } elseif ($type === 'bool') {
            $value = (bool)$value;
          } elseif ($type === 'float') {
            $value = (float)$value;
          } elseif ($type === 'string') {
            $value = (string)$value;
          } elseif ($type === 'DateTime') {
            $value = new DateTime($value);
          }
        }
        $preparedPost[$propertyName] = $value;
      } else {
        $file = UploadedFile::getInstanceByName($propertyName);
        if ($file) {
          $preparedPost[$propertyName] = $file->tempName;
        } elseif (!$property->hasDefaultValue() && !$property->isVirtual()) {
          $info = StreamGeneratorHelper::parseDocComment((string)$property->getDocComment());
          $propertyTitle = $info['name'] ?? $propertyName;
          throw new \InvalidArgumentException("Поле «{$propertyTitle}» не может быть пустым!");
        }
      }
    }
    return $preparedPost;
  }
}
