<?php

namespace frontend\controllers;

use Yii;
use yii\db\Exception;
use common\models\Config;
use frontend\traits\Access;
use yii\web\{Request, Controller};

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class WebhookController extends Controller
{
  public $enableCsrfValidation = false;

  /**
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['POST'])]
  public function actionTwitch(): string
  {
    $requestBody = $this->request->post();

    if (isset($requestBody['challenge'])) {
      // Без лишниих слов просто возвращаем твитчу challenge
      echo $requestBody['challenge'];
      exit;
    }

    if (isset($requestBody['subscription'])) {
      $type = $requestBody['subscription']['type'];

      Yii::$app->db->createCommand("SET innodb_snapshot_isolation = 'OFF'")->execute();
      /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
      switch ($type) {
        case 'stream.online':
          /** @var Config $streamConfig */
          $streamConfig = Config::findOne(['config_name' => 'twitch_steam_online']);
          /** @noinspection NullPointerExceptionInspection */
          $streamConfig->config_value = '1';
          /** @noinspection NullPointerExceptionInspection */
          $streamConfig->save();
          break;
        case 'stream.offline':
          /** @var Config $streamConfig */
          $streamConfig = Config::findOne(['config_name' => 'twitch_steam_online']);
          /** @noinspection NullPointerExceptionInspection */
          $streamConfig->config_value = '0';
          /** @noinspection NullPointerExceptionInspection */
          $streamConfig->save();
          break;
      }
      Yii::$app->db->createCommand("SET innodb_snapshot_isolation = 'ON'")->execute();
    }

    return '';
  }
}
