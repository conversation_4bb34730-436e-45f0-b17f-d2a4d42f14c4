<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Throwable;
use RuntimeException;
use frontend\traits\Access;
use frontend\components\View;
use common\helpers\SgStringHelper;
use frontend\modules\admin\AdminController;
use yii\data\{Pagination, ActiveDataProvider};
use common\models\{Announcer, AnnounceSchedule};
use yii\db\{Exception, Expression, StaleObjectException};
use yii\web\{Request, Response, UploadedFile, NotFoundHttpException};

/**
 * @property-read Request $request
 * @property-read View $view
 * @noinspection PhpUnused
 */
class AnnouncerController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $this->view->title = 'Анонсер ⋅ Админка';
    $query = Announcer::find()
      ->orderBy([
        'pinned_until' => \SORT_DESC,
        'date' => \SORT_DESC,
      ]);

    $dataProvider = new ActiveDataProvider(\compact('query'));
    $dataProvider->setPagination(
      new Pagination([
        'defaultPageSize' => 15,
      ])
    );

    return $this->render('index', \compact('dataProvider'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionAdd(): string
  {
    $announce = new Announcer();

    return $this->render('editor', \compact('announce'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $announce = Announcer::findOne($id);

    if (!$announce) {
      throw new NotFoundHttpException('Анонс не найден');
    }

    return $this->render('editor', \compact('announce'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['announcer_access'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    $post = $this->request->post();

    $announce = new Announcer();
    if (!empty($post['Announcer']['id'])) {
      $announce = Announcer::findOne($post['Announcer']['id']);

      if (!$announce) {
        throw new NotFoundHttpException('Анонс не найден');
      }
    }

    $announce->load($post);

    if (!isset($post['Announcer']['show_on_main'])) {
      $announce->show_on_main = 0;
    }
    if (!isset($post['Announcer']['no_title'])) {
      $announce->no_title = 0;
    }

    $poster = UploadedFile::getInstance($announce, 'poster');
    if ($poster) {
      $date = \date('Y/m/d');
      $newName = SgStringHelper::randomString(\random_int(7, 9)) . '.jpg';
      $uploadUrl = "articles/$date";
      $uploadPath = (string)Yii::getAlias("@root/images/$uploadUrl");
      if (!\is_dir($uploadPath) && !\mkdir($uploadPath, 0o755, true) && !\is_dir($uploadPath)) {
        throw new RuntimeException(\sprintf('Directory "%s" was not created', $uploadPath));
      }
      $poster->saveAs((string)Yii::getAlias("@root/images/$uploadUrl/$newName"));
      $announce->poster = Yii::$app->params['domain.images'] . "/$uploadUrl/$newName";
    }

    if (!$announce->save()) {
      return $this->render('editor', \compact('announce'));
    }

    return $this->redirect('/admin/announcer');
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionShow(string $id): Response
  {
    return $this->toggleShown($id, 1);
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionHide(string $id): Response
  {
    return $this->toggleShown($id, 0);
  }

  /**
   * @param int<0,1> $shown
   * @throws NotFoundHttpException
   * @throws Exception
   */
  public function toggleShown(int|string $id, int $shown): Response
  {
    $announce = Announcer::findOne($id);

    if (!$announce) {
      throw new NotFoundHttpException('Анонс не найден');
    }

    $announce->show_on_main = $shown;
    $announce->save();

    return $this->redirect('/admin/announcer');
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   */
  #[Access(permissions: ['announcer_access'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    /** @var ?Announcer $announce */
    $announce = Announcer::findOne($id);

    $announce?->delete();

    return $this->redirect('/admin/announcer');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['POST', 'GET'])]
  public function actionPin(string $id): Response|string
  {
    /** @var ?Announcer $announce */
    $announce = Announcer::findOne($id);

    if (!$announce) {
      throw new NotFoundHttpException('Анонс не найден');
    }

    if ($this->request->isPost) {
      $announce->load($this->request->post());
      if ($announce->save()) {
        Yii::$app->session->addFlash('success', 'Анонс закреплён на главной');
        return $this->redirect('/admin/announcer');
      }
    }

    return $this->render('pin', \compact('announce'));
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionUnpin(string $id): Response
  {
    /** @var ?Announcer $announce */
    $announce = Announcer::findOne($id);

    if (!$announce) {
      throw new NotFoundHttpException('Анонс не найден');
    }

    $announce->pinned_until = new Expression('NOW()');
    $announce->save();
    Yii::$app->session->addFlash('success', 'Анонс откреплён с главной');

    return $this->redirect('/admin/announcer');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionScheduler(): string
  {
    $this->view->title = 'Планировщик анонсов ⋅ Админка';

    $query = AnnounceSchedule::find()
      ->orderBy([
        'publish_at' => \SORT_ASC,
      ]);

    $dataProvider = new ActiveDataProvider(\compact('query'));
    $dataProvider->setPagination(
      new Pagination([
        'defaultPageSize' => 15,
      ])
    );

    return $this->render('scheduler', \compact('dataProvider'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionAddSchedule(): string
  {
    $announce = new AnnounceSchedule();

    return $this->render('schedule-editor', \compact('announce'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET'])]
  public function actionEditSchedule(string $id): string
  {
    $announce = AnnounceSchedule::findOne($id);

    if (!$announce) {
      throw new NotFoundHttpException('Анонс не найден');
    }

    return $this->render('schedule-editor', \compact('announce'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['POST'])]
  public function actionSaveSchedule(): Response|string
  {
    $post = $this->request->post();

    $announce = new AnnounceSchedule();
    if (!empty($post['AnnounceSchedule']['id'])) {
      $announce = AnnounceSchedule::findOne($post['AnnounceSchedule']['id']);

      if (!$announce) {
        throw new NotFoundHttpException('Анонс не найден');
      }
    }

    $announce->load($post);

    if (!isset($post['AnnounceSchedule']['no_title'])) {
      $announce->no_title = 0;
    }

    $poster = UploadedFile::getInstance($announce, 'image');
    if ($poster) {
      $date = \date('Y/m/d');
      $newName = SgStringHelper::randomString(\random_int(7, 9)) . '.jpg';
      $uploadUrl = "articles/$date";
      $uploadPath = (string)Yii::getAlias("@root/images/$uploadUrl");
      if (!\is_dir($uploadPath) && !\mkdir($uploadPath, 0o755, true) && !\is_dir($uploadPath)) {
        throw new RuntimeException(\sprintf('Directory "%s" was not created', $uploadPath));
      }
      $poster->saveAs((string)Yii::getAlias("@root/images/$uploadUrl/$newName"));
      $announce->image = Yii::$app->params['domain.images'] . "/$uploadUrl/$newName";
    }

    if (!$announce->save()) {
      return $this->render('schedule-editor', \compact('announce'));
    }

    return $this->redirect('/admin/announcer/scheduler');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['POST', 'GET'])]
  public function actionDeleteSchedule(string $id): Response
  {
    $announce = AnnounceSchedule::findOne($id);

    $announce?->delete();

    return $this->redirect('/admin/announcer/scheduler');
  }
}
