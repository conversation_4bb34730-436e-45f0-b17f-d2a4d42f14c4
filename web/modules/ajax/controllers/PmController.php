<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use Throwable;
use yii\helpers\Json;
use RuntimeException;
use yii\db\Expression;
use common\models\Talk;
use common\models\User;
use common\models\Game;
use ReflectionException;
use common\models\TalkUser;
use frontend\traits\Access;
use common\helpers\SgHelper;
use common\models\TalkComment;
use common\helpers\SgTextHelper;
use common\helpers\SgImageHelper;
use yii\web\NotFoundHttpException;
use common\helpers\SgStringHelper;
use yii\web\BadRequestHttpException;
use common\helpers\SgDateTimeHelper;
use yii\base\InvalidConfigException;
use yii\web\ServerErrorHttpException;
use frontend\modules\ajax\ApiController;
use common\notifications\NewPmCommentNotification;

use function Sentry\captureException;

/**
 * @noinspection PhpUnused
 */

class PmController extends ApiController
{
  /**
   * @return array{
   *     total: int,
   *     dialogues: array<array{
   *         id: int,
   *         title: string,
   *         userName: string,
   *         userAvatar: string,
   *         userUrl: string,
   *         userInTeam: bool,
   *         messageCount: int,
   *         unreadMessageCount: int,
   *         timestamp: string,
   *         lastRead: string,
   *         lastMessage: string,
   *     }>,
   *     lastUpdate: int
   * }
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionDialogues(?int $offset = null): array
  {
    $user = Yii::$app->user->identity;

    $subquery = TalkUser::find()
      ->select('talk_id')
      ->where([
        'user_id' => $user->id,
        'talk_user_active' => 1,
      ]);

    $pmQuery = Talk::find()
      ->where(['talk_id' => $subquery])
      ->orderBy(['talk_date_last' => \SORT_DESC])
      ->limit(30);

    if ($offset) {
      $pmQuery->offset($offset);
    }

    $total = (int)(clone($pmQuery))->limit(null)->offset(null)->count();

    /** @var Talk[] $pm */
    $pm = $pmQuery->all();

    $dialogues = [];

    foreach ($pm as $item) {
      $lastMessage = $item->lastMessage->comment_text ?? $item->lastMessage->talk_text ?? '';
      $lastMessage = \preg_replace('/(<\/\w+>)/', '$1 ', $lastMessage);
      if (\is_array($lastMessage)) {
        $lastMessage = \implode('', $lastMessage);
      }
      if (empty($lastMessage)) {
        $lastMessage = '';
      }
      $lastMessage = \strip_tags($lastMessage);

      /** @var TalkUser $userInfo */
      $userInfo = $item->getTalkUsers()->where(['user_id' => $user->id])->one();

      $newComments = 0;
      if ($userInfo->date_last < $item->talk_date_last) {
        $newComments = (int)$item->getTalkComments()->where(['>', 'comment_date', $userInfo->date_last])->count();
      }
      if (!$userInfo->date_last) {
        $newComments = $item->talk_count_comment + 1;
      }

      $dialogues[] = [
        'id' => $item->talk_id,
        'title' => $item->talk_title,
        'userName' => $item->collocutor->userName ?? '[удалённый пользователь]',
        'userAvatar' => $item->collocutor?->getAvatar(42) ?? '',
        'userUrl' => $item->collocutor->profileUrl ?? '',
        'userInTeam' => $item->collocutor->inTeam ?? false,
        'messageCount' => $item->talk_count_comment + 1,
        'unreadMessageCount' => $newComments,
        'timestamp' => $item->talk_date_last,
        'lastRead' => $userInfo->date_last ?? '1970-01-01 00:00:00',
        'lastMessage' => $lastMessage,
      ];
    }

    return [
      'total' => $total,
      'dialogues' => $dialogues,
      'lastUpdate' => \time(),
    ];
  }

  /**
   * @return array{
   *     totalDialogues: int,
   *     dialogues: array<array{
   *         id: int,
   *         title: string,
   *         userName: string,
   *         userAvatar: string,
   *         userUrl: string,
   *         userInTeam: bool,
   *         messageCount: int,
   *         unreadMessageCount: int,
   *         timestamp: string,
   *         lastRead: string,
   *         lastMessage: string,
   *     }>,
   *     lastUpdate: int
   * }
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionUpdates(): array
  {
    $lastUpdate = $this->request->get('lastUpdate');
    if (!$lastUpdate) {
      throw new BadRequestHttpException('Не указано время последнего обновления');
    }

    $user = Yii::$app->user->identity;

    $subquery = TalkUser::find()
      ->select('talk_id')
      ->where([
        'user_id' => $user->id,
        'talk_user_active' => 1,
      ]);

    /** @var Talk[] $talks */
    $talks = Talk::find()
      ->where(['talk_id' => $subquery])
      ->andWhere(['>', 'talk_date_last', new Expression("FROM_UNIXTIME($lastUpdate)")])
      ->all();

    $updates = [];
    foreach ($talks as $talk) {
      $lastMessage = $talk->lastMessage->comment_text ?? $talk->lastMessage->talk_text ?? '';
      $lastMessage = \preg_replace('/(<\/\w+>)/', '$1 ', $lastMessage);
      if (\is_array($lastMessage)) {
        $lastMessage = \implode('', $lastMessage);
      }
      if (empty($lastMessage)) {
        $lastMessage = '';
      }
      $lastMessage = \strip_tags($lastMessage);

      /** @var TalkUser $userInfo */
      $userInfo = $talk->getTalkUsers()->where(['user_id' => $user->id])->one();

      $newComments = 0;
      if ($userInfo->date_last < $talk->talk_date_last) {
        $newComments = (int)$talk->getTalkComments()->where(['>', 'comment_date', $userInfo->date_last])->count();
      }
      if (!$userInfo->date_last) {
        $newComments = $talk->talk_count_comment + 1;
      }

      $updates[] = [
        'id' => $talk->talk_id,
        'title' => $talk->talk_title,
        'userName' => $talk->collocutor->userName ?? '[удалённый пользователь]',
        'userAvatar' => $talk->collocutor?->getAvatar(42) ?? '',
        'userUrl' => $talk->collocutor->profileUrl ?? '',
        'userInTeam' => $talk->collocutor->inTeam ?? false,
        'messageCount' => $talk->talk_count_comment + 1,
        'unreadMessageCount' => $newComments,
        'timestamp' => $talk->talk_date_last,
        'lastRead' => $userInfo->date_last ?? '1970-01-01 00:00:00',
        'lastMessage' => $lastMessage,
      ];
    }

    $total = (int)Talk::find()
      ->where(['talk_id' => $subquery])
      ->orderBy(['talk_date_last' => \SORT_DESC])
      ->count();

    return [
      'dialogues' => $updates,
      'totalDialogues' => $total,
      'lastUpdate' => \time(),
    ];
  }

  /**
   * @return array{
   *     messages: array<array{
   *         id: 'initial'|int,
   *         text: string,
   *         timestamp: string,
   *         isMine: bool,
   *         avatar: string,
   *         userName: string,
   *         attachments: mixed[]
   *     }>,
   *     total: int,
   *     lastUpdate: int
   * }
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionMessages(string $dialogueId, ?int $offset = null, ?int $lastMessageId = null): array
  {
    $talk = Talk::findOne($dialogueId);

    /** @var ?TalkUser $talkUser */
    $talkUser = Yii::$app->user->identity->getTalkUsers()->where(['talk_id' => $dialogueId])->one();

    if (!$talk || !$talkUser) {
      throw new NotFoundHttpException('Диалог не найден');
    }

    $perPage = 30;

    $messagesQuery = $talk->getTalkComments()
      ->orderBy(['comment_id' => \SORT_DESC])
      ->limit($perPage);

    if ($offset) {
      $messagesQuery->offset($offset);
    }

    if ($lastMessageId) {
      $messagesQuery->andWhere(['>', 'comment_id', $lastMessageId]);
    }

    $count = (int)(clone($messagesQuery))->limit(null)->offset(null)->count();

    /** @var TalkComment[] $messages */
    $messages = $messagesQuery->all();

    $realMessages = [];
    foreach ($messages as $message) {
      $attachments = [];
      try {
        if (!empty($message->attachments)) {
          $attachments = Json::decode($message->attachments);

          foreach ($attachments as $key => $attachment) {
            if ($attachment['type'] === 'game') {
              $status = Yii::$app->user->identity->getTracks()->andWhere(['GameId' => $attachment['gameId']])->select(
                'status',
              )->scalar();

              if ($status === false) {
                $status = -1;
              }

              $attachments[$key]['gameInfo']['status'] = $status;
            }
          }
        }
      } catch (Exception) {
        // Shit happens
      }

      $realMessages[] = [
        'id' => $message->comment_id,
        'text' => $message->comment_text,
        'timestamp' => $message->comment_date,
        'isMine' => $message->user_id === (int)Yii::$app->user->id,
        'avatar' => $message->user ? $message->user->getAvatar(42) : SgImageHelper::placeholderUrl(42, 42, 'avatar'),
        'userName' => $message->user->userName ?? '[удалённый пользователь]',
        'attachments' => $attachments,
      ];
    }
    unset($messages);

    if ($count <= $perPage + $offset) {
      $attachments = [];

      if ($talk->attachments) {
        $attachments = $talk->attachments;
        // Почему-то аттачи в первом сообщении проходят json_encode дважды. Декодируем обратно до победного.
        while (!\is_array($attachments)) {
          try {
            $attachments = Json::decode($attachments);
            if ($attachments === null) {
              $attachments = [];
            }
          } catch (Exception) {
            $attachments = [];
          }
        }
      }

      $realMessages[] = [
        'id' => 'initial',
        'text' => $talk->talk_text,
        'timestamp' => $talk->talk_date,
        'isMine' => $talk->user_id === (int)Yii::$app->user->id,
        'avatar' => $talk->user ? $talk->user->getAvatar(42) : SgImageHelper::placeholderUrl(42, 42, 'avatar'),
        'userName' => $talk->user->userName ?? '[удалённый пользователь]',
        'attachments' => $attachments,
      ];
    }

    return [
      'messages' => $realMessages,
      'total' => $count,
      'lastUpdate' => \time(),
    ];
  }

  /**
   * @return array{
   *     success: true
   * }
   * @throws BadRequestHttpException
   * @throws NotFoundHttpException
   * @throws ServerErrorHttpException
   * @throws InvalidConfigException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionMarkAsRead(): array
  {
    $dialogueId = $this->request->post('dialogueId');
    if (!$dialogueId) {
      throw new BadRequestHttpException('Не указан идентификатор диалога');
    }
    /** @var ?TalkUser $talkUser */
    $talkUser = Yii::$app->user->identity->getTalkUsers()->where(['talk_id' => $dialogueId])->one();
    if (!$talkUser) {
      throw new NotFoundHttpException('Диалог не найден');
    }
    $talkUser->date_last = new Expression('NOW()');
    $talkUser->comment_id_last = TalkComment::find()->where(['target_id' => $dialogueId])->max('comment_id') ?? 0;
    $talkUser->comment_count_new = 0;
    if (!$talkUser->save()) {
      throw new ServerErrorHttpException('Не удалось сохранить диалог');
    }

    return ['success' => true];
  }

  /**
   * @return array{
   *     message: array{
   *         id: int,
   *         text: string,
   *         timestamp: string,
   *         isMine: bool,
   *         avatar: string,
   *         userName: string,
   *         attachments: string,
   *     }
   * }
   * @throws BadRequestHttpException
   * @throws ServerErrorHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionSend(): array
  {
    $user = Yii::$app->user->identity;
    $post = $this->request->post();

    Yii::$app->params['comments.allow_tags'] = ['br', 'blockquote', 'spoiler', 'sg-spoiler', 'p'];
    $comment_text = SgTextHelper::jevixComment($post['comment_text']);
    $attachments = $post['attachment_info'] ?? [];
    foreach ($attachments as $key => $attachment) {
      $attachments[$key] = Json::decode($attachment);
    }
    $attachmentsCount = \count($attachments);
    $attachments = Json::encode($attachments);

    if (($attachmentsCount <= 0) && (\strlen(\trim(\strip_tags($comment_text))) <= 0)) {
      throw new BadRequestHttpException('Сообщение не может быть пустым');
    }

    /** @var ?TalkUser $talkUser */
    $talkUser = $user->getTalkUsers()->where(['talk_id' => $post['dialogue_id']])->one();
    if (!$talkUser) {
      throw new NotFoundHttpException('Диалог не найден');
    }

    if (!$user->inTeam) {
      $antifloodTime = Yii::$app->params['pm.antiflood_time'];
      $flood = TalkComment::find()
        ->where(['user_id' => $user->id])
        ->andWhere(['>', 'comment_date', new Expression("DATE_SUB(NOW(), INTERVAL $antifloodTime SECOND)")])
        ->count();

      if ($flood > 0) {
        throw new BadRequestHttpException("Защита от флуда! Подожди $antifloodTime секунд");
      }
    }

    $talkComment = new TalkComment([
      'target_id' => $post['dialogue_id'],
      'user_id' => $user->id,
      'comment_text' => $comment_text,
      'comment_text_hash' => \md5($comment_text),
      'comment_user_ip' => $this->request->userIP ?: '0',
      'attachments' => $attachments,
    ]);

    if (!$talkComment->save()) {
      throw new ServerErrorHttpException('Не удалось сохранить сообщение');
    }
    $talkComment->refresh();

    /** @var ?Talk $dialogue */
    $dialogue = Talk::findOne($post['dialogue_id']);

    if (!$dialogue) {
      throw new ServerErrorHttpException('Диалог не существует');
    }

    $dialogue->talk_date_last = new Expression('NOW()');
    $dialogue->talk_count_comment++;
    $dialogue->save();

    /** @var TalkUser $talkUser */
    $talkUser = TalkUser::find()
      ->where([
        'talk_id' => $post['dialogue_id'],
        'user_id' => $user->id,
      ])
      ->one();
    $talkUser->date_last = new Expression('NOW()');
    $talkUser->save();

    /** @var ?TalkUser $collocutor */
    $collocutor = TalkUser::find()
      ->where(['talk_id' => $post['dialogue_id']])
      ->andWhere(['NOT', ['user_id' => $user->id]])
      ->one();

    if ($collocutor) {
      $collocutor->comment_count_new++;
      $collocutor->save();

      $toUser = $collocutor->user;

      if ($toUser) {
        new NewPmCommentNotification([
          'messageId' => $talkComment->comment_id,
          'userId' => $toUser->id,
        ])->send();
      }
    }

    $attachments = [];
    try {
      $attachments = Json::decode($talkComment->attachments ?? '[]');
    } catch (Throwable) {
      // Do nothing
    }

    return [
      'message' => [
        'id' => $talkComment->comment_id,
        'text' => $talkComment->comment_text,
        'timestamp' => $talkComment->comment_date,
        'isMine' => true,
        'avatar' => $user->getAvatar(42),
        'userName' => $user->userName,
        'attachments' => $attachments,
      ],
    ];
  }

  /**
   * @return array{
   *     messages: array<array{
   *         id: int,
   *         text: string,
   *         timestamp: string,
   *         isMine: bool,
   *         avatar: string,
   *         userName: string,
   *         attachments: mixed
   *     }>,
   *     lastUpdate: int,
   * }
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionNewMessages(string $id, string $lastMessageId): array
  {
    $talk = Talk::findOne($id);
    /** @var ?TalkUser $talkUser */
    $talkUser = Yii::$app->user->identity->getTalkUsers()->where(['talk_id' => $id])->one();

    if (!$talk || !$talkUser) {
      throw new NotFoundHttpException('Диалог не найден');
    }

    /** @var TalkComment[] $messages */
    $messages = TalkComment::find()
      ->where(['target_id' => $id])
      ->andWhere(['>', 'comment_id', $lastMessageId])
      ->orderBy(['comment_id' => \SORT_ASC])
      ->all();

    $realMessages = [];
    foreach ($messages as $message) {
      try {
        $attachments = Json::decode($message->attachments ?? '[]');
      } catch (Throwable) {
        $attachments = [];
      }

      $realMessages[] = [
        'id' => $message->comment_id,
        'text' => $message->comment_text,
        'timestamp' => $message->comment_date,
        'isMine' => ($message->user->id ?? 0) === (int)Yii::$app->user->id,
        'avatar' => $message->user?->getAvatar(42) ?? '',
        'userName' => $message->user->userName ?? '[удалённый пользователь]',
        'attachments' => $attachments,
      ];
    }
    unset($messages);

    return [
      'messages' => $realMessages,
      'lastUpdate' => \time(),
    ];
  }

  /**
   * @return array{
   *     success: bool
   * }
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws ServerErrorHttpException
   * @throws \yii\db\Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionDelete(string $id): array
  {
    /** @var ?TalkUser $talkUser */
    $talkUser = TalkUser::find()
      ->where([
        'talk_id' => $id,
        'user_id' => Yii::$app->user->id,
        'talk_user_active' => 1,
      ])
      ->one();

    if (!$talkUser) {
      throw new NotFoundHttpException('Ты не участник этого диалога');
    }

    $talkUser->talk_user_active = 2;

    if (!$talkUser->save()) {
      throw new ServerErrorHttpException('Не удалось удалить диалог');
    }

    return ['success' => true];
  }

  /**
   * @return array{
   *     id: int,
   *     title: string,
   *     userName: string,
   *     userAvatar: string,
   *     userUrl: string,
   *     userInTeam: bool,
   *     messageCount: int,
   *     unreadMessageCount: int,
   *     timestamp: string,
   *     lastRead: string,
   *     lastMessage: string,
   * }
   * @throws ServerErrorHttpException
   * @throws ReflectionException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionCreate(): array
  {
    $user = Yii::$app->user->identity;
    $post = $this->request->post();

    Yii::$app->params['comments.allow_tags'] = ['br', 'blockquote', 'spoiler', 'sg-spoiler', 'p'];
    $talk_users = \strip_tags($post['talk_users']);
    $talk_title = \strip_tags($post['talk_title']);
    $talk_text = SgTextHelper::jevixComment($post['talk_text']);
    $attachments = $post['attachment_info'] ?? [];

    $sendPmTime = Yii::$app->session->get('send_pm_time');
    if ($sendPmTime
      && !$user->inTeam
      && ($sendPmTime + 30 > \time())
    ) {
      Yii::$app->session->set('send_pm_time', \time());
      throw new ServerErrorHttpException('Слишком часто отправляешь сообщения');
    }

    if (SgStringHelper::strlenu($talk_users) < 3) {
      throw new ServerErrorHttpException('Введи имя пользователя');
    }
    if (SgStringHelper::strlenu($talk_title) < 3) {
      throw new ServerErrorHttpException('Введи заголовок диалога');
    }
    if (SgStringHelper::strlenu($talk_text) < 2) {
      throw new ServerErrorHttpException('Введи текст сообщения');
    }
    if (\count($attachments) > 3) {
      throw new ServerErrorHttpException('Максимум 3 вложения');
    }

    if ($user->user_banned === 1) {
      /** @var User $destUser */
      $destUser = User::find()
        ->where([
          'OR',
          ['user_name' => $talk_users],
          ['show_name' => $talk_users],
        ])
        ->one();

      if (!$destUser->inTeam) {
        throw new ServerErrorHttpException('Ты можешь отправлять сообщения только администрации сайта');
      }
    }

    foreach ($attachments as $key => $attachment) {
      $attachments[$key] = Json::decode($attachment);
    }
    $result = SgHelper::newPm(null, $talk_users, $talk_title, $talk_text, false, null, $attachments);

    if (!$result) {
      throw new ServerErrorHttpException('Не удалось отправить сообщение');
    }

    Yii::$app->session->set('send_pm_time', \time());

    /** @var ?Talk $talk */
    $talk = Talk::find()
      ->where([
        'talk_title' => $talk_title,
        'user_id' => $user->id,
      ])
      ->one();

    if (!$talk) {
      throw new RuntimeException('Не удалось создать диалог');
    }

    $lastMessage = $talk->talk_text;
    $lastMessage = (string)\preg_replace('/(<\/\w+>)/', '$1 ', $lastMessage);
    $lastMessage = \strip_tags($lastMessage);

    return [
      'id' => $talk->talk_id,
      'title' => $talk->talk_title,
      'userName' => $talk->collocutor->userName ?? '[удалённый пользователь]',
      'userAvatar' => $talk->collocutor?->getAvatar(42) ?? '',
      'userUrl' => $talk->collocutor->profileUrl ?? '',
      'userInTeam' => $talk->collocutor->inTeam ?? false,
      'messageCount' => 1,
      'unreadMessageCount' => 0,
      'timestamp' => $talk->talk_date_last,
      'lastRead' => SgDateTimeHelper::unix2sql(\time()),
      'lastMessage' => $lastMessage,
    ];
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionShareGame(): string
  {
    $user = Yii::$app->user->identity;
    $gameId = $this->request->post('gameId');
    $userIds = $this->request->post('userId');
    $message = $this->request->post('message');

    $game = Game::findOne($gameId);

    if (!$game || !$game->mainTitle) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    $gameImg = SgImageHelper::thumb($game->mainTitle->poster, 280, 280);

    $attachments = [
      [
        'type' => 'game',
        'url' => $game->fullUrlWithDomain,
        'gameId' => $game->GameId,
        'gameInfo' => [
          'poster' => $gameImg,
          'url' => $game->fullUrl,
          'name' => SgHelper::theFix($game->mainTitle->title),
          'releaseDate' => $game->releaseDate,
        ],
      ],
    ];

    foreach ($userIds as $userId => $_stuff) {
      try {
        /** @var ?Talk $talk */
        $talk = Talk::find()
          ->leftJoin(['user1' => 'ls_talk_user'],
            'ls_talk.talk_id = user1.talk_id AND user1.user_id = :userId AND user1.talk_user_active=1',
            [':userId' => $userId])
          ->leftJoin(['user2' => 'ls_talk_user'],
            'ls_talk.talk_id = user2.talk_id AND user2.user_id = :userId2 AND user2.talk_user_active=1',
            [':userId2' => $user->id])
          ->andWhere(['NOT', ['user1.user_id' => null]])
          ->andWhere(['NOT', ['user2.user_id' => null]])
          ->one();

        if (!$talk) {
          SgHelper::newPm($userId, null, 'Делюсь игрой', $message, false, null, $attachments);
        } else {
          $talkComment = new TalkComment([
            'target_id' => $talk->talk_id,
            'user_id' => $user->id,
            'comment_text' => $message,
            'comment_text_hash' => \md5($message),
            'comment_user_ip' => $this->request->userIP ?: '0',
            'attachments' => Json::encode($attachments),
          ]);

          if (!$talkComment->save()) {
            throw new ServerErrorHttpException('Не удалось сохранить сообщение');
          }
          $talkComment->refresh();

          $talk->talk_date_last = new Expression('NOW()');
          $talk->talk_count_comment++;
          $talk->save();

          /** @var TalkUser $talkUser */
          $talkUser = TalkUser::find()
            ->where([
              'talk_id' => $talk->talk_id,
              'user_id' => $user->id,
            ])
            ->one();
          $talkUser->date_last = new Expression('NOW()');
          $talkUser->save();

          /** @var ?TalkUser $collocutor */
          $collocutor = TalkUser::find()
            ->where(['talk_id' => $talk->talk_id])
            ->andWhere(['NOT', ['user_id' => $user->id]])
            ->one();

          if ($collocutor) {
            $collocutor->comment_count_new++;
            $collocutor->save();

            $toUser = $collocutor->user;

            if ($toUser) {
              new NewPmCommentNotification([
                'messageId' => $talkComment->comment_id,
                'userId' => $toUser->id,
              ])->send();
            }
          }
        }
      } catch (Throwable $e) {
        captureException($e);
      }
    }

    return 'ok';
  }
}
