<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use yii\db\Expression;
use common\models\Game;
use common\models\Vote;
use ReflectionException;
use yii\web\HttpException;
use frontend\traits\Access;
use common\helpers\SgHelper;
use common\models\GameTitle;
use common\models\UserReview;
use common\models\UserActivity;
use common\helpers\SgImageHelper;
use common\helpers\SgStringHelper;
use yii\web\NotFoundHttpException;
use common\enums\UserActivityType;
use yii\web\ForbiddenHttpException;
use yii\web\BadRequestHttpException;
use yii\base\InvalidConfigException;
use frontend\modules\ajax\ApiController;
use common\notifications\NewReviewNotification;

/** @noinspection PhpUnused */

class ReviewsController extends ApiController
{
  /**
   * @return array{
   *     user: array{
   *         avatar: string,
   *         name: string
   *     }
   * }
   * @noinspection PhpUnused
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionMinimumInfo(): array
  {
    return [
      'user' => [
        'avatar' => Yii::$app->user->identity?->getAvatar(72) ?? '',
        'name' => Yii::$app->user->identity->userName ?? '[несуществующий пользователь]',
        'isBanned' => Yii::$app->user->identity->isBanned ?? false,
      ],
    ];
  }

  /**
   * @return array{
   *     review_id?: int,
   *     user?: array{
   *         avatar: string,
   *         name: string
   *     },
   *     game?: array{
   *         id: int,
   *         poster: string,
   *         name: string
   *     },
   *     rating?: float
   * }
   * @noinspection PhpUnused
   * @throws NotFoundHttpException
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionGameInfo(string $id): array
  {
    $game = Game::findOne($id);
    if ($game === null) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    /** @var ?UserReview $review */
    $review = Yii::$app->user->identity?->getReviews()
      ->andWhere([
        'game_id' => $game->GameId,
        'deleted_final' => 0,
      ])->one();
    if (!empty($review)) {
      return ['review_id' => $review->id];
    }

    $rating = 0.0;
    /** @var ?Vote $vote */
    $vote = Yii::$app->user->identity?->getVotes()
      ->where([
        'target_type' => 'game',
        'target_id' => $game->GameId,
      ])
      ->one();

    if ($vote) {
      $rating = $vote->vote_value;
    }

    return [
      'user' => [
        'avatar' => Yii::$app->user->identity?->getAvatar(72) ?? '',
        'name' => Yii::$app->user->identity->userName ?? '[удалённый пользователь]',
        'isBanned' => Yii::$app->user->identity->isBanned ?? false,
      ],
      'game' => [
        'id' => $game->GameId,
        'poster' => SgImageHelper::thumb($game->square_poster ?? $game->mainTitle->poster ?? null, 192, 192),
        'name' => SgHelper::theFix($game->mainTitle->GameName ?? '[удалённая игра]'),
      ],
      'rating' => $rating,
    ];
  }

  /**
   * @noinspection PhpUnused
   * @return array{
   *     user: array{
   *         avatar: string,
   *         name: string
   *     },
   *     game: array{
   *         id: int,
   *         poster: string,
   *         name: string
   *     },
   *     rating: float,
   *     title?: string,
   *     review: string,
   *     plus_minus: string
   * }
   * @throws NotFoundHttpException
   * @throws ReflectionException
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionReviewInfo(string $id): array
  {
    $review = UserReview::findOne($id);
    if ($review === null) {
      throw new NotFoundHttpException('Отзыв не найден');
    }

    return [
      'user' => [
        'avatar' => $review->user?->getAvatar(72) ?? '',
        'name' => $review->user->userName ?? '[удалённый пользователь]',
        'isBanned' => $review->user->isBanned ?? false,
      ],
      'game' => [
        'id' => $review->game_id,
        'poster' => SgImageHelper::thumb(
          $review->game->square_poster ?? $review->game->mainTitle->poster ?? null,
          192,
          192,
        ),
        'name' => SgHelper::theFix($review->game->mainTitle->GameName ?? '[удалённая игра]'),
      ],
      'rating' => $review->gameRating->vote_value ?? 0.0,
      'title' => $review->header ?? '',
      'review' => $review->editor_js_content,
      'reviewDate' => $review->created_at,
      'plus_minus' => $review->plus_minus,
    ];
  }

  /**
   * @return array{
   *     status: string
   * }
   * @throws BadRequestHttpException
   * @throws ForbiddenHttpException
   * @throws HttpException
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @throws \yii\db\Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionSave(): array
  {
    $post = $this->request->post();

    $currentUser = Yii::$app->user;

    if (!$currentUser->identity) {
      throw new ForbiddenHttpException('Ошибка авторизации');
    }

    if ($currentUser->identity->isBanned) {
      throw new ForbiddenHttpException('Ты в бане');
    }

    $game = Game::findOne($post['game_id']);
    if ($game === null) {
      throw new NotFoundHttpException('Игра не найдена');
    }
    if (!$game->isReleased) {
      throw new BadRequestHttpException('Игра еще не вышла');
    }
    if (!$game->canBeReviewed) {
      throw new BadRequestHttpException('К этой игре нельзя написать отзыв');
    }

    if (isset($post['review_id'])) {
      /** @var ?UserReview $review */
      $review = UserReview::findOne($post['review_id']);

      if (!$review || !$review->user) {
        throw new NotFoundHttpException('Отзыв не найден');
      }

      if (($review->user_id !== $currentUser->id) && !$currentUser->can('admin_access')) {
        throw new BadRequestHttpException('Нельзя редактировать чужой отзыв');
      }

      $user = $review->user;
    } else {
      $user = $currentUser->identity;
      /** @var ?UserReview $review */
      $review = $user->getReviews()
        ->andWhere([
          'game_id' => $post['game_id'],
          'deleted' => 0,
        ])
        ->one();
      if (!$review) {
        $review = new UserReview([
          'user_id' => $currentUser->id,
          'game_id' => $post['game_id'],
          'rating' => 0,
        ]);
      }
    }

    if ($review->deleted_final) {
      throw new BadRequestHttpException('Нельзя редактировать удалённый отзыв');
    }

    $review->deleted = 0;

    if ($user->isCurrentUser && !empty($post['rating']) && ($post['rating'] > 0)) {
      $vote = Vote::findOne([
        'target_type' => 'game',
        'target_id' => $post['game_id'],
        'user_voter_id' => $user->id,
      ]);

      if (!$vote) {
        $vote = new Vote([
          'target_type' => 'game',
          'target_id' => $post['game_id'],
          'user_voter_id' => $user->id,
          'vote_direction' => 1,
        ]);
      }

      $vote->vote_value = $post['rating'];
      $vote->save();

      /**
       * @var ?array{
       *     rating_sum: int,
       *     people_sum: int
       * } $currentRating
       */
      $currentRating = Vote::find()
        ->select(new Expression('SUM(vote_value) as rating_sum, SUM(vote_direction) as people_sum'))
        ->where([
          'target_type' => 'game',
          'target_id' => $post['game_id'],
        ])
        ->asArray()
        ->one();

      if ($game->rating) {
        $game->rating->RateCount = $currentRating['rating_sum'] ?? 0;
        $game->rating->RateTotal = $currentRating['people_sum'] ?? 0;
        $game->rating->save();
      }

      $newRate = 0;
      if ($currentRating && ($currentRating['rating_sum'] > 0)) {
        $newRate = $currentRating['rating_sum'] / $currentRating['people_sum'];
      }

      GameTitle::updateAll(['game_rating' => $newRate], ['GameId' => $post['game_id']]);
    }

    $review->header = $post['title'];
    $post['review'] = SgStringHelper::processBlackwords($post['review']);

    $review->editor_js_content = $post['review'];
    $review->plus_minus = $post['plus_minus'];

    $isNew = $review->isNewRecord;

    if (!$review->save()) {
      $errors = $review->getFirstErrors();
      throw new HttpException(500, (string)\reset($errors));
    }

    if ($isNew) {
      $user->setReadDate($review->targetId, $review->targetType);
      new NewReviewNotification(['reviewId' => $review->id])->send();
      UserActivity::register(UserActivityType::NEW_REVIEW, null, [
        'reviewId' => $review->id,
      ], $review->user_id);
    }

    return ['status' => 'success'];
  }
}
