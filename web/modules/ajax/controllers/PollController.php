<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use yii\db\Exception;
use yii\db\Expression;
use frontend\traits\Access;
use yii\web\NotFoundHttpException;
use common\models\{Poll, PollVote};
use frontend\modules\ajax\ApiController;

/**
 * @noinspection PhpUnused
 */

class PollController extends ApiController
{
  /**
   * @return array{
   *     success: bool,
   *     pollId?: int,
   *     errors?: array<string, mixed>
   * }
   * @noinspection PhpUnused
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionCreate(): array
  {
    $user = Yii::$app->user;
    $poll = new Poll([
      'user_id' => $user->id,
    ]);
    if (!$poll->save()) {
      return ['success' => false, 'errors' => $poll->getErrors()];
    }
    $poll->refresh();
    return ['success' => true, 'pollId' => $poll->id];
  }

  /**
   * @return array{
   *     success: bool,
   *     totalVotes: int,
   *     votes: int[],
   *     voted: bool,
   *     currentVote: int
   * }
   * @throws NotFoundHttpException
   */
  #[Access(methods: ['GET'])]
  public function actionInfo(string $id): array
  {
    $user = Yii::$app->user;
    $poll = Poll::findOne($id);
    if ($poll === null) {
      throw new NotFoundHttpException('Опрос не найден');
    }
    $totalVotes = (int)$poll->getVotes()->count();
    /** @var int[] $votes */
    $votes = $poll->getVotes()
      ->select(new Expression('count(*) as count'))
      ->groupBy('value')
      ->indexBy('value')
      ->column();

    $voted = false;
    $currentVote = -1;

    if ($user->isLoggedIn) {
      /** @var ?PollVote $currentUserVote */
      $currentUserVote = $poll->getVotes()->where(['user_id' => $user->id])->one();
      if ($currentUserVote !== null) {
        $voted = true;
        $currentVote = $currentUserVote->value;
      }
    }

    return [
      'success' => true,
      'totalVotes' => $totalVotes,
      'votes' => $votes,
      'voted' => $voted,
      'currentVote' => $currentVote,
    ];
  }

  /**
   * @return array{
   *     success: bool,
   *     message?: string,
   *     errors?: array<string, mixed>,
   *     totalVotes?: int,
   *     votes?: int[],
   *     voted?: bool,
   *     currentVote?: int
   * }
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionVote(): array
  {
    $pollId = $this->request->post('pollId');
    $value = $this->request->post('answer');
    $user = Yii::$app->user;

    $poll = Poll::findOne($pollId);
    if ($poll === null) {
      throw new NotFoundHttpException('Опрос не найден');
    }

    $existingVote = $poll->getVotes()->where(['user_id' => $user->id])->one();
    if ($existingVote) {
      return ['success' => false, 'message' => 'Твой голос уже учтён'];
    }

    $vote = new PollVote([
      'poll_id' => $pollId,
      'user_id' => $user->id,
      'value' => $value,
    ]);
    if (!$vote->save()) {
      return ['success' => false, 'errors' => $vote->getErrors(), 'message' => 'Голос не засчитан'];
    }
    $vote->refresh();
    return $this->actionInfo($pollId);
  }
}
