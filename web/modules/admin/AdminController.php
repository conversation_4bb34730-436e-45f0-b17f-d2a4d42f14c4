<?php

namespace frontend\modules\admin;

use Yii;
use Exception;
use Throwable;
use frontend\components\View;
use frontend\traits\AccessControlTrait;
use frontend\modules\admin\widgets\AdminMenu;
use yii\base\{Action, InvalidConfigException};
use yii\web\{Controller, NotFoundHttpException, BadRequestHttpException};

/**
 * Class AdminController
 * @property View $view
 */
class AdminController extends Controller
{
  use AccessControlTrait;

  public $enableCsrfValidation = false;
  public $layout = 'default';
  protected string $adminMenuCurrent = '';

  /**
   * {@inheritdoc}
   * @param Action<AdminController> $action
   * @throws NotFoundHttpException
   * @throws BadRequestHttpException
   */
  #[\Override]
  public function beforeAction($action): bool
  {
    Yii::$app->params['disable_stalcraft'] = true;
    Yii::$app->adManager->disabled = true;
    $this->view->registerMetaTag([
      'name' => 'robots',
      'content' => 'noindex',
    ]);
    if (!Yii::$app->user->can('admin_access')) {
      throw new NotFoundHttpException('Страница не найдена');
    }

    return parent::beforeAction($action);
  }

  /**
   * {@inheritdoc}
   * @param array<string, mixed> $params
   * @throws Exception
   * @throws Throwable
   */
  #[\Override]
  public function render($view, $params = []): string
  {
    $this->view->params['adminMenuCurrent'] = $this->adminMenuCurrent;
    $this->view->params['show_admin_panel'] = true;
    return parent::render($view, $params);
  }

  /**
   * {@inheritdoc}
   * @throws Exception
   * @throws InvalidConfigException
   * @throws Throwable
   */
  #[\Override]
  public function renderContent($content): string
  {
    $this->view->blocks['sidebar'] = new AdminMenu([
      'current' => $this->adminMenuCurrent,
    ])();
    $this->view->registerJsFileSG('/dist/js/plugins/admin.js', ['type' => 'module', 'data-cfasync' => 'false']);
    $this->view->registerCssFileSG('/index.css', ['position' => View::POS_HEAD]);
    $this->view->registerCssFileSG('/css/dist/admin.css', ['position' => View::POS_HEAD]);
    return parent::renderContent($content);
  }
}
