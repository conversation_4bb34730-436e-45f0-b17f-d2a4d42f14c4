<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use RuntimeException;
use common\models\{Game,
  GameTitle,
  GameRating,
  GameSeries,
  GameGallery,
  GameRequest,
  SimilarGame,
  GameRelation,
  GameSeriesGame,
  GameImageRequest,
  GameFixSuggestion,
  GameSeriesRelation
};
use frontend\traits\Access;
use common\enums\GameStatus;
use yii\helpers\{Url, ArrayHelper};
use yii\base\InvalidConfigException;
use common\components\db\ActiveQuery;
use yii\data\{Sort, ActiveDataProvider};
use yii\web\{Request, Response, NotFoundHttpException};
use frontend\modules\admin\{AdminController, models\GameSearchForm};
use common\helpers\{Sg<PERSON><PERSON>per, SgStringHelper, GameImageRequestHelper};
use yii\db\{ActiveQueryInterface, Query, Expression, StaleObjectException};

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class GamesController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $searchModel = new GameSearchForm();
    $get = $this->request->get();
    $isLast = false;

    if (isset($get['term']) && !empty(\trim($get['term']))) {
      $term = \trim($get['term']);
      $searchModel->title = $term;
      $term = SgHelper::stripThe($term);
      $ids = GameTitle::find()
        ->select('GameId')
        ->andWhere([
          'OR',
          ['LIKE', 'GameName', $term],
          ['LIKE', 'GameKeywords', $term],
        ])
        ->groupBy(['GameId'])
        ->column();

      $gamesQuery = GameTitle::find()
        ->where([
          'GameId' => $ids,
          'GameNamePrimary' => 1,
        ])
        ->orderBy(new Expression('CHAR_LENGTH(GameName) ASC'));
    } else {
      $isLast = true;
      $ids = Game::find()
        ->select(['GameId'])
        ->orderBy(['GameId' => \SORT_DESC])
        ->limit(30)
        ->column();

      $gamesQuery = GameTitle::find()
        ->where([
          'GameId' => $ids,
          'GameNamePrimary' => 1,
        ])
        ->orderBy(['GameId' => \SORT_DESC]);
    }

    $gamesDataProvider = new ActiveDataProvider([
      'query' => $gamesQuery,
    ]);

    return $this->render(
      'index',
      \compact(
        'gamesDataProvider',
        'searchModel',
        'isLast',
      ),
    );
  }

  /**
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET', 'POST'])]
  public function actionMerge(string $id): Response|string
  {
    $game = GameTitle::findOne(['GameId' => $id, 'GameNamePrimary' => 1]);

    if (!$game) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    if ($this->request->isPost) {
      $newId = $this->request->post('newId');
      if ((int)$newId !== (int)$id) {
        $newGame = GameTitle::findOne(['GameId' => $newId, 'GameNamePrimary' => 1]);

        if ($newGame) {
          $this->mergeGames((int)$id, $newId);
          Yii::$app->session->setFlash('success', "Игра «{$game->title}» объединена с «{$newGame->title}»");
          return $this->redirect("/admin/games?term=$newGame->title");
        }
        Yii::$app->session->setFlash('error', 'Игра не найдена');
      } else {
        Yii::$app->session->setFlash('error', 'Нельзя объединить игру саму с собой');
      }
    }

    return $this->render('merge', \compact('game'));
  }

  /**
   * @throws \yii\db\Exception
   */
  protected function mergeUpdate(string $table, int $newId, int $oldId, string $attribute = 'GameId'): void
  {
    Yii::$app->db
      ->createCommand()
      ->update($table, [$attribute => $newId], [$attribute => $oldId])
      ->execute();
  }

  /**
   * @throws \yii\db\Exception
   */
  protected function mergeUpdateWithoutDuplicates(string $table, string $keyField, int $newId, int $oldId): void
  {
    $db = Yii::$app->db;
    $subQuery = new Query()
      ->select('*')
      ->from("`$table` d")
      ->where("d.$keyField = o.$keyField")
      ->andWhere(['d.GameId' => $newId]);
    $db
      ->createCommand()
      ->update("`$table` o", ['o.GameId' => $newId], [
        'AND',
        ['o.GameId' => $oldId],
        ['NOT', ['EXISTS', $subQuery]],
      ])
      ->execute();
    $db
      ->createCommand()
      ->delete($table, ['GameId' => $oldId])
      ->execute();
  }

  /**
   * @throws \yii\db\Exception
   */
  protected function mergeDelete(string $table, int $oldId, string $attribute = 'GameId'): void
  {
    Yii::$app->db
      ->createCommand()
      ->delete($table, [$attribute => $oldId])
      ->execute();
  }

  /**
   * @throws \yii\db\Exception
   * @throws Throwable
   * @throws StaleObjectException
   */
  protected function mergeGames(int $oldId, int $newId): void
  {
    $transaction = Yii::$app->db->beginTransaction();
    // @phpstan-ignore-next-line
    if (!$transaction) {
      // В принципе не возможно, присутствует тут чисто для типизации
      throw new RuntimeException('Не удалось создать транзакцию');
    }
    /** @noinspection BadExceptionsProcessingInspection */
    try {
      // Переносим скрины
      /** @var GameGallery[] $gameGallery */
      $gameGallery = GameGallery::find()->where(['GameId' => $oldId])->all();
      foreach ($gameGallery as $gallery) {
        $oldPath = $gallery->path;
        $gallery->GameId = $newId;
        $newPath = $gallery->path;
        $dir = \dirname($newPath);
        if (!\is_dir($dir) && !\mkdir($dir, 0o777, true) && !\is_dir($dir)) {
          throw new RuntimeException(\sprintf('Directory "%s" was not created', $dir));
        }
        if ($gallery->save()) {
          \rename($oldPath, $newPath);
        }
      }
      // Игры привязанные к FAQ
      $this->mergeUpdateWithoutDuplicates('faq_games', 'faq_id', $newId, $oldId);
      // Удаляем привязку компании к старой игре
      $this->mergeUpdateWithoutDuplicates('games_company', 'comp_id', $newId, $oldId);
      // Переносим факты
      $this->mergeUpdate('games_facts', $newId, $oldId);
      // Удаляем жанры
      $this->mergeDelete('games_genres', $oldId);
      $this->mergeDelete('game_tags_attachments', $oldId, 'game_id');
      // Меняем ключи
      $this->mergeUpdate('games_keys', $newId, $oldId);
      // Удаляем платформы
      $this->mergeUpdateWithoutDuplicates('games_platform', 'platform', $newId, $oldId);
      // Переносим блоги
      $this->mergeUpdate('ls_topic', $newId, $oldId);
      $this->mergeUpdateWithoutDuplicates('ls_topic_games', 'topic_id', $newId, $oldId);
      // Переносим новости
      $this->mergeUpdateWithoutDuplicates('news_games', 'news_id', $newId, $oldId);
      // Переносим промо
      $this->mergeUpdate('promo', $newId, $oldId);
      // Переносим ссылки
      $this->mergeUpdate('random_links', $newId, $oldId);
      $db = Yii::$app->db;
      $subQuery = new Query()
        ->select('*')
        ->from('`show_games` new')
        ->where('new.article_id = old.article_id')
        ->andWhere([
          'new.game_id' => $newId,
        ]);
      $db
        ->createCommand()
        ->update('`show_games` old', ['old.game_id' => $newId], [
          'AND',
          [
            'old.game_id' => $oldId,
          ],
          ['NOT', ['EXISTS', $subQuery]],
        ])
        ->execute();
      $db
        ->createCommand()
        ->delete('show_games', ['game_id' => $oldId])
        ->execute();
      // Удаляем рейтинг
      $oldRating = GameRating::findOne(['GameId' => $oldId]);
      if ($oldRating) {
        $newRating = GameRating::findOne(['GameId' => $newId]);
        if (!$newRating) {
          $oldRating->GameId = $newId;
          $oldRating->save();
        } else {
          $oldRating->delete();
        }
      }
      // Переносим скрины
      $this->mergeUpdate('stopgame_screens', $newId, $oldId, 'game_id');
      // Переносим обои
      $this->mergeUpdate('stopgame_wp', $newId, $oldId, 'game_id');
      $this->mergeUpdate('stopgame_wp_count', $newId, $oldId, 'game_id');
      // Переносим стримы
      $this->mergeUpdateWithoutDuplicates('stream_games', 'data_id', $newId, $oldId);
      // Переносим отслеживания
      $this->mergeUpdateWithoutDuplicates('track', 'user_id', $newId, $oldId);
      // Переносим голоса
      $subQuery = new Query()
        ->select('*')
        ->from('`ls_vote` new')
        ->where('new.vote_id = old.vote_id')
        ->andWhere([
          'new.target_id' => $newId,
          'new.target_type' => 'game',
        ]);
      $db
        ->createCommand()
        ->update('`ls_vote` old', ['old.target_id' => $newId], [
          'AND',
          [
            'old.target_id' => $oldId,
            'old.target_type' => 'game',
          ],
          ['NOT', ['EXISTS', $subQuery]],
        ])
        ->execute();
      $db
        ->createCommand()
        ->delete('ls_vote', ['target_id' => $oldId, 'target_type' => 'game'])
        ->execute();
      // Переносим видео
      $this->mergeUpdate('video', $newId, $oldId);
      // Удаляем все запросы к старой игре
      $this->mergeUpdate('game_fix_suggestion', $newId, $oldId, 'game_id');
      $this->mergeUpdate('game_image_request', $newId, $oldId, 'game_id');
      // Переносим отзывы
      $this->mergeUpdate('user_reviews', $newId, $oldId, 'game_id');
      // Переносим добавления в подборки
      $subQuery = new Query()
        ->select('*')
        ->from('`compilation_items` new')
        ->where('new.compilation_id = old.compilation_id')
        ->andWhere([
          'new.target_id' => $newId,
          'new.target_type' => 'game',
        ]);
      $db
        ->createCommand()
        ->update('`compilation_items` old', ['old.target_id' => $newId], [
          'AND',
          [
            'old.target_id' => $oldId,
            'old.target_type' => 'game',
          ],
          ['NOT', ['EXISTS', $subQuery]],
        ])
        ->execute();
      $db
        ->createCommand()
        ->delete('compilation_items', ['target_id' => $oldId, 'target_type' => 'game'])
        ->execute();
      // Переносим названия (через ORM, чтобы обновление поискового индекса сработало)
      /** @var GameTitle[] $titles */
      $titles = GameTitle::find()->where(['GameId' => $oldId])->all();
      foreach ($titles as $title) {
        $title->GameId = $newId;
        $title->GameNamePrimary = 0;
        $title->save();
      }
      // Удаляем все связи между играми и удаляем игру из серий
      GameSeriesGame::deleteAll(['game_id' => $oldId]);
      GameRelation::deleteAll(['first_game_id' => $oldId]);
      GameRelation::deleteAll(['second_game_id' => $oldId]);
      // Удаляем игру из похожих
      SimilarGame::deleteAll(['first_game_id' => $oldId]);
      SimilarGame::deleteAll(['second_game_id' => $oldId]);
      // Удаляем инфу по игре (в последнюю очередь!)
      $this->mergeDelete('stopgame_info', $oldId);
      $transaction->commit();
    } catch (Exception|Throwable $e) { // @phpstan-ignore-line
      $transaction->rollBack();
      throw $e;
    }
  }

  /**
   * @throws Throwable
   * @throws InvalidConfigException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionAttachRequestToGame(int $requestId, int $gameId): Response|string
  {
    $request = GameRequest::findOne($requestId);
    $game = Game::findOne($gameId);

    return $this->render('attach-request-to-game', \compact('request', 'game'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET', 'POST'])]
  public function actionAttachToRequest(string $id): Response|string
  {
    $request = GameRequest::findOne($id);

    if (!$request) {
      throw new NotFoundHttpException('Запрос не найден');
    }

    if ($this->request->isPost) {
      $request->load($this->request->post());
      $request->closed_by = (int)Yii::$app->user->id;
      $request->save();
      $request->process();
      $request->user?->recalculateCredibility();
      $url = '/admin/games/requests?section=new';
      if ($request->user && !$request->user->inTeam) {
        $url .= '&filter=users';
      }
      if (Yii::$app->session->has('last_requests_url')) {
        $url = Yii::$app->session->get('last_requests_url');
      }
      if ($this->request->post('redirect')) {
        $url = $this->request->post('redirect');
      }
      return $this->redirect($url);
    }

    return $this->render('attach-to-request', \compact('request'));
  }

  /**
   * @throws Throwable
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionRemoveRequest(string $id): Response
  {
    $request = GameRequest::findOne($id);

    if (!$request) {
      throw new NotFoundHttpException('Запрос не найден');
    }

    $request->closed_by = Yii::$app->user->id;
    $request->save();
    $request->user?->recalculateCredibility();

    if ($this->request->referrer) {
      return $this->redirect($this->request->referrer);
    }

    return $this->redirect('/admin/games');
  }

  /**
   * @return ActiveQuery<GameTitle>
   * @throws InvalidConfigException
   */
  public function getUnfinishedQuery(string $id = 'all'): ActiveQueryInterface
  {
    $gamesQuery = GameTitle::find()
      ->joinWith('info')
      ->where([
        'GameNamePrimary' => 1,
      ])
      ->andWhere(['NOT', ['stopgame_info.GameId' => null]]);

    switch ($id) {
      case 'no-square-poster':
        $gamesQuery->andWhere([
          'OR',
          ['square_poster' => null],
          ['square_poster' => ''],
        ]);
        break;
      case 'no-large-poster':
        $gamesQuery->andWhere([
          'OR',
          ['large_poster' => null],
          ['large_poster' => ''],
        ]);
        break;
      case 'no-release-date':
        $gamesQuery->andWhere([
          'OR',
          ['stopgame_info.GameDate' => '0000-00-00'],
          ['stopgame_info.GameDate' => ''],
          ['stopgame_info.GameDate' => null],
        ]);
        break;
      case 'incomplete-release-date':
        $gamesQuery
          ->andWhere([
            'OR',
            ['LIKE', 'stopgame_info.GameDate', '%-00-00', false],
            ['LIKE', 'stopgame_info.GameDate', '%-00', false],
          ])
          ->andWhere(['NOT', ['stopgame_info.GameDate' => '0000-00-00']])
          ->andWhere(['NOT', ['stopgame_info.GameDate' => '0001-00-00']]);
        break;
      case 'no-platforms':
        $gamesQuery
          ->leftJoin('games_platform', 'games_platform.GameId = stopgame_title.GameId')
          ->andWhere([
            'AND',
            ['<=', 'stopgame_info.GameDate', new Expression('NOW()')],
            ['games_platform.GameId' => null],
          ]);
        break;
      case 'no-genres':
        $gamesQuery
          ->leftJoin('game_tags_attachments', 'game_tags_attachments.game_id = stopgame_title.GameId')
          ->andWhere([
            'AND',
            ['<=', 'stopgame_info.GameDate', new Expression('NOW()')],
            ['game_tags_attachments.game_id' => null],
          ]);
        break;
      case 'no-screenshots':
        $gamesQuery
          ->leftJoin('games_gallery', 'games_gallery.GameId = stopgame_title.GameId')
          ->andWhere([
            'AND',
            ['<=', 'stopgame_info.GameDate', new Expression('NOW()')],
            ['games_gallery.screen_id' => null],
          ]);
        break;
      case 'all':
      default:
        $gamesQuery
          ->distinct()
          ->leftJoin('games_platform', 'games_platform.GameId = stopgame_title.GameId')
          ->leftJoin('games_gallery', 'games_gallery.GameId = stopgame_title.GameId')
          ->leftJoin('game_tags_attachments', 'game_tags_attachments.game_id = stopgame_title.GameId')
          ->andWhere([
            'OR',
            ['stopgame_info.GameDate' => '0000-00-00'],
            ['stopgame_info.GameDate' => ''],
            ['LIKE', 'stopgame_info.GameDate', '%-00-00', false],
            ['LIKE', 'stopgame_info.GameDate', '%-00', false],
            ['stopgame_info.GameDate' => null],
            [
              'AND',
              ['<=', 'stopgame_info.GameDate', new Expression('NOW()')],
              [
                'OR',
                ['games_platform.GameId' => null],
                ['game_tags_attachments.game_id' => null],
                ['games_gallery.screen_id' => null],
              ],
            ],
            ['square_poster' => null],
            ['large_poster' => null],
          ]);
        break;
    }

    return $gamesQuery;
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionUnfinished(string $id = 'all'): string
  {
    $searchModel = new GameSearchForm();
    $type = $id;
    $sort = $this->request->get('sort', 'date_add');

    $mainQuery = $this->getUnfinishedQuery($type);

    $counts = [
      'all' => $this->getUnfinishedQuery()->count(),
      'no-square-poster' => $this->getUnfinishedQuery('no-square-poster')->count(),
      'no-large-poster' => $this->getUnfinishedQuery('no-large-poster')->count(),
      'no-release-date' => $this->getUnfinishedQuery('no-release-date')->count(),
      'incomplete-release-date' => $this->getUnfinishedQuery('incomplete-release-date')->count(),
      'no-platforms' => $this->getUnfinishedQuery('no-platforms')->count(),
      'no-genres' => $this->getUnfinishedQuery('no-genres')->count(),
      'no-screenshots' => $this->getUnfinishedQuery('no-screenshots')->count(),
    ];

    /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
    switch ($sort) {
      case 'actuality':
        $mainQuery
          ->innerJoin([
            'act' => new Query()
              ->select('GameId, count(*) as actuality')
              ->from('news_games')
              ->leftJoin('news_data', 'news_data.news_id = news_games.news_id')
              ->where(['>=', 'news_data.news_date', new Expression('NOW() - INTERVAL 2 MONTH')])
              ->groupBy('GameId'),
          ], 'act.GameId = stopgame_title.GameId')
          ->orderBy([
            'actuality' => \SORT_DESC,
            'GameName' => \SORT_ASC,
          ]);
        break;
      case 'popularity':
        $mainQuery
          ->innerJoin([
            'pop' => new Query()
              ->select('GameId, count(*) as popularity')
              ->from('track')
              ->groupBy('GameId'),
          ], 'pop.GameId = stopgame_title.GameId')
          ->orderBy([
            'popularity' => \SORT_DESC,
            'GameName' => \SORT_ASC,
          ]);
        break;
      case 'date_add':
        $mainQuery->orderBy(['GameId' => \SORT_DESC]);
        break;
    }

    if ($type === 'incomplete-release-date') {
      if ($this->request->get('date_start')) {
        $mainQuery->andWhere(['>=', 'stopgame_info.GameDate', $this->request->get('date_start')]);
      }
      if ($this->request->get('date_end')) {
        $mainQuery->andWhere(['<=', 'stopgame_info.GameDate', $this->request->get('date_end')]);
      }
    }

    $gamesDataProvider = new ActiveDataProvider([
      'query' => $mainQuery,
    ]);

    return $this->render(
      'unfinished',
      \compact(
        'gamesDataProvider',
        'searchModel',
        'counts',
        'type',
        'sort',
      ),
    );
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function
  actionRequests(
    string $section = 'new',
    string $filter = 'editorial',
  ): string {
    $requests = match ($section) {
      'images' => GameImageRequest::find()
        ->joinWith('user')
        ->where(['game_image_request.status' => GameImageRequest::STATUS_NEW])
        ->orderBy(['itaf_user.credibility' => \SORT_DESC]),
      'fix' => GameFixSuggestion::find()
        ->joinWith('game')
        ->joinWith('game.mainTitle')
        ->joinWith('user')
        ->where([
          'game_fix_suggestion.status' => GameFixSuggestion::STATUS_NEW,
        ])
        ->andWhere(['NOT', ['stopgame_info.Gameid' => null]])
        ->groupBy('game_id')
        ->andFilterWhere(['like', 'stopgame_title.GameName', $this->request->get('term')])
        ->orderBy(['itaf_user.credibility' => \SORT_DESC]),
      'games-popularity' => GameRequest::find()
        ->joinWith('user')
        ->leftJoin([
          'counts' => new Query()
            ->select(['title', 'count' => new Expression('COUNT(*)')])
            ->from([
              't' => GameRequest::find()
                ->select(['title', 'user_id'])
                ->where(['game_id' => null, 'closed_by' => null])
                ->groupBy(['title', 'user_id']),
            ])
            ->groupBy('title'),
        ], 'counts.title = game_requests.title')
        ->where(['game_id' => null, 'closed_by' => null])
        ->andWhere(['>', 'counts.count', 1])
        ->andFilterWhere(['like', 'game_requests.title', $this->request->get('term')])
        ->orderBy(['counts.count' => \SORT_DESC, 'title' => \SORT_ASC]),
      'games-existing' => GameRequest::find()
        ->joinWith('user')
        ->leftJoin('stopgame_title', 'stopgame_title.GameName = game_requests.title')
        ->leftJoin([
          'stores' => new Expression(
            "JSON_TABLE(game_requests.stores, '$[*]' COLUMNS (store VARCHAR(255) PATH '$.store', store_id VARCHAR(255) PATH '$.store_id'))"
          ),
        ], '1 = 1')
        ->leftJoin(
          [new Expression('`game_store_attachments` FORCE INDEX (game_store_attachments_store_store_id_index)')],
          'game_store_attachments.store = stores.store AND game_store_attachments.store_id = stores.store_id'
        )
        ->where(['game_requests.game_id' => null, 'closed_by' => null])
        ->andWhere([
          'OR',
          ['NOT', ['stopgame_title.GameName' => null]],
          ['NOT', ['game_store_attachments.game_id' => null]],
        ])
        ->andFilterWhere(['like', 'title', $this->request->get('term')])
        ->orderBy(['title' => \SORT_ASC]),
      'games-with-steam' => GameRequest::find()
        ->joinWith('user')
        ->leftJoin([
          'stores' => new Expression(
            "JSON_TABLE(game_requests.stores, '$[*]' COLUMNS (store VARCHAR(255) PATH '$.store', store_id VARCHAR(255) PATH '$.store_id'))"
          ),
        ], '1 = 1')
        ->where(['game_id' => null, 'closed_by' => null])
        ->andWhere(['store' => 'steam'])
        ->andFilterWhere(['like', 'title', $this->request->get('term')])
        ->orderBy(['itaf_user.credibility' => \SORT_DESC, 'title' => \SORT_ASC]),
      default => GameRequest::find()
        ->joinWith('user')
        ->where(['game_id' => null, 'closed_by' => null])
        ->andFilterWhere(['like', 'title', $this->request->get('term')])
        ->orderBy(['itaf_user.credibility' => \SORT_DESC, 'title' => \SORT_ASC]),
    };

    $editorialQuery = (clone($requests))
      ->joinWith('user.teamInfo')
      ->andWhere(['ex' => 0]);
    $editorialCount = (clone($editorialQuery))
      ->groupBy(null)
      ->count();

    $usersQuery = (clone($requests))
      ->joinWith('user.teamInfo')
      ->andWhere([
        'OR',
        ['user_team_info.user_id' => null],
        ['ex' => 1],
      ]);
    $usersCount = (clone($usersQuery))
      ->groupBy(null)
      ->count();

    /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
    switch ($filter) {
      case 'editorial':
        $requests = $editorialQuery;
        break;
      case 'users':
        $requests = $usersQuery;
        break;
    }

    $dataProvider = new ActiveDataProvider([
      'query' => $requests,
    ]);
    $dataProvider->setSort(
      new Sort([
        'defaultOrder' => ['id' => \SORT_ASC],
      ]),
    );

    $currentRequests = [];
    if ($section === 'fix') {
      /** @var GameFixSuggestion[] $currentPage */
      $currentPage = $dataProvider->getModels();
      $gameIds = [];
      foreach ($currentPage as $request) {
        $gameIds[] = $request->game_id;
      }
      unset($currentPage);
      /** @var GameFixSuggestion[] $requestsItems */
      $requestsItems = (clone($requests))
        ->andWhere(['game_id' => $gameIds])
        ->groupBy(null)
        ->all();
      foreach ($requestsItems as $request) {
        if (!isset($currentRequests[$request->game_id])) {
          $currentRequests[$request->game_id] = [];
        }
        $currentRequests[$request->game_id][] = $request;
      }
      unset($requestsItems);
    }

    if (\in_array($section, ['new', 'games-popularity', 'games-existing', 'games-with-steam'])) {
      $dataProvider->setSort(
        new Sort([
          'defaultOrder' => null,
        ]),
      );

      Yii::$app->session->set('last_requests_url', Url::current());
    }

    return $this->render(
      'requests',
      \compact('dataProvider', 'section', 'editorialCount', 'usersCount', 'filter', 'currentRequests'),
    );
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionAcceptImageRequest(string $id): Response
  {
    GameImageRequestHelper::accept($id);

    Yii::$app->session->setFlash('success', 'Новая обложка принята');

    if ($this->request->referrer) {
      return $this->redirect($this->request->referrer);
    }
    return $this->redirect(['requests', 'section' => 'images']);
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionRejectImageRequest(string $id): Response
  {
    GameImageRequestHelper::reject($id);

    Yii::$app->session->setFlash('success', 'Новая обложка отклонена');

    if ($this->request->referrer) {
      return $this->redirect($this->request->referrer);
    }
    return $this->redirect(['requests', 'section' => 'images']);
  }

  /**
   * @throws NotFoundHttpException
   * @throws StaleObjectException
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionSeriesDelete(int $id): Response
  {
    $series = GameSeries::findOne($id);
    if (empty($series)) {
      throw new NotFoundHttpException('Серия не найдена');
    }

    $subQuery = GameSeriesGame::find()
      ->select(['game_id'])
      ->where(['game_series_id' => $id]);
    GameRelation::deleteAll(['AND', ['first_game_id' => $subQuery], ['second_game_id' => $subQuery]]);
    GameSeriesGame::deleteAll(['game_series_id' => $id]);
    GameSeriesRelation::deleteAll(['OR', ['first_series' => $id], ['second_series' => $id]]);
    $series->delete();

    return $this->redirect($this->request->referrer ?? '/admin/game-series');
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionSimilarRequests(int $id): Response|string
  {
    $game = Game::findOne($id);

    if (!$game || !$game->mainTitle) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    $options = [];
    $oldRequestId = $this->request->get('oldRequestId');
    if (!empty($oldRequestId)) {
      $options['filter'] = 'id != ' . $oldRequestId;
    }

    $meili = Yii::$app->meili;
    $index = $meili->index('game_requests');
    $term = SgStringHelper::strtoloweru($game->mainTitle->GameName ?? '');
    $search = $index->search($term, $options);
    $requestIds = ArrayHelper::getColumn($search->getHits(), 'id');
    if (\count($requestIds) > 0) {
      $requests = GameRequest::find()
        ->where(['id' => $requestIds])
        ->orderBy(new Expression('FIELD(id, ' . \implode(',', $requestIds) . ')'))
        ->all();
    } else {
      $requests = [];
    }

    return $this->render('similar-requests', \compact('game', 'requests'));
  }

  /**
   * @throws InvalidConfigException
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionCancelled(): string
  {
    $gamesDataProvider = new ActiveDataProvider([
      'query' => GameTitle::find()
        ->joinWith('info')
        ->where([
          'stopgame_info.status' => GameStatus::CANCELLED->value,
          'GameNamePrimary' => 1,
        ])
        ->orderBy(['stopgame_info.GameId' => \SORT_ASC]),
    ]);

    return $this->render('cancelled', \compact('gamesDataProvider'));
  }
}
