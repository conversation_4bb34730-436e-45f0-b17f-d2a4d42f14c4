<?php

namespace frontend\controllers;

use Yii;
use Throwable;
use common\widgets\SVG;
use common\models\{Faq,
  Game,
  Blog,
  GameTag,
  User,
  Topic,
  Track,
  Article,
  Comment,
  NewsData,
  Favourite,
  Compilation,
  GameRequest,
  UserActivity,
  UserMailChange,
  CompilationItem,
  BlogSubscription,
  GameImageRequest,
  UserFavouriteGame,
  GameFixSuggestion
};
use ReflectionException;
use Random\RandomException;
use frontend\traits\Access;
use frontend\components\View;
use common\jobs\DeleteUserJob;
use common\components\ModelLoader;
use yii\helpers\{Url, ArrayHelper};
use yii\base\{Action, InvalidConfigException};
use yii\data\{Pagination, ActiveDataProvider};
use common\enums\{UserActivityType, GameLibraryStatus};
use common\og_generators\{OGManager, ProfileOgGenerator};
use yii\db\{ActiveQueryInterface, Exception, Query, Expression, ActiveQuery, StaleObjectException};
use yii\web\{Request, Response, Controller, NotFoundHttpException, ForbiddenHttpException, BadRequestHttpException};
use common\helpers\SectionsHelper;

/**
 * @property-read Request $request
 * @property-read View $view
 * @noinspection PhpUnused
 */
class UserController extends Controller
{
  public ?User $user = null;
  public $layout = '@frontend/views/v9/user/default-layout.php';

  /**
   * @param Action<UserController> $action
   * @param array<string, mixed> $params
   * @return array<string, mixed>
   * @throws NotFoundHttpException
   * @throws BadRequestHttpException
   */
  #[\Override]
  public function bindActionParams($action, $params): array
  {
    if (!empty($params['userName'])) {
      $this->user = User::findOne(['user_name' => $params['userName']]);
    } else {
      $this->user = Yii::$app->user->identity;
    }
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }
    return parent::bindActionParams($action, $params);
  }

  /**
   * @param array<string, mixed> $params
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws \Exception
   */
  #[\Override]
  public function render($view, $params = []): string
  {
    $user = $this->user;
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }
    $userName = $user->userName;

    $title = ($this->view->params['pageTitle'] ?? $userName);
    $description = $this->view->params['description'] ?? "$userName - Все данные пользователя StopGame";

    $this->view->title = $title . ' // Пользователь StopGame';
    $this->view->registerMetaTag(['name' => 'keywords', 'content' => "$userName, пользователь StopGame"]);
    $this->view->registerMetaTag(
      ['name' => 'description', 'content' => $description],
    );

    $ogManager = new OGManager([
      'generator' => ProfileOgGenerator::class,
      'generatorParams' => \compact('user'),
      'preparation' => static function (array $params) {
        $params['games'] = $params['user']->getTracks()
          ->where(['track.status' => GameLibraryStatus::PLAYING->value])
          ->limit(3)
          ->orderBy(['track_date' => \SORT_DESC])
          ->all();
        return $params;
      },
      'fileName' => $user->user_name,
      'directory' => 'users'
    ]);
    $ogManager();

    $this->view->registerOG([
      'og' => [
        'image' => [
          'url' => $ogManager->url
        ],
        'title' => $title,
        'description' => $description,
        'profile' => [
          'username' => $userName,
          'gender' => ($user->user_gender === 'w') ? 'female' : 'male'
        ]
      ]
    ]);

    $totalComments = $user
      ->getComments()
      ->count();
    $totalPubs = $user->totalPubs();
    $totalBlogsQuery = $user->getTopics();

    if (($user->id !== Yii::$app->user->id) && !Yii::$app->user->can('moderate_users')) {
      $totalBlogsQuery
        ->where([
          'topic_publish' => 1,
          'topic_deleted' => 0,
        ]);
    }

    $totalBlogs = $totalBlogsQuery->count();
    $totalFaqs = $user
      ->getFaqs()
      ->where(['faq_deleted' => 0])
      ->count();
    // $totalAchievements = $user->getAchievements()->count();
    $totalWishlist = (int)$user
      ->getTracks()
      ->where(['track.status' => GameLibraryStatus::WISHLIST->value])
      ->count();
    $totalPlaying = (int)$user
      ->getTracks()
      ->where(['track.status' => GameLibraryStatus::PLAYING->value])
      ->count();
    $totalBeaten = (int)$user
      ->getTracks()
      ->where(['track.status' => GameLibraryStatus::BEATEN->value])
      ->count();
    $totalTrashed = (int)$user
      ->getTracks()
      ->where(['track.status' => GameLibraryStatus::TRASHED->value])
      ->count();
    $totalFavourite = (int)UserFavouriteGame::find()
      ->where(['user_id' => $user->id])
      ->count();
    $totalTracked = $totalWishlist + $totalPlaying + $totalBeaten + $totalTrashed;
    $totalSubscribers = 0;
    $subscribed = $user->isSubscribed;
    if ($user->personalBlog) {
      $totalSubscribers = $user->personalBlog->getSubscribedUsers()->count();
    }
    $totalReviews = $user
      ->getReviews()
      ->where(['deleted' => 0])
      ->count();

    $totalRated = $user->getVotes()->rightJoin('stopgame_title', 'GameId = target_id')->andWhere([
      'target_type' => 'game',
      'GameNamePrimary' => 1,
    ])->count();

    $totalGames = $user
      ->getTracks()->select('GameId')
      ->union(
        $user
          ->getVotes()
          ->select(['GameId' => 'target_id'])
          ->innerJoin('stopgame_info', 'GameId = target_id')
          ->andWhere(['target_type' => 'game'])
          ->andWhere(['>', 'vote_value', 0]),
      )
      ->union(
        UserFavouriteGame::find()
          ->select(['GameId' => 'game_id'])
          ->innerJoin('stopgame_info', 'GameId = game_id')
          ->andWhere(['user_id' => $user->id]),
      )
      ->distinct()->count();

    $totalCompilationsQuery = Compilation::find()
      ->where([
        'user_id' => $user->id,
        'type' => Compilation::TYPE_UGC,
        'deleted' => 0,
      ]);

    if (Yii::$app->user->id !== $user->id) {
      $totalCompilationsQuery->andWhere(['public' => [1, 2]]);
    }

    $totalCompilations = $totalCompilationsQuery->count();

    $blogWins = Topic::find()
      ->where([
        'user_id' => $user->id,
        'topic_on_main' => 1,
        'topic_publish' => 1,
        'topic_deleted' => 0,
      ])
      ->count();

    $tabs = [
      [
        'label' => 'Главная',
        'url' => ['/user/index', 'userName' => $user->user_name],
        'active' => Yii::$app->requestedRoute === 'user/index',
      ],
    ];

    if (($totalTracked > 0) || ($totalRated > 0)) {
      $tabs[] = [
        'label' => 'Игры',
        'url' => ['/user/games', 'userName' => $user->user_name],
        'active' => Yii::$app->requestedRoute === 'user/games',
        'sup' => $totalGames,
      ];
    }

    $tabs[] = [
      'label' => 'Подборки игр',
      'url' => ['/user/compilations', 'userName' => $user->user_name],
      'active' => Yii::$app->requestedRoute === 'user/compilations',
      'sup' => $totalCompilations,
    ];

    $currentUser = Yii::$app->user;

    if ($currentUser->id === $user->id) {
      $tabs[] = [
        'label' => 'Закладки',
        'url' => ['/favourite'],
        'active' => Yii::$app->requestedRoute === 'user/favourite',
      ];
    }

    if ($totalBlogs > 0) {
      $tabs[] = [
        'label' => 'Блоги',
        'url' => ['/user/blogs', 'userName' => $user->user_name],
        'active' => Yii::$app->requestedRoute === 'user/blogs',
        'sup' => $totalBlogs,
      ];
    }

    if ($totalReviews > 0) {
      $tabs[] = [
        'label' => 'Отзывы',
        'url' => ['/user/reviews', 'userName' => $user->user_name],
        'active' => Yii::$app->requestedRoute === 'user/reviews',
        'sup' => $totalReviews,
      ];
    }

    if ($totalPubs > 0) {
      $tabs[] = [
        'label' => 'Публикации',
        'url' => ['/user/pubs', 'userName' => $user->user_name],
        'active' => Yii::$app->requestedRoute === 'user/pubs',
        'sup' => $totalPubs,
      ];
    }

    if ($totalComments > 0) {
      $tabs[] = [
        'label' => 'Комментарии',
        'url' => ['/user/comments', 'userName' => $user->user_name],
        'active' => Yii::$app->requestedRoute === 'user/comments',
        'sup' => $totalComments,
      ];
    }

    /*if ($totalAchievements > 0) {
        $tabs[] = [
            'label' => 'Награды',
            'url' => ['/user/achievements', 'userName' => $user->user_name],
            'active' => Yii::$app->requestedRoute === 'user/achievements'
        ];
    }*/

    $tabs[] = [
      'label' => 'Подписки',
      'url' => ['/user/subscriptions', 'userName' => $user->user_name],
      'active' => Yii::$app->requestedRoute === 'user/subscriptions',
    ];

    $this->view->params = ArrayHelper::merge(
      $this->view->params,
      \compact(
        'user',
        'totalComments',
        'totalPubs',
        'totalBlogs',
        /*'totalAchievements',*/
        'totalTracked',
        'totalRated',
        'blogWins',
        'totalFaqs',
        'totalSubscribers',
        'subscribed',
        'totalReviews',
        'totalGames',
        'totalWishlist',
        'totalPlaying',
        'totalBeaten',
        'totalTrashed',
        'totalCompilations',
        'totalFavourite',
        'tabs',
      ),
    );

    return parent::render($view, $params);
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   */
  public function actionIndex(): string
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $this->layout = '@frontend/views/v9/user/main-layout.php';

    $nowPlaying = Game::find()
      ->with('mainTitle')
      ->rightJoin([
        'tracks' => $this->user
          ->getTracks()
          ->where(['track.status' => GameLibraryStatus::PLAYING->value]),
      ], 'tracks.GameId = stopgame_info.GameId')
      ->orderBy(['track_date' => \SORT_DESC])
      ->limit(8)
      ->all();

    $favouriteGames = Game::find()
      ->with('mainTitle')
      ->rightJoin([
        'favourites' => UserFavouriteGame::find()
          ->where(['user_id' => $this->user->id]),
      ], 'favourites.game_id = stopgame_info.GameId')
      ->orderBy(['favourites.created_at' => \SORT_DESC])
      ->limit(8)
      ->all();

    $favouritesCount = UserFavouriteGame::find()
      ->where(['user_id' => $this->user->id])
      ->count();

    return $this->render(
      '@frontend/views/v9/user/index',
      \compact('nowPlaying', 'favouritesCount', 'favouriteGames'),
    );
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  public function actionComments(int $page = 1): string
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $this->view->params['pageTitle'] = 'Комментарии пользователя ' . $this->user->userName;
    if ($this->user->isCurrentUser) {
      $this->view->params['pageTitle'] = 'Мои комментарии';
    }

    $startSelect = ($page - 1) * 20;
    $comments = $this->user
      ->getComments()
      ->orderBy(['comment_date' => \SORT_DESC])
      ->offset($startSelect)
      ->limit(20)
      ->all();

    if ($page > 1) {
      $this->view->params['subtitle'] = 'Страница ' . $page;
    }

    return $this->render('@frontend/views/v9/user/comments', \compact('comments', 'page'));
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  public function actionBlogs(string $section = 'published', int $page = 1): string|Response
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $this->view->params['pageTitle'] = 'Блоги пользователя ' . $this->user->userName;
    if ($this->user->isCurrentUser) {
      $this->view->params['pageTitle'] = 'Мои блоги';
    }

    $blogsQuery = $this->user
      ->getTopics()
      ->orderBy(['topic_date_published' => \SORT_DESC]);

    $publishedQuery = (clone $blogsQuery)
      ->where([
        'topic_deleted' => 0,
        'topic_publish' => 1,
      ]);

    $draftsQuery = (clone $blogsQuery)
      ->where([
        'topic_deleted' => 0,
        'topic_publish' => 0,
      ]);

    $deletedQuery = (clone $blogsQuery)
      ->where([
        'topic_deleted' => 1,
      ]);

    $counts = [];

    if (($section !== 'published') && !$this->user->isCurrentUser && !Yii::$app->user->can('moderate_users')) {
      return $this->redirect(['user/blogs', 'userName' => $this->user->user_name]);
    }

    if ($this->user->isCurrentUser || Yii::$app->user->can('moderate_users')) {
      $counts = [
        'published' => (clone $publishedQuery)->count(),
        'drafts' => (clone $draftsQuery)->count(),
        'deleted' => (clone $deletedQuery)->count(),
      ];
    }

    $blogsQuery = match ($section) {
      'draft' => $draftsQuery,
      'deleted' => $deletedQuery,
      default => $publishedQuery
    };

    $dataProvider = new ActiveDataProvider([
      'query' => $blogsQuery,
    ]);
    $dataProvider->setPagination(
      new Pagination([
        'defaultPageSize' => 12,
      ]),
    );

    $models = $dataProvider->getModels();
    $ids = ArrayHelper::getColumn($models, 'topic_id');
    $readData = $this->user
      ->getReadData()
      ->where([
        'target_type' => 'topic',
        'target_id' => $ids,
      ])
      ->indexBy('target_id')
      ->all();

    return $this->render(
      '@frontend/views/v9/user/blogs',
      \compact('dataProvider', 'readData', 'section', 'page', 'counts'),
    );
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  public function actionReviews(int $page = 1, string $sort = 'date', string $section = 'published'): string|Response
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $user = $this->user;

    $this->view->params['pageTitle'] = 'Отзывы к играм от пользователя ' . $user->userName;
    if ($this->user->isCurrentUser) {
      $this->view->params['pageTitle'] = 'Мои отзывы';
    }

    $query = $user->getReviews()
      ->joinWith('gameRating');

    $publishedQuery = (clone $query)
      ->where(['deleted' => 0]);
    $deletedQuery = (clone $query)
      ->where(['deleted' => 1])
      ->andWhere(['deleted_final' => 0]);

    $publishedCount = (clone $publishedQuery)->count();
    $deletedCount = (clone $deletedQuery)->count();

    if (($section === 'deleted') && ($deletedCount === 0)) {
      return $this->redirect(['user/reviews', 'userName' => $user->user_name]);
    }

    if ($section === 'published') {
      $query = $publishedQuery;
    } elseif ($section === 'deleted') {
      $query = $deletedQuery;
    }
    $dataProvider = new ActiveDataProvider([
      'query' => $query,
      'sort' => [
        'defaultOrder' => ['date' => \SORT_ASC],
        'attributes' => [
          'date' => [
            'asc' => ['created_at' => \SORT_DESC],
            'desc' => ['created_at' => \SORT_ASC],
          ],
          'rating' => [
            'asc' => ['rating' => \SORT_DESC],
          ],
          'vote' => [
            'asc' => ['vote_value' => \SORT_DESC, 'created_at' => \SORT_DESC]
          ]
        ],
      ],
    ]);
    $dataProvider->setPagination(
      new Pagination([
        'defaultPageSize' => 30,
      ]),
    );

    $models = $dataProvider->getModels();
    $ids = ArrayHelper::getColumn($models, 'id');

    $readInfo = [];
    if (Yii::$app->user->isLoggedIn) {
      $readInfo = Yii::$app->user->identity
        ->getReadData()
        ->where([
          'target_type' => 'user_review',
          'target_id' => $ids,
        ])
        ->indexBy('target_id')
        ->all();
    }

    return $this->render(
      '@frontend/views/v9/user/reviews',
      \compact('dataProvider', 'page', 'readInfo', 'sort', 'publishedCount', 'deletedCount', 'user', 'section'),
    );
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  public function actionPubs(int $page = 1, string $section = 'all'): string
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $this->view->params['pageTitle'] = 'Публикации за авторством пользователя ' . $this->user->userName;
    if ($this->user->isCurrentUser) {
      $this->view->params['pageTitle'] = 'Мои публикации';
    }

    $baseArticlesQuery = Article::find()
      ->select(['id' => 'base_id', 'type' => new Expression('"show"'), 'data_add'])
      ->where([
        'user_id' => $this->user->id,
        'data_active' => 1,
        'CommentId' => null,
      ])
      ->andWhere([
        'NOT',
        [
          'data_type' => [
            'infact',
            'podcast',
            'live',
            'cheats',
            'trainers',
            'trailers',
          ],
        ],
      ]);

    $newsQuery = NewsData::find()
      ->select(['id' => 'news_id', 'type' => new Expression('"news"'), 'data_add' => 'news_date'])
      ->where([
        'user_id' => $this->user->id,
        'news_active' => 1,
      ]);

    $allQuery = new Query()
      ->from((clone $newsQuery)->union(clone $baseArticlesQuery));

    $articlesQuery = (clone $baseArticlesQuery)
      ->andWhere([
        'data_type' => SectionsHelper::getArticleSections(),
        'video_attr' => null,
      ]);

    $videoQuery = (clone $baseArticlesQuery)
      ->andWhere([
        'data_type' => [...SectionsHelper::getVideoSections(), 'review', 'preview'],
      ])
      ->andWhere(['NOT', ['video_attr' => null,]]);

    $query = match ($section) {
      'news' => $newsQuery->asArray(),
      'articles' => $articlesQuery->asArray(),
      'video' => $videoQuery->asArray(),
      default => $allQuery
    };

    $counts = [
      'all' => (clone $allQuery)->count(),
      'news' => (clone $newsQuery)->count(),
      'articles' => (clone $articlesQuery)->count(),
      'video' => (clone $videoQuery)->count(),
    ];

    $dataProvider = new ActiveDataProvider([
      'query' => $query
        ->orderBy(['data_add' => \SORT_DESC]),
      'key' => 'id',
    ]);
    $dataProvider->setPagination(
      new Pagination([
        'defaultPageSize' => 30,
      ]),
    );

    $models = $dataProvider->getModels();

    $newsIds = [];
    $articlesIds = [];

    foreach ($models as $model) {
      /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
      switch ($model['type']) {
        case 'news':
          $newsIds[] = $model['id'];
          break;
        case 'show':
          $articlesIds[] = $model['id'];
          break;
      }
    }

    $news = NewsData::find()
      ->where(['news_id' => $newsIds])
      ->indexBy('news_id')
      ->all();
    $articles = Article::find()
      ->where(['base_id' => $articlesIds])
      ->indexBy('base_id')
      ->all();

    $readData = [];

    if (Yii::$app->user->isLoggedIn) {
      $readData = [
        'news' => Yii::$app->user->identity
          ->getReadData()
          ->where([
            'target_type' => 'news',
            'target_id' => $newsIds,
          ])
          ->indexBy('target_id')
          ->all(),
        'show' => Yii::$app->user->identity
          ->getReadData()
          ->where([
            'target_type' => 'show',
            'target_id' => $articlesIds,
          ])
          ->indexBy('target_id')
          ->all(),
      ];
    }

    if ($page > 1) {
      $this->view->params['subtitle'] = 'Страница ' . $page;
    }

    return $this->render(
      '@frontend/views/v9/user/publications',
      \compact('dataProvider', 'news', 'articles', 'page', 'readData', 'counts', 'section'),
    );
  }

  /**
   * @param array<string, string|string[]> $filters
   * @throws Exception
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws RandomException
   * @throws ReflectionException
   * @noinspection PhpUnused
   */
  public function actionGames(
    int $page = 1,
    string $section = 'all',
    ?string $subsection = null,
    ?string $sort = 'default',
    ?array $filters = [],
  ): Response|string {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    if (!empty($this->request->get('grid_type'))) {
      if (Yii::$app->user->isLoggedIn) {
        Yii::$app->user->settings->profile_grid_type = $this->request->get('grid_type');
      }
      return $this->redirect(Url::current(['grid_type' => null]));
    }

    $this->layout = '@frontend/views/v9/user/games-layout.php';

    $pageTitle = 'Мои игры';
    $activeFiltersCount = 0;

    $gamesQuery = $this->user
      ->getTracks()
      ->select(
        [
          '*',
          'RealDate' => new Expression(
            "IF (GameDate = '0000-00-00', '9999-01-01', REPLACE(REPLACE(GameDate, '-00-00', '-12-31'), '-00', '-30'))",
          ),
        ],
      )
      ->joinWith(['game', 'game.mainTitle'])
      ->with(['game.platforms', 'game.rating', 'game.tags', 'game.currentUserTrack', 'game.currentUserVote'])
      ->andWhere(['NOT', ['stopgame_info.GameId' => null]]);

    $availableOrders = [
      'latest' => [
        'label' => 'Последние добавленные',
        'order' => ['track_date' => \SORT_DESC],
      ],
      'date' => [
        'label' => 'По дате (от новых)',
        'order' => ['RealDate' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
      ],
      '-date' => [
        'label' => 'По дате (от старых)',
        'order' => ['RealDate' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
      ],
      'rank' => [
        'label' => 'По оценке',
        'query' => fn(ActiveQuery $query) => $query->leftJoin(
          'ls_vote',
          "user_voter_id = :userId AND target_type='game' AND target_id=track.GameId",
          [':userId' => $this->user->id],
        )->select(['*', 'vote_value' => 'ls_vote.vote_value']),
        'order' => ['vote_value' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
      ],
      'rating' => [
        'label' => 'По рейтингу',
        'query' => static fn(ActiveQuery $query) => $query->joinWith('game.rating')->select(
          ['*', 'rating' => new Expression('ROUND((RateCount/RateTotal)/2, 1)')],
        ),
        'order' => ['rating' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
      ],
      'popularity' => [
        'label' => 'По популярности',
        'query' => [$this, 'addPopularityJoin'],
        'order' => ['popularity' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
      ],
      'name' => [
        'label' => 'По алфавиту',
        'order' => ['stopgame_title.GameName' => \SORT_ASC],
      ],
    ];

    if ($section === 'wishlist') {
      unset($availableOrders['rank']);
    }

    $defaultOrder = 'latest';

    $innerTabs = [];
    $plainRequest = false;

    /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
    switch ($section) {
      case 'wishlist':
        $gamesQuery->andWhere(['track.status' => GameLibraryStatus::WISHLIST->value]);
        $pageTitle = 'Буду играть';
        if (!\in_array($subsection, ['released', 'unreleased'])) {
          $subsection = 'released';
        }

        $currentYear = \date('Y');
        $currentMonth = \date('m');

        $releasedQuery = (clone $gamesQuery)
          ->andWhere(['<=', 'GameDate', new Expression('NOW()')])
          ->andWhere(['NOT', ['GameDate' => '0000-00-00']])
          ->andWhere(['NOT', ['GameDate' => "$currentYear-00-00"]])
          ->andWhere(['NOT', ['GameDate' => "$currentYear-$currentMonth-00"]]);
        $unreleasedQuery = (clone $gamesQuery)
          ->andWhere([
            'OR',
            ['>', 'GameDate', new Expression('NOW()')],
            ['GameDate' => "$currentYear-00-00"],
            ['GameDate' => "$currentYear-$currentMonth-00"],
            ['GameDate' => '0000-00-00'],
          ]);

        $innerTabs = [
          [
            'label' => 'Уже вышли',
            'url' => Url::to(['user/games', 'userName' => $this->user->user_name, 'section' => 'wishlist']),
            'active' => $subsection === 'released',
            'sup' => (clone $releasedQuery)
              ->count(),
          ],
          [
            'label' => 'Выйдут',
            'url' => Url::to(
              [
                'user/games',
                'userName' => $this->user->user_name,
                'section' => 'wishlist',
                'subsection' => 'unreleased',
              ],
            ),
            'active' => $subsection === 'unreleased',
            'sup' => (clone $unreleasedQuery)
              ->count(),
          ],
        ];
        if ($subsection === 'released') {
          $defaultOrder = 'date';
          $gamesQuery = $releasedQuery;
        } elseif ($subsection === 'unreleased') {
          $gamesQuery = $unreleasedQuery;
          $availableOrders = [
            'latest' => [
              'label' => 'Последние добавленные',
              'order' => ['track_date' => \SORT_DESC],
            ],
            'date' => [
              'label' => 'По дате (от новых)',
              'order' => ['RealDate' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
            ],
            '-date' => [
              'label' => 'По дате (от ближайших)',
              'order' => ['RealDate' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
            ],
            'popularity' => [
              'label' => 'По популярности',
              'query' => [$this, 'addPopularityJoin'],
              'order' => ['popularity' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
            ],
            'name' => [
              'label' => 'По алфавиту',
              'order' => ['GameName' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
            ],
          ];
          $defaultOrder = '-date';
        }
        break;
      case 'playing':
        $gamesQuery->andWhere(['track.status' => GameLibraryStatus::PLAYING->value]);
        $pageTitle = 'Играю';
        break;
      case 'beaten':
        $gamesQuery
          ->andWhere(['track.status' => GameLibraryStatus::BEATEN->value]);
        $pageTitle = 'Пройдено';
        $availableOrders = [
          'latest' => [
            'label' => 'Последние пройденные',
            'query' => fn(ActiveQuery $query) => $query->leftJoin(table: [
              'beaten' => UserActivity::find()
                ->select(['game_id', 'beaten_date' => new Expression('MAX(date)')])
                ->where([
                  'user_id' => $this->user->id,
                  'action' => UserActivityType::ADD_TO_BEATEN->value,
                ])
                ->groupBy('game_id'),
            ], on: 'beaten.game_id = stopgame_info.GameId'),
            'order' => ['beaten_date' => \SORT_DESC, 'track_date' => \SORT_DESC],
          ],
          'date' => [
            'label' => 'По дате (от новых)',
            'order' => ['RealDate' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          '-date' => [
            'label' => 'По дате (от старых)',
            'order' => ['RealDate' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rank' => [
            'label' => 'По оценке',
            'query' => fn(ActiveQuery $query) => $query->leftJoin(
              'ls_vote',
              "user_voter_id = :userId AND target_type='game' AND target_id=track.GameId",
              [':userId' => $this->user->id],
            )->select(['*', 'vote_value' => 'ls_vote.vote_value']),
            'order' => ['vote_value' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rating' => [
            'label' => 'По рейтингу',
            'query' => static fn(ActiveQuery $query) => $query->joinWith('game.rating')->select(
              ['*', 'rating' => new Expression('ROUND((RateCount/RateTotal)/2, 1)')],
            ),
            'order' => ['rating' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'popularity' => [
            'label' => 'По популярности',
            'query' => [$this, 'addPopularityJoin'],
            'order' => ['popularity' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'name' => [
            'label' => 'По алфавиту',
            'order' => ['stopgame_title.GameName' => \SORT_ASC],
          ],
        ];
        break;
      case 'trashed':
        $gamesQuery->andWhere(['track.status' => GameLibraryStatus::TRASHED->value]);
        $pageTitle = 'Заброшено';
        break;
      case 'rated':
        $plainRequest = true;
        $gamesQuery = Game::find()
          ->select(
            [
              '*',
              'RealDate' => new Expression(
                "IF (GameDate = '0000-00-00', '9999-01-01', REPLACE(REPLACE(GameDate, '-00-00', '-12-31'), '-00', '-30'))",
              ),
            ],
          )
          ->joinWith('mainTitle')
          ->with(['platforms', 'rating'])
          ->innerJoinWith([
            'userVote' => fn($query) => $query
              ->andWhere(['user_voter_id' => $this->user->id])
              ->andWhere(['>', 'vote_value', 0]),
          ]);
        $pageTitle = 'Оценённые';
        $availableOrders = [
          'latest' => [
            'label' => 'Последние оценённые',
            'order' => ['vote_date' => \SORT_DESC],
          ],
          'date' => [
            'label' => 'По дате (от новых)',
            'order' => ['RealDate' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          '-date' => [
            'label' => 'По дате (от старых)',
            'order' => ['RealDate' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rank' => [
            'label' => 'По оценке',
            'order' => ['vote_value' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rating' => [
            'label' => 'По рейтингу',
            'query' => static fn(ActiveQuery $query) => $query->joinWith('rating')->select(
              ['*', 'rating' => new Expression('ROUND((RateCount/RateTotal)/2, 1)')],
            ),
            'order' => ['rating' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'popularity' => [
            'label' => 'По популярности',
            'query' => [$this, 'addPopularityJoin'],
            'order' => ['popularity' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'name' => [
            'label' => 'По алфавиту',
            'order' => ['stopgame_title.GameName' => \SORT_ASC],
          ],
        ];
        break;
      case 'favourite':
        $plainRequest = true;
        $pageTitle = 'Любимые';
        $gamesQuery = Game::find()
          ->select(
            [
              '*',
              'RealDate' => new Expression(
                "IF (GameDate = '0000-00-00', '9999-01-01', REPLACE(REPLACE(GameDate, '-00-00', '-12-31'), '-00', '-30'))",
              ),
            ],
          )
          ->joinWith('mainTitle')
          ->innerJoin(
            UserFavouriteGame::tableName(),
            'user_favourite_game.game_id = stopgame_info.GameId AND user_id = :userId',
            [':userId' => $this->user->id],
          );
        $availableOrders = [
          'latest' => [
            'label' => 'Последние добавленные',
            'order' => ['user_favourite_game.created_at' => \SORT_DESC],
          ],
          'date' => [
            'label' => 'По дате (от новых)',
            'order' => ['RealDate' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          '-date' => [
            'label' => 'По дате (от старых)',
            'order' => ['RealDate' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rank' => [
            'label' => 'По оценке',
            'query' => fn(ActiveQuery $query) => $query->leftJoin(
              'ls_vote',
              "user_voter_id = :userVoterId AND target_type='game' AND target_id=stopgame_info.GameId",
              [':userVoterId' => $this->user->id],
            )->select(['*', 'vote_value' => 'ls_vote.vote_value']),
            'order' => ['vote_value' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rating' => [
            'label' => 'По рейтингу',
            'query' => static fn(ActiveQuery $query) => $query->joinWith('rating')->select(
              ['*', 'rating' => new Expression('ROUND((RateCount/RateTotal)/2, 1)')],
            ),
            'order' => ['rating' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'popularity' => [
            'label' => 'По популярности',
            'query' => [$this, 'addPopularityJoin'],
            'order' => ['popularity' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'name' => [
            'label' => 'По алфавиту',
            'query' => static fn(ActiveQuery $query) => $query->joinWith('mainTitle'),
            'order' => ['GameName' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
        ];
        break;
      case 'compilation':
        /** @var ?Compilation $compilation */
        $compilation = Compilation::find()->where([
          'id' => $subsection,
          'type' => Compilation::TYPE_UGC,
          'deleted' => 0,
          'user_id' => $this->user->id,
        ])->one();
        if (!$compilation || (($compilation->user_id !== Yii::$app->user->id) && ($compilation->public === 0))) {
          throw new NotFoundHttpException('Подборка не найдена');
        }
        $pageTitle = $compilation->title;
        $plainRequest = true;
        $gamesQuery = Game::find()
          ->select(
            [
              '*',
              'RealDate' => new Expression(
                "IF (GameDate = '0000-00-00', '9999-01-01', REPLACE(REPLACE(GameDate, '-00-00', '-12-31'), '-00', '-30'))",
              ),
            ],
          )
          ->joinWith('mainTitle')
          ->rightJoin([
            'comp_item' => CompilationItem::find()
              ->where([
                'compilation_id' => $compilation->id,
                'target_type' => 'game',
              ]),
          ], 'comp_item.target_id = stopgame_info.GameId');
        $availableOrders = [
          'compilation' => [
            'label' => 'Авторская сортировка',
            'order' => ['comp_item.order' => \SORT_ASC],
          ],
          'date' => [
            'label' => 'По дате (от новых)',
            'order' => ['RealDate' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          '-date' => [
            'label' => 'По дате (от старых)',
            'order' => ['RealDate' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rank' => [
            'label' => 'По оценке',
            'query' => fn(ActiveQuery $query) => $query->leftJoin(
              'ls_vote',
              "user_voter_id = :userVoterId AND ls_vote.target_type='game' AND ls_vote.target_id=stopgame_info.GameId",
              [':userVoterId' => $this->user->id],
            )->select(['*', 'vote_value' => 'ls_vote.vote_value']),
            'order' => ['vote_value' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rating' => [
            'label' => 'По рейтингу',
            'query' => static fn(ActiveQuery $query) => $query->joinWith('rating')->select(
              ['*', 'rating' => new Expression('ROUND((RateCount/RateTotal)/2, 1)')],
            ),
            'order' => ['rating' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'popularity' => [
            'label' => 'По популярности',
            'query' => [$this, 'addPopularityJoin'],
            'order' => ['popularity' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'name' => [
            'label' => 'По алфавиту',
            'order' => ['stopgame_title.GameName' => \SORT_ASC],
          ],
        ];
        $defaultOrder = 'compilation';
        break;
      case 'all':
        $plainRequest = true;
        $pageTitle = 'Все игры';
        $gamesQuery = Game::find()
          ->select(
            [
              'stopgame_info.*',
              'RealDate' => new Expression(
                "IF (GameDate = '0000-00-00', '9999-01-01', REPLACE(REPLACE(GameDate, '-00-00', '-12-31'), '-00', '-30'))",
              ),
            ],
          )
          ->innerJoinWith(['mainTitle'])
          ->with(['platforms', 'rating', 'tags', 'currentUserTrack', 'currentUserVote'])
          ->innerJoin(
            [
              'allIds' => $this->user
                ->getTracks()
                ->select(['GameId' => 'track.GameId'])
                ->innerJoin('stopgame_info', 'stopgame_info.GameId = track.GameId')
                ->union(
                  $this->user
                    ->getVotes()
                    ->select(['GameId' => 'target_id'])
                    ->innerJoin('stopgame_info', 'GameId = target_id')
                    ->andWhere(['target_type' => 'game'])
                    ->andWhere(['>', 'vote_value', 0]),
                )
                ->union(
                  UserFavouriteGame::find()
                    ->select(['GameId' => 'game_id'])
                    ->innerJoin('stopgame_info', 'GameId = game_id')
                    ->andWhere(['user_id' => $this->user->id]),
                )
                ->distinct(),
            ],
            'allIds.GameId = stopgame_info.GameId',
          );
        $availableOrders = [
          'latest' => [
            'label' => 'Последние добавленные',
            'query' => fn(ActiveQuery $query) => $query->leftJoin(['track' => $this->user->getTracks()],
              'track.GameId = stopgame_info.GameId'),
            'order' => ['track_date' => \SORT_DESC],
          ],
          'date' => [
            'label' => 'По дате (от новых)',
            'order' => ['RealDate' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          '-date' => [
            'label' => 'По дате (от старых)',
            'order' => ['RealDate' => \SORT_ASC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rank' => [
            'label' => 'По оценке',
            'query' => fn(ActiveQuery $query) => $query
              ->leftJoin([
                'ls_vote' => $this->user
                  ->getVotes()
                  ->from(new Expression('{{%ls_vote}} FORCE INDEX(user_type)'))
                  ->andWhere(['target_type' => 'game'])
                  ->andWhere(['>', 'vote_value', 0]),

              ], 'target_id=stopgame_info.GameId')
              ->select(
                [
                  'stopgame_info.*',
                  'RealDate' => new Expression(
                    "IF (GameDate = '0000-00-00', '9999-01-01', REPLACE(REPLACE(GameDate, '-00-00', '-12-31'), '-00', '-30'))",
                  ),
                  'vote_value' => 'ls_vote.vote_value',
                ],
              ),
            'order' => ['vote_value' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'rating' => [
            'label' => 'По рейтингу',
            'query' => static fn(ActiveQuery $query) => $query->joinWith('rating')->select(
              [
                'stopgame_info.*',
                'RealDate' => new Expression(
                  "IF (GameDate = '0000-00-00', '9999-01-01', REPLACE(REPLACE(GameDate, '-00-00', '-12-31'), '-00', '-30'))",
                ),
                'rating' => new Expression('ROUND((RateCount/RateTotal)/2, 1)'),
              ],
            ),
            'order' => ['rating' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'popularity' => [
            'label' => 'По популярности',
            'query' => [$this, 'addPopularityJoin'],
            'order' => ['popularity' => \SORT_DESC, 'stopgame_title.GameName' => \SORT_ASC],
          ],
          'name' => [
            'label' => 'По алфавиту',
            'order' => ['stopgame_title.GameName' => \SORT_ASC],
          ],
        ];
        break;
    }

    if (!empty($filters['title'])) {
      $relationName = 'game.titles';
      if ($plainRequest) {
        $relationName = 'titles';
      }
      $gamesQuery
        ->joinWith("$relationName altTitle")
        ->andWhere([
          'OR',
          ['LIKE', 'stopgame_title.GameName', $filters['title']],
          ['LIKE', 'altTitle.GameName', $filters['title']],
        ]);
      $activeFiltersCount++;
    }

    if (!empty($filters['platforms'])) {
      if (!\is_array($filters['platforms'])) {
        $filters['platforms'] = [$filters['platforms']];
      }
      $relationName = 'game.platformAttachments';
      if ($plainRequest) {
        $relationName = 'platformAttachments';
      }
      foreach ($filters['platforms'] as $i => $platform) {
        $relationId = 'platform_' . $i;
        $gamesQuery
          ->joinWith(["$relationName $relationId"])
          ->andWhere(["$relationId.platform_id" => $platform]);
      }
      $activeFiltersCount++;
    }

    if (!empty($filters['tags'])) {
      $tagIds = GameTag::find()
        ->select(['id', 'slug'])
        ->indexBy('slug')
        ->column();

      if (!\is_array($filters['tags'])) {
        $filters['tags'] = [$filters['tags']];
      }
      $relationName = 'game.gameTags';
      $typeRelationName = 'game.gameSeriesGames';
      if ($plainRequest) {
        $relationName = 'gameTags';
        $typeRelationName = 'gameSeriesGames';
      }
      /**
       * @var int $i
       * @var string $tag
       */
      foreach ($filters['tags'] as $i => $tag) {
        if ($tag === 'dlc') {
          $filters['tags'][$i] = 'series_type_1';
          $tag = 'series_type_1';
        }
        if (\str_starts_with($tag, 'series_type_')) {
          $tagId = (int)\str_replace('series_type_', '', $tag);
          $relationId = 'game_series_' . $i;
          $gamesQuery
            ->joinWith(["$typeRelationName $relationId"])
            ->andWhere(["$relationId.type" => $tagId]);
        } elseif (isset($tagIds[$tag])) {
          $tagId = $tagIds[$tag];
          $relationId = 'game_tags_' . $i;
          $gamesQuery
            ->joinWith(["$relationName $relationId"])
            ->andWhere(["$relationId.tag_id" => $tagId]);
        }
      }
      $activeFiltersCount++;
    }

    if (!empty($filters['stopRate'])) {
      if (!\is_array($filters['stopRate'])) {
        $filters['stopRate'] = [$filters['stopRate']];
      }
      $realValues = \array_map(static fn($item) => match ($item) {
        'musor' => 1,
        'prohodnyak' => 2,
        'pohvalno' => 3,
        'izumitelno' => 4,
        default => 0
      }, $filters['stopRate']);
      $relationName = 'game.rating';
      if ($plainRequest) {
        $relationName = 'rating';
      }
      $gamesQuery
        ->joinWith($relationName)
        ->andWhere(['StopRating' => $realValues]);
      $activeFiltersCount++;
    }

    if (!empty($filters['start_year']) || !empty($filters['end_year'])) {
      $activeFiltersCount++;
    }

    if (!empty($filters['start_year']) && \is_numeric($filters['start_year'])) {
      $gamesQuery->andWhere(['>=', 'GameDate', $filters['start_year'] . '-00-00']);
    }

    if (!empty($filters['end_year']) && \is_numeric($filters['end_year'])) {
      $gamesQuery
        ->andWhere(['<=', 'GameDate', $filters['end_year'] . '-12-31'])
        ->andWhere(['NOT', ['GameDate' => '0000-00-00']]);
    }

    if (!empty($filters['rating_start']) || !empty($filters['rating_end'])) {
      $activeFiltersCount++;
    }

    if (!empty($filters['rating_start']) && \is_numeric($filters['rating_start'])) {
      $relationName = 'game.rating';
      if ($plainRequest) {
        $relationName = 'rating';
      }
      $gamesQuery
        ->joinWith($relationName)
        ->andWhere(['>=', new Expression('ROUND((RateCount/RateTotal), 1)'), $filters['rating_start']]);
    }

    if (!empty($filters['rating_end']) && \is_numeric($filters['rating_end'])) {
      $relationName = 'game.rating';
      if ($plainRequest) {
        $relationName = 'rating';
      }
      $gamesQuery
        ->joinWith($relationName)
        ->andWhere(['<=', new Expression('ROUND((RateCount/RateTotal), 1)'), $filters['rating_end']]);
    }

    $popularityLimit = 0;
    if (!empty($filters['popularity_start']) || !empty($filters['popularity_end'])) {
      $popularityLimit = (float)Track::find()
        ->select(['pop' => 'count(*)', 'GameId'])
        ->groupBy('GameId')
        ->orderBy(['pop' => \SORT_DESC])
        ->limit(1)
        ->scalar();
      $activeFiltersCount++;
    }

    if (!empty($filters['popularity_start']) && \is_numeric($filters['popularity_start'])) {
      $popularityPercent = (($filters['popularity_start'] / 100) ** 2) * $popularityLimit;
      $gamesQuery = $this->addPopularityJoin($gamesQuery);
      $gamesQuery->andWhere(['>=', 'popularity', $popularityPercent]);
    }

    if (!empty($filters['popularity_end']) && \is_numeric($filters['popularity_end'])) {
      $popularityPercent = ((($filters['popularity_end'] + 1) / 100) ** 2) * $popularityLimit;
      $gamesQuery = $this->addPopularityJoin($gamesQuery);
      $gamesQuery->andWhere(['<=', 'popularity', $popularityPercent]);
    }

    if (empty($filters['start_year'])) {
      $filters['start_year'] = 1970;
    }
    if (empty($filters['end_year'])) {
      $filters['end_year'] = (int)\date('Y') + 2;
    }

    if ($activeFiltersCount > 0) {
      $searchType = 'разделу';
      if ($section === 'compilation') {
        $searchType = 'подборке';
      }
      $pageTitle = "Поиск по $searchType «{$pageTitle}»";
    }

    if (empty($sort) || !isset($availableOrders[$sort])) {
      $sort = $defaultOrder;
    }

    if (isset($availableOrders[$sort]['query'])) {
      $gamesQuery = $availableOrders[$sort]['query']($gamesQuery);
    }

    $gamesQuery->orderBy($availableOrders[$sort]['order'])
      ->andWhere(['NOT', ['stopgame_info.GameID' => null]]);

    $gamesGridType = 'grid';
    if (Yii::$app->user->isLoggedIn) {
      $gamesGridType = Yii::$app->user->settings->profile_grid_type;
    }

    $dataProvider = new ActiveDataProvider([
      'query' => $gamesQuery,
      'key' => 'GameId',
    ]);
    $dataProvider->setPagination(
      new Pagination([
        'page' => $page - 1,
        'defaultPageSize' => 30,
      ]),
    );

    $query2 = $this->user->getTracks();

    $totalFavourite = (int)UserFavouriteGame::find()->where(['user_id' => $this->user->id])->count();
    $totalWishlist = (int)(clone $query2)->where(['track.status' => GameLibraryStatus::WISHLIST->value])->count();
    $totalPlaying = (int)(clone $query2)->where(['track.status' => GameLibraryStatus::PLAYING->value])->count();
    $totalBeaten = (int)(clone $query2)->where(['track.status' => GameLibraryStatus::BEATEN->value])->count();
    $totalTrashed = (int)(clone $query2)->where(['track.status' => GameLibraryStatus::TRASHED->value])->count();
    $totalRated = $this->user->getVotes()->rightJoin('stopgame_title', 'GameId = target_id')->andWhere([
      'target_type' => 'game',
      'GameNamePrimary' => 1,
    ])->count();
    $totalTracked = $this->user
      ->getTracks()
      ->select(['GameId' => 'track.GameId'])
      ->innerJoin('stopgame_info', 'stopgame_info.GameId = track.GameId')
      ->union(
        $this->user
          ->getVotes()
          ->select(['GameId' => 'target_id'])
          ->innerJoin('stopgame_info', 'GameId = target_id')
          ->andWhere(['target_type' => 'game'])
          ->andWhere(['>', 'vote_value', 0]),
      )
      ->union(
        UserFavouriteGame::find()
          ->select(['GameId' => 'game_id'])
          ->innerJoin('stopgame_info', 'GameId = game_id')
          ->andWhere(['user_id' => $this->user->id]),
      )
      ->distinct()->count();

    $this->view->params['gamesSections'] = [
      [
        'title' => 'Вся коллекция игр',
        'count' => $totalTracked,
        'url' => ['/user/games', 'userName' => $this->user->user_name, 'section' => 'all'],
        'active' => $section === 'all',
      ],
      [
        'title' => 'Любимые',
        'count' => $totalFavourite,
        'svg' => 'v9/heart-full',
        'color' => '--brand-red',
        'url' => ['/user/games', 'userName' => $this->user->user_name, 'section' => 'favourite'],
        'active' => $section === 'favourite',
      ],
      [
        'title' => 'Играю',
        'count' => $totalPlaying,
        'svg' => 'v9/playing',
        'color' => '--playing-color',
        'url' => ['/user/games', 'userName' => $this->user->user_name, 'section' => 'playing'],
        'active' => $section === 'playing',
      ],
      [
        'title' => 'Буду играть',
        'count' => $totalWishlist,
        'svg' => 'v9/wishlist',
        'color' => '--wishlist-color',
        'url' => ['/user/games', 'userName' => $this->user->user_name, 'section' => 'wishlist'],
        'active' => $section === 'wishlist',
      ],
      [
        'title' => 'Пройдено',
        'count' => $totalBeaten,
        'svg' => 'v9/beaten',
        'color' => '--beaten-color',
        'url' => ['/user/games', 'userName' => $this->user->user_name, 'section' => 'beaten'],
        'active' => $section === 'beaten',
      ],
      [
        'title' => 'Заброшено',
        'count' => $totalTrashed,
        'svg' => 'v9/sleep',
        'color' => '--sleep-color',
        'url' => ['/user/games', 'userName' => $this->user->user_name, 'section' => 'trashed'],
        'active' => $section === 'trashed',
      ],
      [
        'title' => 'Оценённые',
        'count' => $totalRated,
        'svg' => 'sg-new/star',
        'adaptive-svg' => true,
        'url' => ['/user/games', 'userName' => $this->user->user_name, 'section' => 'rated'],
        'active' => $section === 'rated',
      ],
    ];

    $compilationQuery = Compilation::find()
      ->where(['user_id' => $this->user->id, 'type' => Compilation::TYPE_UGC, 'deleted' => 0]);

    if (!$this->user->isCurrentUser) {
      $compilationQuery->andWhere(['public' => [1, 2]]);
    }

    $compilations = $compilationQuery->all();

    $this->view->params['compilations'] = \array_map(fn(Compilation $compilation) => [
      'title' => $compilation->title,
      'url' => [
        '/user/games',
        'userName' => $this->user->user_name,
        'section' => 'compilation',
        'subsection' => $compilation->id,
      ],
      'count' => $compilation->getCompilationItems()->count(),
      'active' => ($section === 'compilation') && ($subsection === (string)$compilation->id),
    ], $compilations);

    if ($page > 1) {
      $this->view->params['subtitle'] = 'Страница ' . $page;
    }
    $this->view->params['activeFiltersCount'] = $activeFiltersCount;
    $this->view->params['pageTitle'] = $pageTitle;
    $this->view->params['description'] = 'Игры пользователя ' . $this->user->userName;

    $viewParams = \compact(
      'section',
      'dataProvider',
      'page',
      'gamesGridType',
      'totalWishlist',
      'totalPlaying',
      'totalBeaten',
      'totalTrashed',
      'pageTitle',
      'sort',
      'availableOrders',
      'activeFiltersCount',
      'innerTabs',
      'subsection',
      'filters',
    );

    if (isset($compilation)) {
      $viewParams['compilation'] = $compilation;
    }

    return $this->render('@frontend/views/v9/user/games', $viewParams);
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  /*public function actionAchievements(): string
  {
      return $this->render('@frontend/views/v9/user/achievements');
  }*/

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin'])]
  public function actionDelete(): Response
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    Yii::$app->queue->push(
      new DeleteUserJob([
        'userId' => $this->user->id,
      ]),
    );

    return $this->redirect('/');
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  public function actionSubscriptions(string $section = 'following'): string
  {
    $user = $this->user;
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $subType = ($section === 'following') ? 'подписки' : 'подписчики';
    $this->view->params['pageTitle'] = \mb_ucfirst($subType) . ' пользователя ' . $user->userName;
    if ($user->isCurrentUser) {
      $this->view->params['pageTitle'] = 'Мои ' . $subType;
    }

    $currentUserSubs = [];
    if (Yii::$app->user->isLoggedIn) {
      $currentUserSubs = Yii::$app->user->identity
        ->getBlogSubscriptions()
        ->select(['blog_id'])
        ->column();
    }

    $counts = [
      'following' => $user->getBlogSubscriptions()->count(),
      'followers' => $user->personalBlog?->getSubscribedUsers()->count() ?? 0,
    ];

    if ($section === 'following') {
      /** @var BlogSubscription[] $subscriptions */
      $subscriptions = $user
        ->getBlogSubscriptions()
        ->with('blog')
        ->all();

      $userIds = [];
      foreach ($subscriptions as $subscription) {
        if ($subscription->blog?->blog_type === Blog::TYPE_PERSONAL) {
          $userIds[] = $subscription->blog->user_owner_id;
        }
      }

      $users = User::find()
        ->where(['id' => $userIds])
        ->indexBy('id')
        ->all();

      return $this->render(
        '@frontend/views/v9/user/following',
        \compact('subscriptions', 'users', 'currentUserSubs', 'counts'),
      );
    }

    $subscribers = [];
    $subBlogs = [];
    if (!empty($user->personalBlog)) {
      $subscribers = $user->personalBlog
        ->getSubscribedUsers()
        ->all();
      $subIds = ArrayHelper::getColumn($subscribers, 'id');
      $subBlogs = Blog::find()
        ->select(['blog_id', 'user_owner_id'])
        ->where([
          'user_owner_id' => $subIds,
          'blog_type' => Blog::TYPE_PERSONAL,
        ])
        ->indexBy('user_owner_id')
        ->all();
    }

    return $this->render(
      '@frontend/views/v9/user/followers',
      \compact('subscribers', 'subBlogs', 'currentUserSubs', 'counts'),
    );
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws Throwable
   * @noinspection PhpUnused
   */
  public function actionCompilations(int $page = 1, ?string $filter = null): string
  {
    if (empty($this->user)) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $this->view->params['pageTitle'] = 'Подборки пользователя ' . $this->user->userName;
    if ($this->user->isCurrentUser) {
      $this->view->params['pageTitle'] = 'Мои подборки';
    }

    $query = Compilation::find()
      ->select('id')
      ->where([
        'user_id' => $this->user->id,
        'type' => Compilation::TYPE_UGC,
        'deleted' => 0,
      ])
      ->asArray();

    $allQuery = (clone $query);
    $publicQuery = (clone $query)->andWhere(['public' => 1]);
    $privateQuery = (clone $query)->andWhere(['public' => 2]);
    $hiddenQuery = (clone $query)->andWhere(['public' => 0]);

    if (!$this->user->isCurrentUser) {
      $allQuery->andWhere(['public' => [1, 2]]);

      if ($filter === 'hidden') {
        $filter = null;
      }
    }

    $dataProvider = new ActiveDataProvider([
      'query' => match ($filter) {
        'public' => (clone $publicQuery),
        'private' => (clone $privateQuery),
        'hidden' => (clone $hiddenQuery),
        default => (clone $allQuery)
      }
    ]);

    if ($page > 1) {
      $this->view->params['subtitle'] = 'Страница ' . $page;
    }

    $models = $dataProvider->getModels();
    foreach ($models as $model) {
      ModelLoader::push(Compilation::class, $model['id']);
    }

    ModelLoader::populateAll();

    $this->view->params['inner-tabs'] = [
      [
        'label' => 'Все',
        'url' => ['user/compilations', 'userName' => $this->user->user_name],
        'active' => empty($filter),
        'sup' => (clone $allQuery)->count()
      ],
      [
        'label' => SVG::ai('sg-new/users') . 'Публичные',
        'url' => ['user/compilations', 'userName' => $this->user->user_name, 'filter' => 'public'],
        'active' => $filter === 'public',
        'sup' => (clone $publicQuery)->count()
      ],
      [
        'label' => SVG::ai('sg-new/user') . 'Личные',
        'url' => ['user/compilations', 'userName' => $this->user->user_name, 'filter' => 'private'],
        'active' => $filter === 'private',
        'sup' => (clone $privateQuery)->count()
      ]
    ];

    if ($this->user->isCurrentUser) {
      $this->view->params['inner-tabs'][] = [
        'label' => SVG::ai('sg-new/eye-slash') . 'Приватные',
        'url' => ['user/compilations', 'userName' => $this->user->user_name, 'filter' => 'hidden'],
        'active' => $filter === 'hidden',
        'sup' => (clone $hiddenQuery)->count()
      ];
    }

    return $this->render('@frontend/views/v9/user/compilations', \compact('dataProvider', 'page'));
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionFavourite(
    string $section = 'all',
    int $page = 1,
  ): string {
    $this->view->params['pageTitle'] = 'Мои закладки';

    $baseQuery = Favourite::find()
      ->where(['user_id' => Yii::$app->user->id])
      ->orderBy(['favourite.fav_date' => \SORT_DESC]);

    $siteMaterialQuery = (clone $baseQuery)
      ->andWhere(['target_type' => 'show']);

    $newsQuery = (clone $baseQuery)
      ->andWhere(['target_type' => 'news']);

    $blogsQuery = (clone $baseQuery)
      ->andWhere(['target_type' => 'blogs']);

    $commentsQuery = (clone $baseQuery)
      ->andWhere(['target_type' => 'comments']);

    $compilationsQuery = (clone $baseQuery)
      ->andWhere(['target_type' => 'compilation']);

    $faq = (clone $baseQuery)
      ->andWhere(['target_type' => 'faq']);

    $query = match ($section) {
      'show' => $siteMaterialQuery,
      'news' => $newsQuery,
      'blogs' => $blogsQuery,
      'comments' => $commentsQuery,
      'compilations' => $compilationsQuery,
      'faq' => $faq,
      default => $baseQuery,
    };

    $dataProvider = new ActiveDataProvider(\compact('query'));

    $favourites = $dataProvider->getModels();

    $ids = [];
    $models = [];

    foreach ($favourites as $favourite) {
      $type = $favourite->target_type;
      if (!isset($ids[$type])) {
        $ids[$type] = [];
      }
      $ids[$type][] = $favourite->target_id;
    }

    $readData = [];

    if (isset($ids['show'])) {
      $models['show'] = Article::find()
        ->where(['base_id' => $ids['show']])
        ->indexBy('base_id')
        ->all();

      if (Yii::$app->user->isLoggedIn) {
        $readData['show'] = Yii::$app->user->identity
          ->getReadData()
          ->where([
            'target_type' => 'show',
            'target_id' => $ids['show'],
          ])
          ->indexBy('target_id')
          ->all();
      }
    }
    if (isset($ids['news'])) {
      $models['news'] = NewsData::find()
        ->where(['news_id' => $ids['news']])
        ->indexBy('news_id')
        ->all();
      if (Yii::$app->user->isLoggedIn) {
        $readData['news'] = Yii::$app->user->identity
          ->getReadData()
          ->where([
            'target_type' => 'news',
            'target_id' => $ids['news'],
          ])
          ->indexBy('target_id')
          ->all();
      }
    }
    if (isset($ids['blogs'])) {
      $models['blogs'] = Topic::find()
        ->where(['topic_id' => $ids['blogs']])
        ->indexBy('topic_id')
        ->all();
      if (Yii::$app->user->isLoggedIn) {
        $readData['blogs'] = Yii::$app->user->identity
          ->getReadData()
          ->where([
            'target_type' => 'blogs',
            'target_id' => $ids['blogs'],
          ])
          ->indexBy('target_id')
          ->all();
      }
    }
    if (isset($ids['comments'])) {
      $models['comments'] = Comment::find()
        ->where(['comment_id' => $ids['comments']])
        ->indexBy('comment_id')
        ->all();
    }
    if (isset($ids['compilation'])) {
      $models['compilation'] = ArrayHelper::index(ModelLoader::getBatch(Compilation::class, $ids['compilation']), 'id');
    }
    if (isset($ids['faq'])) {
      $models['faq'] = Faq::find()
        ->where(['faq_id' => $ids['faq']])
        ->indexBy('faq_id')
        ->all();
      if (Yii::$app->user->isLoggedIn) {
        $readData['faq'] = Yii::$app->user->identity
          ->getReadData()
          ->where([
            'target_type' => 'faq',
            'target_id' => $ids['faq'],
          ])
          ->indexBy('target_id')
          ->all();
      }
    }

    if ($page > 1) {
      $this->view->params['subtitle'] = 'Страница ' . $page;
    }

    $counts = [
      'all' => (clone $baseQuery)->count(),
      'show' => (clone $siteMaterialQuery)->count(),
      'news' => (clone $newsQuery)->count(),
      'blogs' => (clone $blogsQuery)->count(),
      'comments' => (clone $commentsQuery)->count(),
      'compilations' => (clone $compilationsQuery)->count(),
      'faq' => (clone $faq)->count(),
    ];

    return $this->render(
      '@frontend/views/v9/user/favourite',
      \compact('dataProvider', 'models', 'section', 'page', 'counts', 'readData'),
    );
  }

  /**
   * @param ActiveQuery<Game|Track> $query
   * @return ActiveQuery<Game|Track>
   * @throws InvalidConfigException
   */
  protected function addPopularityJoin(ActiveQuery $query): ActiveQueryInterface
  {
    $hasPopJoin = false;
    if (isset($query->join)) {
      foreach ($query->join as $join) {
        if (\is_array($join) && \is_array($join[1]) && \array_key_exists('pop', $join[1])) {
          $hasPopJoin = true;
          break;
        }
      }
    }

    if (!$hasPopJoin) {
      $query->leftJoin(
        [
          'pop' => Track::find()->select(['GameId', 'popularity' => new Expression('COUNT(*)')])->groupBy(
            ['GameId'],
          ),
        ],
        'pop.GameId = stopgame_info.GameId',
      );
    }
    return $query;
  }

  /**
   * @throws ForbiddenHttpException
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionRequests(string $section = 'new', int $page = 1): string
  {
    $userId = $this->user?->id;

    if (!$userId) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    if (($userId !== Yii::$app->user->id) && !Yii::$app->user->can('admin_access')) {
      throw new ForbiddenHttpException('Нельзя просматривать чужие запросы');
    }

    $this->view->params['pageTitle'] = 'Мои запросы';

    $gameFixQuery = GameFixSuggestion::find()
      ->select(['id', 'created_at'])
      ->where([
        'user_id' => $userId,
      ]);

    $gameImageQuery = GameImageRequest::find()
      ->select(['id', 'created_at'])
      ->where([
        'user_id' => $userId,
      ]);

    $gameRequestQuery = GameRequest::find()
      ->select(['id', 'created_at'])
      ->where([
        'user_id' => $userId,
      ]);

    $gameFixNewQuery = (clone $gameFixQuery)
      ->andWhere([
        'game_fix_suggestion.status' => GameFixSuggestion::STATUS_NEW,
      ]);

    $gameFixDoneQuery = (clone $gameFixQuery)
      ->andWhere([
        'NOT',
        ['game_fix_suggestion.status' => GameFixSuggestion::STATUS_NEW],
      ]);

    $gameImageNewQuery = (clone $gameImageQuery)
      ->andWhere([
        'game_image_request.status' => GameImageRequest::STATUS_NEW,
      ]);

    $gameImageDoneQuery = (clone $gameImageQuery)
      ->andWhere([
        'NOT',
        ['game_image_request.status' => GameImageRequest::STATUS_NEW],
      ]);

    $gameRequestNewQuery = (clone $gameRequestQuery)
      ->andWhere([
        'game_id' => null,
        'closed_by' => null,
      ]);

    $gameRequestDoneQuery = (clone $gameRequestQuery)
      ->andWhere([
        'OR',
        ['NOT', ['game_id' => null]],
        ['NOT', ['closed_by' => null]],
      ]);

    $newQuery = (clone $gameFixNewQuery)
      ->select(['game_id', 'max_date' => new Expression('MAX(created_at)')])
      ->groupBy('game_id')
      ->union(
        (clone $gameImageNewQuery)
          ->select(['game_id', 'max_date' => new Expression('MAX(created_at)')])
          ->groupBy('game_id'),
      )
      ->union(
        (clone $gameRequestNewQuery)
          ->select(
            [
              'game_id' => new Expression("concat('suggest-', id)"),
              'max_date' => new Expression('MAX(created_at)'),
            ],
          )
          ->groupBy('id'),
      );
    $processedQuery = (clone $gameFixDoneQuery)
      ->select(['game_id', 'max_date' => new Expression('MAX(updated_at)')])
      ->groupBy('game_id')
      ->union(
        (clone $gameImageDoneQuery)
          ->select(['game_id', 'max_date' => new Expression('MAX(updated_at)')])
          ->groupBy('game_id'),
      )
      ->union(
        (clone $gameRequestDoneQuery)
          ->select(
            [
              'game_id' => new Expression("concat('suggest-', id)"),
              'max_date' => new Expression('MAX(updated_at)'),
            ],
          )
          ->groupBy('id'),
      );

    $newCount = new Query()
      ->from((clone $gameFixNewQuery)->union((clone $gameImageNewQuery))->union((clone $gameRequestNewQuery)))
      ->where(['NOT', ['created_at' => null]])
      ->count();
    $processedCount = new Query()
      ->from((clone $gameFixDoneQuery)->union((clone $gameImageDoneQuery))->union((clone $gameRequestDoneQuery)))
      ->where(['NOT', ['created_at' => null]])
      ->count();

    $newQuery = new Query()
      ->from([
        'subquery' => $newQuery,
      ])
      ->select(['game_id', 'max_date' => new Expression('MAX(max_date)')])
      ->where(['not', ['max_date' => null]])
      ->groupBy('game_id')
      ->orderBy(['max_date' => \SORT_DESC]);

    $processedQuery = new Query()
      ->from([
        'subquery' => $processedQuery,
      ])
      ->select(['game_id', 'max_date' => new Expression('MAX(max_date)')])
      ->where(['not', ['max_date' => null]])
      ->groupBy('game_id')
      ->orderBy(['max_date' => \SORT_DESC]);

    $query = $newQuery;
    if ($section === 'processed') {
      $query = $processedQuery;
    }

    $dataProvider = new ActiveDataProvider(\compact('query'));

    $items = $dataProvider->getModels();

    $allIds = ArrayHelper::getColumn($items, 'game_id');
    $gameIds = [];
    $requestsIds = [];
    foreach ($allIds as $id) {
      if (\str_starts_with($id, 'suggest-')) {
        $requestsIds[] = \substr($id, 8);
      } else {
        $gameIds[] = $id;
      }
    }

    $games = Game::find()
      ->where(['GameId' => $gameIds])
      ->indexBy('GameId')
      ->all();
    $gameRequests = GameRequest::find()
      ->where(['id' => $requestsIds])
      ->indexBy('id')
      ->all();
    $gameFixSuggestionsQuery = GameFixSuggestion::find()
      ->where([
        'user_id' => $userId,
        'game_id' => $gameIds,
      ]);
    $gameImageRequestsQuery = GameImageRequest::find()
      ->where([
        'user_id' => $userId,
        'game_id' => $gameIds,
      ]);
    if ($section === 'processed') {
      $gameFixSuggestionsQuery->andWhere(['NOT', ['game_fix_suggestion.status' => GameFixSuggestion::STATUS_NEW]]);
      $gameImageRequestsQuery->andWhere(['NOT', ['game_image_request.status' => GameImageRequest::STATUS_NEW]]);
    } else {
      $gameFixSuggestionsQuery->andWhere(['game_fix_suggestion.status' => GameFixSuggestion::STATUS_NEW]);
      $gameImageRequestsQuery->andWhere(['game_image_request.status' => GameImageRequest::STATUS_NEW]);
    }
    $gameFixSuggestions = $gameFixSuggestionsQuery->all();
    $gameImageRequests = $gameImageRequestsQuery->all();

    $gameFixSuggestions = ArrayHelper::index($gameFixSuggestions, null, 'game_id');
    $gameImageRequests = ArrayHelper::index($gameImageRequests, null, 'game_id');

    if ($page > 1) {
      $this->view->params['subtitle'] = 'Страница ' . $page;
    }

    return $this->render(
      '@frontend/views/v9/user/requests',
      \compact(
        'dataProvider',
        'games',
        'gameRequests',
        'gameFixSuggestions',
        'gameImageRequests',
        'section',
        'newCount',
        'processedCount',
      ),
    );
  }

  /**
   * @noinspection PhpUnused
   */
  /**
   * @throws Throwable
   * @throws InvalidConfigException
   * @throws StaleObjectException
   * @throws NotFoundHttpException
   */
  public function actionMailChangeConfirm(#[\SensitiveParameter] ?string $token): string|Response
  {
    /** @var ?UserMailChange $mailChange */
    $mailChange = UserMailChange::find()->where(\compact('token'))->one();

    if (!$mailChange) {
      $error = 'Ссылка устарела или неверна';
    } else {
      $user = $mailChange->user;
      $user->user_mail = $mailChange->new_email;
      $user->save();
      $mailChange->delete();
      Yii::$app->user->login($user);
      Yii::$app->session->setFlash('success', 'Почта успешно изменена');
      return $this->redirect(['user-edit/reg', 'userName' => $user->user_name]);
    }

    $this->layout = 'main';
    return $this->render('@frontend/views/v9/user/mail-change-confirm', \compact('error'));
  }
}
