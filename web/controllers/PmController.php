<?php

namespace frontend\controllers;

use Yii;
use yii\web\Controller;
use frontend\traits\Access;
use frontend\components\View;
use yii\web\NotFoundHttpException;

/**
 * @property-read View $view
 * @noinspection PhpUnused
 */
class PmController extends Controller
{
  /**
   * @param array<string, mixed> $params
   */
  #[\Override]
  public function render($view, $params = []): string
  {
    Yii::$app->adManager->disabled = true;

    return parent::render($view, $params);
  }

  /**
   * @throws NotFoundHttpException
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionIndex(): string
  {
    if (Yii::$app->user->isGuest) {
      throw new NotFoundHttpException('Для просмотра личных сообщений необходимо авторизоваться');
    }

    $this->view->title = 'Личные сообщения';
    $this->view->registerOG([
      'og' => [
        'title' => 'Личные сообщения',
        'description' => 'Веди приватные беседы с пользователями StopGame.ru'
      ]
    ]);

    return $this->render('index');
  }
}
