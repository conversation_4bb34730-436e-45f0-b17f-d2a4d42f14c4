<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use Throwable;
use yii\helpers\Json;
use RuntimeException;
use common\models\Game;
use yii\web\UploadedFile;
use common\models\Article;
use frontend\traits\Access;
use common\models\GameGallery;
use common\models\ArticleGame;
use common\helpers\SgImageHelper;
use yii\web\NotFoundHttpException;
use yii\web\ForbiddenHttpException;
use yii\base\InvalidConfigException;
use frontend\modules\ajax\ApiController;

use function Sentry\captureException;

/**
 * @noinspection PhpUnused
 */

class ScreenshotsController extends ApiController
{
  /**
   * @return array{
   *     success: bool,
   *     message?: string,
   *     screenId?: int,
   *     url?: string,
   *     fullUrl?: string,
   *     width?: ?int,
   *     height?: ?int
   * }
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['screens_access'], methods: ['POST'])]
  public function actionAddImage(): array
  {
    if (!Yii::$app->user->can('screens_access')) {
      throw new ForbiddenHttpException('Недостаточно прав');
    }

    $gameId = $this->request->post('game_id');
    $game = Game::findOne($gameId);

    if (!$game || !$game->mainTitle) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    $image = UploadedFile::getInstanceByName('image');

    if (!$image || !$image->tempName || !\file_exists($image->tempName)) {
      return ['success' => false, 'message' => 'Изображение не было загружено на сервер'];
    }

    $fullDir = (string)Yii::getAlias("@root/images/screenshots/$game->GameId");
    $smallDir = (string)Yii::getAlias("@root/images/screenshots/$game->GameId/small");

    // Создаем необходимые каталоги
    if (!\is_dir($fullDir)) {
      if (\file_exists($fullDir)) {
        \unlink($fullDir);
      }
      try {
        if (!\mkdir($smallDir, 0o777, true) && !\is_dir($smallDir)) {
          throw new RuntimeException(\sprintf('Directory "%s" was not created', $smallDir));
        }
      } catch (Throwable) {
        return ['success' => false, 'message' => 'Произошла непредвиденная ошибка'];
      }
    }

    $md5InFolder = [];
    $countFiles = 0;
    $handle = \opendir($fullDir);
    if (!$handle) {
      captureException(new RuntimeException("Не удалось открыть папку $fullDir"));
      return ['success' => false, 'message' => 'Произошла непредвиденная ошибка'];
    }
    $maxScreenId = 0;
    /** @noinspection PhpAssignmentInConditionInspection */
    while ($file = \readdir($handle)) {
      if (($file === '.') || ($file === '..') || \is_dir("$fullDir/$file")) {
        continue;
      }
      $md5InFolder[$file] = \md5_file("$fullDir/$file");
      $fname = \explode('-', \explode('.', $file)[0]);
      $screenId = \array_pop($fname);
      if (\is_numeric($screenId) && ($screenId > $maxScreenId)) {
        $maxScreenId = $screenId;
      }
      $countFiles++;
    }

    $duplicate = \array_search(\md5_file($image->tempName), $md5InFolder);
    if (!\getimagesize($image->tempName)) {
      return ['success' => false, 'message' => 'Некорректное изображение'];
    }
    if ($duplicate && ($duplicate !== $image->name)) {
      return ['success' => false, 'message' => 'Дубликат!'];
    }

    $countFiles++;
    $maxScreenId++;
    $newName = "{$game->mainTitle->GameNameUrl}-$maxScreenId.jpg";
    $fullPath = "$fullDir/$newName";
    $smallPath = "$smallDir/$newName";

    if (\filesize($image->tempName) > 20000000) {
      return ['success' => false, 'message' => 'Слишком большой файл!'];
    }

    \rename($image->tempName, $fullPath);
    try {
      SgImageHelper::cropCentered($fullPath, $smallPath);
    } catch (Throwable) {
      // Ну и ладно, мы всё равно это не используем 🙃
    }

    try {
      $resolution = \getimagesize($fullPath);
    } catch (Throwable) {
      return ['success' => false, 'message' => 'Не удалось получить размеры изображения'];
    }
    if (!$resolution) {
      return ['success' => false, 'message' => 'Не удалось получить размеры изображения'];
    }

    $screen = new GameGallery([
      'data_type' => 'screenshots',
      'GameId' => $game->GameId,
      'screen_file' => $newName,
      'pic_resolution' => "$resolution[0]x$resolution[1]",
      'pic_size' => \filesize($fullPath) ?: 0,
    ]);

    if (!$screen->save()) {
      return ['success' => false, 'message' => Json::encode($screen->errors, \JSON_UNESCAPED_UNICODE)];
    }

    $isNew = false;
    /** @var ?Article $screenPost */
    $screenPost = Article::find()
      ->joinWith('articleGames')
      ->where([
        'game_id' => $game->GameId,
        'data_type' => 'screenshots',
      ])
      ->one();

    if (!$screenPost) {
      $screenPost = new Article([
        'data_type' => 'screenshots',
        'data_url' => 'screenshots',
        'data_title' => 'скриншоты',
      ]);
      $isNew = true;
    }

    $screenPost->pic_count = $countFiles;
    $screenPost->setScenario(Article::SCENARIO_DRAFT);
    if (!$screenPost->save()) {
      return ['success' => false, 'message' => Json::encode($screenPost->errors, \JSON_UNESCAPED_UNICODE)];
    }

    if ($isNew) {
      $articleGame = new ArticleGame([
        'article_id' => $screenPost->base_id,
        'game_id' => $game->GameId,
      ]);
      $articleGame->save();
      $screenPost->notify();
    }

    return [
      'success' => true,
      'screenId' => $screen->screen_id,
      'url' => $screen->getUrl(GameGallery::SIZE_SMALL),
      'fullUrl' => $screen->url,
      'width' => $screen->width,
      'height' => $screen->height,
    ];
  }

  /**
   * @return array{
   *     success: bool
   * }
   * @throws Throwable
   */
  #[Access(permissions: ['screens_access'], methods: ['POST'])]
  public function actionDelete(string $id): array
  {
    $screen = GameGallery::findOne($id);

    if (!$screen) {
      return ['success' => true];
    }

    /** @var ?Article $article */
    $article = Article::find()
      ->joinWith('articleGames')
      ->where([
        'game_id' => $screen->GameId,
        'data_type' => 'screenshots',
      ])
      ->one();

    $largeFile = (string)Yii::getAlias("@root/images/screenshots/$screen->GameId/$screen->screen_file");
    $smallFile = (string)Yii::getAlias("@root/images/screenshots/$screen->GameId/small/$screen->screen_file");

    if (!$screen->delete()) {
      return ['success' => false];
    }

    try {
      \unlink($largeFile);
    } catch (Throwable) {
      // do nothing
    }
    try {
      \unlink($smallFile);
    } catch (Throwable) {
      // do nothing
    }

    if ($article) {
      $article->pic_count--;
      $article->setScenario(Article::SCENARIO_DRAFT);
      $article->save();
    }

    return ['success' => true];
  }
}
