<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use frontend\traits\Access;
use frontend\components\View;
use common\components\db\ActiveQuery;
use yii\data\{Sort, ActiveDataProvider};
use frontend\modules\admin\AdminController;
use common\exceptions\ControlFlowException;
use common\helpers\{Sg<PERSON><PERSON><PERSON>, SgStringHelper};
use common\widgets\formatters\PresentationFormatter;
use yii\db\{ActiveQueryInterface, StaleObjectException};
use common\models\{Article, ArticleGame, GameTitle, GameRequest, GameRequestAttachment};
use yii\web\{Request, Response, NotFoundHttpException, ForbiddenHttpException};

use function Sentry\captureException;

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class CheatsController extends AdminController
{
  /**
   * @var ActiveQuery<Article> $cheatsQuery
   */
  public ActiveQueryInterface $cheatsQuery {
    get => Article::find()
      ->joinWith('articleGames')
      ->with('user', 'gameTitle', 'announce')
      ->where(['data_type' => 'cheats'])
      ->orderBy(['data_add' => \SORT_DESC]);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['cheats_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $query = $this->cheatsQuery;
    $query->andWhere([
      'data_active' => 1,
    ]);
    $this->view->title = 'Читы ⋅ Админка';

    return $this->renderCheatsTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['cheats_access'], methods: ['GET'])]
  public function actionReadyToPublish(): string
  {
    $this->view->title = 'Читы ⋅ Админка';
    $query = $this->cheatsQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ]);

    return $this->renderCheatsTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['cheats_access'], methods: ['GET'])]
  public function actionDrafts(): string
  {
    $this->view->title = 'Читы ⋅ Админка';
    $user = Yii::$app->user;
    $query = $this->cheatsQuery
      ->andWhere([
        'data_active' => false,
        'ready_to_publish' => false,
      ]);

    if (!$user->can('cheats_manage')) {
      $query->andWhere(['user_id' => $user->id]);
    }

    return $this->renderCheatsTable($query);
  }

  /**
   * @param ActiveQuery<Article> $query
   * @throws Throwable
   */
  protected function renderCheatsTable(ActiveQuery $query): string
  {
    $term = $this->request->get('term', '');
    $term = \trim($term);
    if ($term !== '') {
      $term = SgHelper::stripThe($term);
      $gameIds = GameTitle::find()
        ->select('GameId')
        ->andWhere([
          'OR',
          ['LIKE', 'GameName', $term],
          ['LIKE', 'GameKeywords', $term],
        ])
        ->groupBy(['GameId'])
        ->column();
      $query->andWhere(['game_id' => $gameIds]);
    }

    $dataProvider = new ActiveDataProvider(\compact('query'));
    $dataProvider->setSort(
      new Sort([
        'defaultOrder' => ['data_add' => \SORT_DESC],
      ]),
    );

    $readyCount = $this->cheatsQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ])->count();

    if ($readyCount === '0') {
      $readyCount = null;
    }

    return $this->render('index', \compact('dataProvider', 'readyCount', 'term'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['cheats_create'], methods: ['GET'])]
  public function actionAdd(): string
  {
    $this->view->title = 'Новые читы ⋅ Админка';
    $article = new Article();
    $article->user_id = (int)Yii::$app->user->id;
    $gameTitle = new GameTitle();
    $article->setScenario(Article::SCENARIO_DRAFT);

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['cheats_create'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Читы не найдены');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя редактировать чужие читы');
    }

    if (!$article->data_active && !$article->ready_to_publish) {
      $article->setScenario(Article::SCENARIO_DRAFT);
    }

    $gameTitle = $article->gameTitle ?? new GameTitle();
    $this->view->title = "$article->title ⋅ Админка";

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['cheats_create'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    $post = $this->request->post();

    $article = new Article();
    $article->data_type = 'cheats';
    $article->data_title = 'Коды';
    $id = $post['Article']['base_id'];
    if ($id) {
      $article = Article::findOne($id);

      if (!$article) {
        throw new NotFoundHttpException('Читы не найдены');
      }

      if (!$article->canEdit) {
        throw new ForbiddenHttpException('Нельзя редактировать чужие читы');
      }
    }

    $gameTitle = new GameTitle();
    $isNew = $article->isNewRecord;
    $articleGames = [];

    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $gameId = $post['GameTitle']['GameId'] ?? null;
      if (\is_array($gameId)) {
        $gameId = $gameId[0] ?? null;
      }
      $article->load($this->request->post());

      $skipValidation = false;
      if (isset($post['submit_article_draft'])) {
        $skipValidation = true;
        $article->setScenario(Article::SCENARIO_DRAFT);
      }

      if (empty($gameId)) {
        $gameTitle->addError('GameId', 'Игра не указана');
      } else {
        $gameTitle = GameTitle::findOne(['GameId' => $gameId]);

        if ($gameTitle) {
          $articleGames[] = $gameTitle->GameId;

          $cheatsExistsQuery = Article::find()
            ->joinWith('articleGames')
            ->where([
              'game_id' => $gameTitle->GameId,
              'CommentId' => null,
              'data_type' => 'cheats',
            ]);

          if (!$article->isNewRecord) {
            $cheatsExistsQuery->andWhere(['NOT', ['base_id' => $article->base_id]]);
          }

          if (empty($article->CommentId) && $cheatsExistsQuery->exists()) {
            $cheatsParentId = (int)$cheatsExistsQuery->select('base_id')->scalar();
            $article->CommentId = $cheatsParentId;
          }
        } elseif (!$skipValidation) {
          $gameTitle = new GameTitle(['GameId' => $gameId]);
          $gameTitle->addError('GameId', 'Игра с таким названием не найдена в базе');
        }
      }

      $article->setScenario(Article::SCENARIO_CHEAT);
      if (!$article->data_active) {
        $article->data_active = 0;
        $article->ready_to_publish = (int)isset($post['submit_article_publish']);
      }

      /* @phpstan-ignore-next-line */
      if (!$article->user_id && $article->isNewRecord && ($article->user_id !== '0')) {
        $article->user_id = (int)Yii::$app->user->id;
      }

      // @phpstan-ignore-next-line
      if ($article->user_id === '0') {
        $article->user_id = null;
      }

      $article->data_text = new PresentationFormatter([
        'title' => $article->data_title,
        'data' => $article->editor_js_content ?? '[]',
        'page' => 'all',
      ])();

      if ($article->hasErrors() || $gameTitle?->hasErrors()
        || !$article->validate() || !$article->save()
      ) {
        throw new ControlFlowException('Не удалось сохранить читы');
      }

      $article->refresh();

      $requestIds = $post['game_request_id'] ?? [];
      $requestTitles = $post['game_request_title'] ?? [];

      GameRequestAttachment::deleteAll([
        'target_type' => 'show',
        'target_id' => $article->base_id,
      ]);

      ArticleGame::deleteAll([
        'article_id' => $article->base_id,
      ]);

      // Привязываем запросы на добавление игр
      foreach ($requestTitles as $requestTitle) {
        $foundGame = GameTitle::findOne(['GameName' => $requestTitle]);

        // Если уже есть игра с таким названием, то просто привязываем игру
        if ($foundGame) {
          $articleGames[] = $foundGame->GameId;
        } else {
          $foundRequest = GameRequest::findOne(['title' => $requestTitle]);

          // Если уже есть запрос с таким названием
          if ($foundRequest) {
            // И он уже обработан — привязываем игру
            if ($foundRequest->game_id) {
              $articleGames[] = $foundRequest->game_id;
              // И он ещё не обработан — привязываем материал к запросу
            } else {
              $requestIds[] = $foundRequest->id;
            }
            // Если запроса нет — создаём его и привязываем материал к запросу
          } else {
            $request = new GameRequest([
              'title' => $requestTitle,
              'user_id' => Yii::$app->user->id,
            ]);
            $request->save();
            $request->user?->recalculateCredibility();
            $requestIds[] = $request->id;
          }
        }
      }

      $requestIds = \array_unique($requestIds);
      foreach ($requestIds as $requestId) {
        $attachment = new GameRequestAttachment([
          'request_id' => $requestId,
          'target_type' => 'show',
          'target_id' => $article->base_id,
        ]);
        $attachment->save();
      }

      $articleGames = \array_unique($articleGames);
      foreach ($articleGames as $gameId) {
        $articleGame = new ArticleGame([
          'article_id' => $article->base_id,
          'game_id' => $gameId,
        ]);
        $articleGame->save();
      }

      if (\count($article->dirtyAttributes) > 0) {
        $article->save();
      }

      $article->clearShowCache();
      $article->clearUnpublishedCache();

      $url = '/admin/cheats/drafts';
      if ($article->ready_to_publish) {
        $url = '/admin/cheats/ready-to-publish';
      } elseif ($article->data_active) {
        $url = '/admin/cheats';
      }
      $autosaveId = $isNew ? 'new' : $article->base_id;
      Yii::$app->session->setFlash('remove-autosave', 'cheats-form-' . $autosaveId);
      return $this->redirect($url);
    } catch (Exception $exception) {
      captureException($exception);
      $this->view->title = "$article->title ⋅ Админка";
      $article->setScenario(Article::SCENARIO_DRAFT);
      $url = '/admin/cheats/add';
      if (!$isNew) {
        $url = "/admin/cheats/edit/$article->base_id";
      }

      $message = $exception->getMessage();
      if (!empty($message)) {
        $article->addError('data_id', $message);
      }

      $this->view->registerJs("window.history.replaceState({}, '$article->title ⋅ Админка', '$url')");
      return $this->renderEditor($article, $gameTitle);
    }
  }

  /**
   * @throws Throwable
   */
  protected function renderEditor(Article $article, ?GameTitle $gameTitle): string
  {
    $article->data_text = \html_entity_decode(SgStringHelper::br2nl($article->data_text ?? ''));
    $gameTitle?->setScenario(GameTitle::SCENARIO_ATTACH);
    $viewParams = \compact('article', 'gameTitle');
    return $this->render('editor', $viewParams);
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['cheats_manage'], methods: ['POST', 'GET'])]
  public function actionPublish(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Читы не найдены');
    }

    try {
      $article->publish();
      $article->notify();
    } catch (Exception $e) {
      Yii::$app->session->setFlash('error', $e->getMessage());
    }

    return $this->redirect('/admin/cheats/index');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['cheats_manage'], methods: ['POST', 'GET'])]
  public function actionUnpublish(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Читы не найдены');
    }

    $article->setScenario(Article::SCENARIO_CHEAT);
    $article->ready_to_publish = 0;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/cheats/drafts');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['cheats_create'], methods: ['POST', 'GET'])]
  public function actionToReview(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Читы не найдены');
    }

    $article->setScenario(Article::SCENARIO_CHEAT);
    $article->ready_to_publish = 1;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/cheats/ready-to-publish');
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException
   */
  #[Access(permissions: ['cheats_create'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Читы не найдены');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя удалить чужие читы');
    }

    if ($article->data_active || $article->ready_to_publish) {
      Yii::$app->session->setFlash('error', 'Нельзя удалить статью не являющуюся черновиком');
      return $this->redirect('/admin/cheats');
    }

    $article->delete();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/cheats/drafts');
  }
}
