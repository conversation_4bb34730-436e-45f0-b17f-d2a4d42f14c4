<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use yii\helpers\Json;
use common\models\{NewsTag,
  NewsData,
  NewsGame,
  GameTitle,
  ActionLog,
  GameRequest,
  PublishSchedule,
  GameRequestAttachment
};
use frontend\traits\Access;
use frontend\components\View;
use yii\caching\TagDependency;
use yii\data\ActiveDataProvider;
use yii\db\StaleObjectException;
use common\og_generators\LogoGenerator;
use common\helpers\{SgHelper, ImageHelper};
use frontend\modules\admin\AdminController;
use common\exceptions\ControlFlowException;
use common\widgets\formatters\PresentationFormatter;
use yii\web\{Request, Response, UploadedFile, NotFoundHttpException, ForbiddenHttpException, BadRequestHttpException};

use function Sentry\captureException;

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class NewsController extends AdminController
{
  protected int $unpublishedCount {
    get {
      $subQuery = PublishSchedule::find()
        ->select('target_id')
        ->where(['target_type' => 'news']);

      $query = NewsData::find()
        ->where(['news_active' => 0])
        ->andWhere([
          'not in',
          'news_id',
          $subQuery,
        ]);

      return (int)$query->count();
    }
  }
  protected int $plannedCount {
    get => (int)NewsData::find()
      ->innerJoinWith('schedule')
      ->where(['news_active' => 0])
      ->count();
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_access'], methods: ['GET'])]
  public function actionDrafts(): string
  {
    $this->view->title = 'Новости ⋅ Админка';

    $subQuery = PublishSchedule::find()
      ->select('target_id')
      ->where(['target_type' => 'news']);

    $dataProvider = new ActiveDataProvider([
      'query' => NewsData::find()
        ->where(['news_active' => 0])
        ->andWhere([
          'not in',
          'news_id',
          $subQuery,
        ]),
    ]);

    $unpublishedCount = $this->unpublishedCount;
    $plannedCount = $this->plannedCount;

    return $this->render('index', \compact('dataProvider', 'unpublishedCount', 'plannedCount'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_access'], methods: ['GET'])]
  public function actionPlanned(): string
  {
    $this->view->title = 'Новости ⋅ Админка';

    $dataProvider = new ActiveDataProvider([
      'query' => NewsData::find()
        ->innerJoinWith('schedule')
        ->where(['news_active' => 0]),
    ]);

    $unpublishedCount = $this->unpublishedCount;
    $plannedCount = $this->plannedCount;

    return $this->render('index', \compact('dataProvider', 'unpublishedCount', 'plannedCount'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['news_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $this->view->title = 'Новости ⋅ Админка';

    $dataProvider = new ActiveDataProvider([
      'query' => NewsData::find()
        ->orderBy(['news_date_for_pin' => \SORT_DESC]),
    ]);

    $unpublishedCount = $this->unpublishedCount;
    $plannedCount = $this->plannedCount;

    return $this->render('index', \compact('dataProvider', 'unpublishedCount', 'plannedCount'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['news_create'], methods: ['GET'])]
  public function actionAdd(): string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Новости', '/admin/news');
    Yii::$app->breadcrumbs->addElement('Новая новость');
    $newsData = new NewsData();
    return $this->render('editor', \compact('newsData'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['news_create'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Новости', '/admin/news');
    Yii::$app->breadcrumbs->addElement('Редактирование новости');
    $newsData = NewsData::findOne($id);

    if (!$newsData) {
      throw new NotFoundHttpException('Новость не найдена');
    }

    return $this->render('editor', \compact('newsData'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['news_create'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Новости', '/admin/news');
    $post = $this->request->post();

    if (!\array_key_exists('NewsData', $post)) {
      throw new BadRequestHttpException('Не удалось сохранить: переданы неверные данные');
    }

    $newsData = new NewsData();

    $id = $post['NewsData']['news_id'];
    if ($id) {
      $newsData = NewsData::findOne($id);

      if (!$newsData) {
        throw new NotFoundHttpException('Новость не найдена');
      }

      if (!$newsData->canEdit) {
        throw new ForbiddenHttpException('Нельзя редактировать чужую новость');
      }
    }

    $isNew = $newsData->isNewRecord;
    Yii::$app->breadcrumbs->addElement($isNew ? 'Новая новость' : 'Редактирование новости');

    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $newsData->load($post);

      if ($isNew) {
        $user = Yii::$app->user->identity;

        $newsData->news_author = $user->userName;
        $newsData->user_id = $user->id;
      }

      if ($newsData->editor_js_content) {
        $newsData->news_text = new PresentationFormatter([
          'title' => $newsData->news_title,
          'data' => $newsData->editor_js_content,
          'page' => 'all',
        ])();
      }

      $poster = UploadedFile::getInstance($newsData, 'news_logo');
      if ($poster && \in_array($poster->extension, NewsData::POSTER_EXTENSIONS)) {
        ImageHelper::open($poster->tempName)->resize(1280, 720)->save($poster->tempName);
        $paths = SgHelper::getUploadPaths('news');
        $poster->saveAs($paths['path']);
        $newsData->news_logo = $paths['url'];
      }

      $newsData->news_cost = (float)$newsData->calculateCost();

      if (!isset($post['NewsData']['deny_comments'])) {
        $newsData->deny_comments = 0;
      }

      if (!isset($post['NewsData']['press_release'])) {
        $newsData->press_release = 0;
      }

      if (!$newsData->validate() || !$newsData->save()) {
        throw new ControlFlowException('Не удалось сохранить новость');
      }

      $newsData->refresh();

      if ($newsData->news_logo) {
        try {
          $newsData->news_meta_logo = $newsData->generateMetaLogo();
        } catch (Exception $e) {
          // Если что-то сломалось при генерации — никак не реагируем на это
          captureException($e);
        }

        $paths = SgHelper::getUploadPaths('news');
        new LogoGenerator([
          'target' => $newsData,
          'path' => $paths['path']
        ])->run();
        $newsData->news_og_image = $paths['url'];
        $newsData->save();
      }

      NewsTag::deleteAll(['news_id' => $newsData->news_id]);

      $newsTags = $post['news_tags'] ?? [];

      foreach ($newsTags as $tagName) {
        $tag = new NewsTag([
          'news_id' => $newsData->news_id,
          'news_tag' => $tagName,
        ]);
        $tag->save();
      }

      $newsData->news_tags = \implode(',', $newsTags);
      $newsData->save();

      $games = $post['game_list'] ?? [];
      $requestIds = $post['game_request_id'] ?? [];
      $requestTitles = $post['game_request_title'] ?? [];

      GameRequestAttachment::deleteAll([
        'target_type' => 'news',
        'target_id' => $newsData->news_id,
      ]);

      // Привязываем запросы на добавление игр
      foreach ($requestTitles as $requestTitle) {
        $foundGame = GameTitle::findOne(['GameName' => $requestTitle]);

        // Если уже есть игра с таким названием, то просто привязываем игру
        if ($foundGame) {
          $games[] = $foundGame->GameId;
        } else {
          $foundRequest = GameRequest::findOne(['title' => $requestTitle]);

          // Если уже есть запрос с таким названием
          if ($foundRequest) {
            // И он уже обработан — привязываем игру
            if ($foundRequest->game_id) {
              $games[] = $foundRequest->game_id;
              // И он ещё не обработан — привязываем материал к запросу
            } else {
              $requestIds[] = $foundRequest->id;
            }
            // Если запроса нет — создаём его и привязываем материал к запросу
          } else {
            $request = new GameRequest([
              'title' => $requestTitle,
              'user_id' => Yii::$app->user->id,
            ]);
            $request->save();
            $request->user?->recalculateCredibility();
            $requestIds[] = $request->id;
          }
        }
      }

      $requestIds = \array_unique($requestIds);
      foreach ($requestIds as $requestId) {
        $attachment = new GameRequestAttachment([
          'request_id' => $requestId,
          'target_type' => 'news',
          'target_id' => $newsData->news_id,
        ]);
        $attachment->save();
      }

      NewsGame::deleteAll(['news_id' => $newsData->news_id]);

      $games = \array_unique($games);
      foreach ($games as $gameId) {
        $game = new NewsGame([
          'news_id' => $newsData->news_id,
          'GameId' => $gameId,
        ]);
        $game->save();
      }

      if (Yii::$app->cache) {
        TagDependency::invalidate(Yii::$app->cache, ["news_$newsData->news_id", 'news_on_main', 'unpublished_news']);
      }

      $autosaveId = $isNew ? 'new' : $newsData->news_id;
      Yii::$app->session->setFlash('remove-autosave', 'news-form-' . $autosaveId);
      return $this->redirect('/admin/news');
    } catch (Exception $exception) {
      $url = '/admin/news/add';
      if (!$isNew) {
        $url = "/admin/news/edit/$newsData->news_id";
      }

      $message = $exception->getMessage();
      if (!empty($message)) {
        $newsData->addError('news_id', $message);
      }
      $this->view->registerJs("window.history.replaceState({}, '$newsData->news_title ⋅ Админка', '$url')");
      return $this->render('editor', \compact('newsData'));
    }
  }

  /**
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_manage'], methods: ['POST', 'GET'])]
  public function actionPublish(string $id): Response
  {
    $newsData = NewsData::findOne($id);

    if (!$newsData) {
      throw new NotFoundHttpException('Новость не найдена');
    }

    $newsData->publish();
    $newsData->notify();

    return $this->redirect('/admin/news');
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @throws NotFoundHttpException
   * @throws ForbiddenHttpException
   */
  #[Access(permissions: ['news_create'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    $user = Yii::$app->user;
    $newsData = NewsData::findOne($id);

    if (!$newsData) {
      throw new NotFoundHttpException('Новость не найдена');
    }

    if (($newsData->user_id !== $user->id)
      && !$user->can('news_manage')
    ) {
      throw new ForbiddenHttpException('Нельзя удалить чужую новость');
    }

    if (Yii::$app->cache) {
      Yii::$app->cache->delete('news_on_main');
      TagDependency::invalidate(Yii::$app->cache, ["news_$newsData->news_id", 'news_on_main', 'unpublished_news']);
    }
    $newsData->delete();
    return $this->redirect('/admin/news');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_manage'], methods: ['POST', 'GET'])]
  public function actionShow(string $id): Response
  {
    return $this->changeOnMain($id, 1);
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_manage'], methods: ['POST', 'GET'])]
  public function actionHide(string $id): Response
  {
    return $this->changeOnMain($id, 0);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_manage'], methods: ['POST', 'GET'])]
  public function actionSchedule(string $id): Response|string
  {
    $newsData = NewsData::findOne($id);

    if (!$newsData) {
      throw new NotFoundHttpException('Новость не найдена');
    }

    $schedule = new PublishSchedule([
      'target_id' => $newsData->news_id,
      'target_type' => 'news',
      'announce' => 0,
      'notify' => 1,
    ]);

    if ($this->request->isPost) {
      $post = $this->request->post();
      $schedule->load($post);

      if (!isset($post['PublishSchedule']['announce'])) {
        $schedule->announce = 0;
      }

      if ($schedule->save()) {
        return $this->redirect('/admin/news');
      }
    }

    $isValid = $newsData->validate();

    return $this->render('schedule', \compact('newsData', 'schedule', 'isValid'));
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_manage'], methods: ['POST', 'GET'])]
  public function actionUnschedule(string $id): Response
  {
    $newsData = NewsData::findOne($id);

    if (!$newsData) {
      throw new NotFoundHttpException('Новость не найдена');
    }

    $schedule = PublishSchedule::findOne([
      'target_type' => 'news',
      'target_id' => $id,
    ]);

    $schedule?->delete();

    return $this->redirect('/admin/news');
  }

  /**
   * @param int<0,1> $onMain
   * @throws NotFoundHttpException|\yii\db\Exception
   */
  protected function changeOnMain(int|string $id, int $onMain): Response
  {
    $newsData = NewsData::findOne($id);

    if (!$newsData) {
      throw new NotFoundHttpException('Новость не найдена');
    }

    $newsData->news_on_main = $onMain;
    $newsData->save();
    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, ['news_on_main']);
    }
    if ($onMain === 0) {
      $log = new ActionLog([
        'user_id' => Yii::$app->user->id,
        'action_type' => ActionLog::NEWS_REMOVE_FROM_MAIN,
        'payload' => Json::encode(['news_id' => $newsData->news_id]),
      ]);
      $log->save();
    }
    return $this->redirect('/admin/news');
  }

  /**
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['send_to_telegram'], methods: ['POST', 'GET'])]
  public function actionSendToTelegram(string $id): Response
  {
    $newsItem = NewsData::findOne($id);

    if ($newsItem) {
      $newsItem->sendToTelegram();
      Yii::$app->session->addFlash(
        'success',
        "Новость «{$newsItem->title}» добавлена в очередь на отправку в Telegram.<br>Фактическая отправка произойдёт в течение 1-2 минут",
      );
    }

    return $this->redirect($this->request->referrer ?? '/admin/news');
  }

  /**
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['news_create'], methods: ['POST', 'GET'])]
  public function actionPreview(): string
  {
    $realNews = new NewsData([
      'news_title' => 'Здесь будут данные твоей новости',
      'news_date' => \date('Y-m-d H:i:s'),
      'news_date_for_pin' => \date('Y-m-d H:i:s'),
      'news_comments' => \random_int(0, 150),
    ]);

    if ($this->request->isPost) {
      $post = $this->request->post('NewsData');
      if (\array_key_exists('news_id', $post) && !!$post['news_id']) {
        $realNews = NewsData::findOne($post['news_id']);
        if (!$realNews) {
          throw new NotFoundHttpException('Новость не найдена');
        }
      }
      $realNews->load($this->request->post());

      $poster = UploadedFile::getInstance($realNews, 'news_logo');
      if ($poster && \in_array($poster->extension, NewsData::POSTER_EXTENSIONS)) {
        /** @noinspection BadExceptionsProcessingInspection */
        try {
          ImageHelper::open($poster->tempName)->resize(1280, 720)->save($poster->tempName);
          $paths = SgHelper::getUploadPaths('news');
          $poster->saveAs($paths['path']);
          $realNews->news_logo = $paths['url'];
        } catch (Exception) {
          // Do nothing
        }
      }
    }

    return $this->renderAjax('preview', \compact('realNews'));
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET', 'POST'])]
  public function actionAnnounce(string $id): Response
  {
    $news = NewsData::findOne($id);

    if (!$news) {
      throw new NotFoundHttpException('Новость не найдена');
    }

    try {
      $news->announce();
    } catch (Exception $e) {
      Yii::$app->session->setFlash('error', $e->getMessage());
    }

    return $this->redirect('/admin/news');
  }
}
