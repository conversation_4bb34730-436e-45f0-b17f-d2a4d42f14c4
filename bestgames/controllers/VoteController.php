<?php

namespace bestgames\controllers;

use Yii;
use yii\db\Exception;
use frontend\traits\Access;
use yii\base\InvalidConfigException;
use bestgames\helpers\BestgamesHelper;
use frontend\modules\ajax\ApiController;
use common\models\{Bestgames, BestgamesNominee, BestgamesNomination};
use yii\web\{HttpException, NotFoundHttpException, BadRequestHttpException};

/**
 * @noinspection PhpUnused
 */

class VoteController extends ApiController
{
  /**
   * @return array{
   *     ok: bool
   * }
   * @throws HttpException
   * @throws InvalidConfigException
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionIndex(int $nominationId, int $gameId): array
  {
    if (BestgamesHelper::isVotingEnd()) {
      throw new BadRequestHttpException('Голосование завершено');
    }

    $nomination = BestgamesNomination::findOne($nominationId);
    if (!$nomination) {
      throw new NotFoundHttpException('Номинация не найдена');
    }
    /** @var ?BestgamesNominee $nominee */
    $nominee = $nomination->getNominees()->where(['id' => $gameId])->one();
    if (!$nominee) {
      throw new BadRequestHttpException('Этот номинант не участвует в указанной номинации');
    }

    $vote = Yii::$app->user->identity->getVotes()
      ->where(['cats' => $nomination->id])
      ->one();

    if (!$vote) {
      $vote = new Bestgames([
        'user_id' => Yii::$app->user->id,
        'cats' => $nomination->id,
      ]);
    }

    $vote->nominees = $nominee->id;

    $vote->save();

    return ['ok' => true];
  }
}
