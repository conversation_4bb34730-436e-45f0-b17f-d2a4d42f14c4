<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use Throwable;
use yii\db\Expression;
use common\models\Faq;
use common\models\Game;
use common\models\User;
use common\models\Topic;
use ReflectionException;
use common\models\Article;
use common\models\Comment;
use common\models\NewsData;
use common\models\Platform;
use frontend\traits\Access;
use common\helpers\SgHelper;
use common\models\GameTitle;
use yii\helpers\ArrayHelper;
use common\models\GameSeries;
use common\enums\FaqSections;
use common\models\Compilation;
use common\helpers\SearchHelper;
use common\helpers\SgTypeHelper;
use common\helpers\SgImageHelper;
use common\components\ModelLoader;
use common\helpers\SectionsHelper;
use common\helpers\SgDateTimeHelper;
use yii\base\InvalidConfigException;
use frontend\modules\ajax\ApiController;
use common\components\Sphinx\Enums\SortModes;

use function Sentry\captureException;

/**
 * @noinspection PhpUnused
 *
 * @property-read string $term Смотри {@link self::getTerm}
 */
class SearchController extends ApiController
{
  /** @var string[] */
  protected array $nonArticleDataUrl = ['video'];

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         type: string,
   *         id: int,
   *         title: string,
   *         square_poster: string,
   *         poster: string,
   *         rating?: string,
   *         url: string,
   *         platforms?: Platform[],
   *         date?: string,
   *         status?: int,
   *         games_count?: int
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws ReflectionException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionGames(): array
  {
    $term = $this->term;
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => ['date:desc'],
      '-date' => ['date:asc'],
      'name' => ['title:asc'],
      '-name' => ['title:desc']
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['games'] = SearchHelper::searchGames($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'games', $order);
    }

    $newGames = [];
    if (\count($results['games']['matches']) > 0) {
      $gameIds = ArrayHelper::getColumn($results['games']['matches'], 'TitleId');
      $gameIdList = \implode(',', \array_filter($gameIds, static fn($id) => $id !== null));
      $seriesIds = ArrayHelper::getColumn($results['games']['matches'], 'id');
      $seriesIdList = \implode(',', \array_filter($seriesIds, static fn($id) => $id !== null));

      $games = [];
      $series = [];

      if ($gameIdList !== '') {
        /** @var GameTitle[] $games */
        $games = GameTitle::find()
          ->with('info.rating', 'info.mainTitle', 'info.platforms')
          ->joinWith('info')
          ->where(['TitleId' => $gameIds])
          ->andWhere(['NOT', ['stopgame_info.GameId' => null]])
          ->indexBy('TitleId')
          ->all();

        $gameIds = ArrayHelper::getColumn($games, 'GameId');

        $trackStatuses = [];

        if (Yii::$app->user->isLoggedIn && Yii::$app->user->identity) {
          /** @var array<int, int> $trackStatuses */
          $trackStatuses = Yii::$app->user->identity->getTracks()
            ->select(['status'])
            ->andWhere(['GameId' => $gameIds])
            ->indexBy('GameId')
            ->column();
        }
      }

      if ($seriesIdList !== '') {
        /** @var GameSeries[] $series */
        $series = GameSeries::find()
          ->where(['id' => $seriesIds])
          ->indexBy('id')
          ->all();
      }

      foreach ($results['games']['matches'] as $document) {
        switch ($document['isSeries']) {
          case 1:
            $seriesInfo = $series[$document['id']] ?? null;
            if (!$seriesInfo) {
              continue 2;
            }
            $yearsInfo = $seriesInfo->getGames()
              ->select([
                'min' => new Expression('EXTRACT(YEAR FROM MIN(GameDate))'),
                'max' => new Expression('EXTRACT(YEAR FROM MAX(GameDate))'),
              ])
              ->andWhere(['>', 'GameDate', '1900-01-01'])
              ->asArray()
              ->one();

            $gamesCount = $seriesInfo->getGames()->count();

            $gameYears = '';

            if ($yearsInfo) {
              $gameYears = ($yearsInfo['min'] === $yearsInfo['max']) ? $yearsInfo['min'] : ($yearsInfo['min'] . ' - ' . $yearsInfo['max']);
            }

            $newGames[] = [
              'type' => 'series',
              'id' => $seriesInfo->id,
              'title' => $seriesInfo->name,
              'square_poster' => SgImageHelper::thumb($seriesInfo->square_poster, 280, 280),
              'poster' => SgImageHelper::thumb($seriesInfo->large_poster, 262, 186),
              'url' => $seriesInfo->getFullUrl(),
              'games_count' => (int)($gamesCount ?? 0),
              'games_years' => $gameYears,
            ];
            break;
          case 0:
          default:
            $gameTitle = $games[$document['TitleId']] ?? null;
            if (!$gameTitle || !$gameTitle->info || !$gameTitle->info->mainTitle) {
              continue 2;
            }
            $newGames[] = [
              'type' => 'game',
              'id' => $gameTitle->GameId,
              'title' => SgHelper::theFix($gameTitle->GameName),
              'square_poster' => SgImageHelper::thumb(
                $gameTitle->info->square_poster ?? $gameTitle->info->mainTitle->poster,
                280,
                280,
              ),
              'poster' => SgImageHelper::thumb(
                $gameTitle->info->large_poster ?? $gameTitle->info->mainTitle->poster,
                262,
                186,
              ),
              'rating' => $gameTitle->info->rating ? $gameTitle->info->rating->showUserRating() : '0',
              'url' => $gameTitle->info->mainTitle->fullUrl,
              'platforms' => $gameTitle->info->platforms,
              'date' => $gameTitle->info->releaseDate,
              'status' => $trackStatuses[$gameTitle->GameId] ?? -1,
            ];
            break;
        }
      }
      unset($games);
    }

    return [
      'term' => $term,
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $newGames,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => \count($results['games']['matches']) < 15,
    ];
  }

  /**
   * @return array{
   *     total: array<string, string>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         square_poster: string,
   *         rating: string,
   *         url: string,
   *         platforms: Platform[],
   *         date: string,
   *         status: int
   *     }>,
   *     isLoggedIn: bool
   * }
   * @noinspection PhpUnused
   * @throws ReflectionException
   * @throws Exception
   */
  #[Access(methods: ['GET'])]
  public function actionGamesSimple(): array
  {
    $term = $this->getTerm();

    $results = $this->queryAllSimple($term);

    $newGames = [];
    if (\count($results['games']['matches']) > 0) {
      $gameIds = ArrayHelper::getColumn($results['games']['matches'], 'GameId');
      $idList = \implode(',', $gameIds);

      /** @var Game[] $games */
      $games = Game::find()
        ->with('mainTitle', 'rating')
        ->where(['GameId' => $gameIds])
        ->orderBy(new Expression("FIELD (GameId, $idList)"))
        ->all();

      $gameIds = ArrayHelper::getColumn($games, 'GameId');

      $trackStatuses = [];

      if (Yii::$app->user->isLoggedIn && Yii::$app->user->identity) {
        /** @var array<int, int> $trackStatuses */
        $trackStatuses = Yii::$app->user->identity->getTracks()
          ->select(['status'])
          ->andWhere(['GameId' => $gameIds])
          ->indexBy('GameId')
          ->column();
      }

      foreach ($games as $game) {
        if (!$game->mainTitle) {
          continue;
        }

        $newGames[] = [
          'id' => $game->GameId,
          'title' => SgHelper::theFix($game->mainTitle->GameName),
          'square_poster' => SgImageHelper::thumb($game->square_poster ?? $game->mainTitle->poster, 280, 280),
          'poster' => SgImageHelper::thumb($game->mainTitle->poster, 262, 186),
          'rating' => $game->rating?->showUserRating() ?? '0',
          'url' => $game->mainTitle->getFullUrl(),
          'platforms' => $game->platforms,
          'date' => $game->releaseDate,
          'status' => $trackStatuses[$game->GameId] ?? -1,
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $newGames,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionNews(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => [SortModes::ATTR_DESC, 'date'],
      '-date' => [SortModes::ATTR_ASC, 'date'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['news'] = SearchHelper::searchNews($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'news', $order);
    }

    if ($order === $sortOptions['relevance']) {
      \uasort($results['news']['matches'], static fn($a, $b) => $b['weight'] <=> $a['weight']);
    }

    $resultItems = [];
    if (\count($results['news']['matches']) > 0) {
      $ids = \array_keys($results['news']['matches']);

      /** @var NewsData[] $items */
      $items = NewsData::find()
        ->where(['news_id' => $ids])
        ->orderBy(new Expression('FIELD(news_id, ' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->news_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 218, 124),
          'url' => $item->fullUrl,
          'icon' => $item->iconV2,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->news_date, hideTimeIfDateIsFull: true),
          'dataType' => 'news',
          'comments' => $item->commentCount
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => ($results['news']['total'] ?? 0) < 15,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionNewsSimple(): array
  {
    $term = $this->getTerm();

    $results = $this->queryAllSimple($term);

    $resultItems = [];
    if (\count($results['news']['matches']) > 0) {
      $ids = ArrayHelper::getColumn($results['news']['matches'], 'news_id');

      /** @var NewsData[] $items */
      $items = NewsData::find()
        ->where(['news_id' => $ids])
        ->orderBy(new Expression('FIELD(news_id, ' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->news_id,
          'title' => $item->news_title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->news_date),
          'dataType' => 'news',
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionArticles(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => [SortModes::ATTR_DESC, 'date'],
      '-date' => [SortModes::ATTR_ASC, 'date'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['articles'] = SearchHelper::searchArticles($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'articles', $order);
    }

    if ($order === $sortOptions['relevance']) {
      \uasort($results['articles']['matches'], static fn($a, $b) => $b['weight'] <=> $a['weight']);
    }

    $resultItems = [];
    if (\count($results['articles']['matches']) > 0) {
      $ids = \array_keys($results['articles']['matches']);

      /** @var Article[] $items */
      /** @noinspection DuplicatedCode */
      $items = Article::find()
        ->with('gameTitle')
        ->where(['base_id' => $ids])
        ->orderBy(new Expression('FIELD(base_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->base_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 218, 124),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->data_add, hideTimeIfDateIsFull: true),
          'dataType' => 'show',
          'comments' => $item->commentCount
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => ($results['articles']['total'] ?? 0) < 15,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionArticlesSimple(): array
  {
    $term = $this->getTerm();

    $results = $this->queryAllSimple($term);

    $resultItems = [];
    if (\count($results['articles']['matches']) > 0) {
      $ids = ArrayHelper::getColumn($results['articles']['matches'], 'base_id');

      /** @var Article[] $items */
      /** @noinspection DuplicatedCode */
      $items = Article::find()
        ->with('gameTitle')
        ->where(['base_id' => $ids])
        ->orderBy(new Expression('FIELD(base_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->base_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? 'неизвестный раздел',
          'date' => SgDateTimeHelper::relativeTimestamp($item->data_add),
          'dataType' => 'show',
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionVideo(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => [SortModes::ATTR_DESC, 'date'],
      '-date' => [SortModes::ATTR_ASC, 'date'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['video'] = SearchHelper::searchVideo($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'video', $order);
    }

    if ($order === $sortOptions['relevance']) {
      \uasort($results['video']['matches'], static fn($a, $b) => $b['weight'] <=> $a['weight']);
    }

    $resultItems = [];
    if (\count($results['video']['matches']) > 0) {
      $ids = \array_keys($results['video']['matches']);

      /** @var Article[] $items */
      /** @noinspection DuplicatedCode */
      $items = Article::find()
        ->with('gameTitle')
        ->where(['base_id' => $ids])
        ->orderBy(new Expression('FIELD(base_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->base_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 218, 124),
          'url' => $item->getFullUrl(),
          'icon' => 'video',
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->data_add, hideTimeIfDateIsFull: true),
          'dataType' => 'show',
          'comments' => $item->commentCount
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => ($results['video']['total'] ?? 0) < 15,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionVideoSimple(): array
  {
    $term = $this->getTerm();
    $results = $this->queryAllSimple($term);

    $resultItems = [];
    if (\count($results['video']['matches']) > 0) {
      $ids = ArrayHelper::getColumn($results['video']['matches'], 'base_id');

      /** @var Article[] $items */
      /** @noinspection DuplicatedCode */
      $items = Article::find()
        ->with('gameTitle')
        ->where(['base_id' => $ids])
        ->orderBy(new Expression('FIELD(base_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->base_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->data_add),
          'dataType' => 'show',
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionCheats(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => [SortModes::ATTR_DESC, 'date'],
      '-date' => [SortModes::ATTR_ASC, 'date'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['cheats'] = SearchHelper::searchCheats($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'cheats', $order);
    }

    if ($order === $sortOptions['relevance']) {
      \uasort($results['cheats']['matches'], static fn($a, $b) => $b['weight'] <=> $a['weight']);
    }

    $resultItems = [];
    if (\count($results['cheats']['matches']) > 0) {
      $ids = \array_keys($results['cheats']['matches']);

      /** @var Article[] $items */
      /** @noinspection DuplicatedCode */
      $items = Article::find()
        ->with('gameTitle')
        ->where(['base_id' => $ids])
        ->orderBy(new Expression('FIELD(base_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->base_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 218, 124),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->data_add, showTime: false),
          'dataType' => 'show',
          'comments' => $item->commentCount
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => ($results['cheats']['total'] ?? 0) < 15,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionCheatsSimple(): array
  {
    $term = $this->getTerm();

    $results = $this->queryAllSimple($term);

    $resultItems = [];
    if (\count($results['cheats']['matches']) > 0) {
      $ids = ArrayHelper::getColumn($results['cheats']['matches'], 'base_id');

      /** @var Article[] $items */
      /** @noinspection DuplicatedCode */
      $items = Article::find()
        ->with('gameTitle')
        ->where(['base_id' => $ids])
        ->orderBy(new Expression('FIELD(base_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->base_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->data_add),
          'dataType' => 'show',
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionBlogs(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => [SortModes::ATTR_DESC, 'date'],
      '-date' => [SortModes::ATTR_ASC, 'date'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['blogs'] = SearchHelper::searchBlogs($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'blogs', $order);
    }

    if ($order === $sortOptions['relevance']) {
      \uasort($results['blogs']['matches'], static fn($a, $b) => $b['weight'] <=> $a['weight']);
    }

    $resultItems = [];
    if (\count($results['blogs']['matches']) > 0) {
      $ids = \array_keys($results['blogs']['matches']);

      /** @var Topic[] $items */
      $items = Topic::find()
        ->with(['blog'])
        ->where(['topic_id' => $ids])
        ->orderBy(new Expression('FIELD(topic_id, ' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->topic_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 218, 124),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->topic_date_published, hideTimeIfDateIsFull: true),
          'dataType' => 'topic',
          'comments' => $item->commentCount
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => ($results['blogs']['total'] ?? 0) < 15,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }>,
   *     isLoggedIn: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionBlogsSimple(): array
  {
    $term = $this->getTerm();

    $results = $this->queryAllSimple($term);

    $resultItems = [];
    if (\count($results['blogs']['matches']) > 0) {
      $ids = ArrayHelper::getColumn($results['blogs']['matches'], 'topic_id');

      /** @var Topic[] $items */
      $items = Topic::find()
        ->with(['blog'])
        ->where(['topic_id' => $ids])
        ->orderBy(new Expression('FIELD(topic_id, ' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->topic_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->topic_date_published),
          'dataType' => 'topic',
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         url: string,
   *         date: string,
   *         text: string
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionFaq(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => [SortModes::ATTR_DESC, 'date'],
      '-date' => [SortModes::ATTR_ASC, 'date'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['faq'] = SearchHelper::searchFaq($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'faq', $order);
    }

    if ($order === $sortOptions['relevance']) {
      \uasort($results['faq']['matches'], static fn($a, $b) => $b['weight'] <=> $a['weight']);
    }

    $resultItems = [];
    if (\count($results['faq']['matches']) > 0) {
      $ids = \array_keys($results['faq']['matches']);

      /** @var Faq[] $items */
      $items = Faq::find()
        ->with('user')
        ->where(['faq_id' => $ids])
        ->orderBy(new Expression('FIELD(faq_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $section = FaqSections::tryFrom($item->section ?? 'offtopic');
        if (!$section) {
          $section = FaqSections::OFFTOPIC;
        }

        $resultItems[] = [
          'id' => $item->faq_id,
          'title' => $item->title,
          'url' => $item->getFullUrl(),
          'date' => SgDateTimeHelper::relativeTimestamp($item->faq_date_add, hideTimeIfDateIsFull: true),
          'text' => \strip_tags($item->faq_text),
          'sectionName' => $section->title(),
          'views' => $item->viewCount,
          'comments' => $item->commentCount,
          'status' => !$item->faq_closed ? 'open' : ($item->answer_comment_id ? 'solved' : 'closed')
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => ($results['faq']['total'] ?? 0) < 15,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         url: string,
   *         date: string,
   *         text: string
   *     }>,
   *     isLoggedIn: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionFaqSimple(): array
  {
    $term = $this->getTerm();

    $results = $this->queryAllSimple($term);

    $resultItems = [];
    if (\count($results['faq']['matches']) > 0) {
      $ids = ArrayHelper::getColumn($results['faq']['matches'], 'faq_id');

      /** @var Faq[] $items */
      $items = Faq::find()
        ->with('user')
        ->where(['faq_id' => $ids])
        ->orderBy(new Expression('FIELD(faq_id,' . \implode(', ', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $resultItems[] = [
          'id' => $item->faq_id,
          'title' => $item->title,
          'url' => $item->getFullUrl(),
          'date' => SgDateTimeHelper::relativeTimestamp($item->faq_date_add),
          'text' => \strip_tags($item->faq_text),
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         url: string,
   *         poster: string,
   *         date: string,
   *         avatar: string,
   *         rating: int,
   *         comments: int,
   *         inTeam: bool,
   *         isBanned: bool
   *     }>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionUsers(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => ['date:desc'],
      '-date' => ['date:asc'],
      'name' => ['resulting_name:asc'],
      '-name' => ['resulting_name:desc']
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['users'] = SearchHelper::searchUsers($term, [
        'limit' => 12,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'users', $order);
    }

    $resultItems = [];
    if ($results['users']['total_found'] > 0) {
      $userIds = ArrayHelper::getColumn($results['users']['matches'], 'id');
      $usersQuery = User::find()
        ->where(['id' => $userIds])
        ->orderBy(new Expression('FIELD(id,' . \implode(', ', $userIds) . ')'));

      /** @var User[] $users */
      $users = $usersQuery->limit(12)->all();

      foreach ($users as $user) {
        $avatar = $user->getAvatar(120);

        $resultItems[] = [
          'id' => $user->id,
          'title' => $user->userName,
          'url' => $user->profileUrl,
          'poster' => $user->user_poster ? SgImageHelper::thumb($user->user_poster, 262, 100) : '',
          'date' => Yii::$app->formatter->asDate($user->user_register, 'php:d.m.Y'),
          'avatar' => $avatar,
          'rating' => (int)\round($user->user_rating ?? 0.0),
          'comments' => $user->user_comments,
          'inTeam' => $user->inTeam,
          'isBanned' => $user->isBanned,
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => \count($results['users']['matches']) < 12,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array{
   *         id: int,
   *         title: string,
   *         url: string,
   *         poster: string,
   *         date: string,
   *         avatar: string,
   *         rating: int,
   *         comments: int,
   *         inTeam: bool,
   *         isBanned: bool
   *     }>,
   *     isLoggedIn: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionUsersSimple(): array
  {
    $term = $this->getTerm();

    $results = $this->queryAll($term, 'users', null);
    $usersQuery = User::find()
      ->where([
        'OR',
        ['like', 'user_name', $term],
        ['like', 'show_name', $term],
      ])
      ->orderBy(['user_rating' => \SORT_DESC]);

    /** @var User[] $users */
    $users = $usersQuery->limit(12)->all();

    $resultItems = [];
    foreach ($users as $user) {
      $avatar = $user->getAvatar(120);

      $resultItems[] = [
        'id' => $user->id,
        'title' => $user->userName,
        'url' => $user->profileUrl,
        'poster' => $user->user_poster ? SgImageHelper::thumb($user->user_poster, 262, 100) : '',
        'date' => Yii::$app->formatter->asDate($user->user_register, 'php:d.m.Y'),
        'avatar' => $avatar,
        'rating' => (int)\round($user->user_rating ?? 0.0),
        'comments' => $user->user_comments,
        'inTeam' => $user->inTeam,
        'isBanned' => $user->isBanned,
      ];
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
    ];
  }

  /**
   * @return array{
   *     total: array<string, int>,
   *     results: array<array<string, mixed>>,
   *     isLoggedIn: bool,
   *     isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionComments(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'date' => [SortModes::ATTR_DESC, 'date'],
      '-date' => [SortModes::ATTR_ASC, 'date'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['comments'] = SearchHelper::searchComments($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'comments', $order);
    }

    $resultItems = [];
    if (\count($results['comments']['matches']) > 0) {
      $commentIds = \array_keys($results['comments']['matches']);
      $idList = \implode(',', $commentIds);

      /** @var Comment[] $comments */
      $comments = Comment::find()
        ->where([
          'comment_id' => $commentIds,
          'comment_publish' => 1,
        ])
        ->orderBy(new Expression("FIELD(comment_id, $idList)"))
        ->all();

      foreach ($comments as $comment) {
        $resultItems[] = ArrayHelper::merge($comment->toAjaxArray(), [
          'levels' => [],
          'level' => 0,
          'author' => $comment->user?->toArray([], [], true, true) ?? [],
        ]);
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => \count($results['comments']['matches']) < 15,
    ];
  }

  /**
   * @return array{
   *   total: array<string, int>,
   *   results: array<array{
   *     id: int,
   *     title: string,
   *     url: string,
   *     description: ?string,
   *     games_count: int,
   *     gameImages: array<string>,
   *     user: array{
   *       name: string,
   *       avatar: string
   *     }
   *   }>,
   *   isLoggedIn: bool,
   *   isFinal: bool
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['GET'])]
  public function actionCompilations(): array
  {
    $term = $this->getTerm();
    $offset = (int)$this->request->get('offset', 0);
    $sort = (string)$this->request->get('sort', '');

    $sortOptions = [
      'relevance' => null,
      'rating' => ['rating:desc'],
      'date' => ['date:desc'],
      '-date' => ['date:asc'],
    ];

    $order = $sortOptions['relevance'];

    if (!empty($sort) && isset($sortOptions[$sort])) {
      $order = $sortOptions[$sort];
    }

    if ($offset > 0) {
      $results = [];
      $results['compilations'] = SearchHelper::searchCompilations($term, [
        'limit' => 15,
        'offset' => $offset,
        'sort' => $order
      ]);
    } else {
      $results = $this->queryAll($term, 'compilations', $order);
    }

    $resultItems = [];
    if (\count($results['compilations']['matches']) > 0) {
      $ids = ArrayHelper::getColumn($results['compilations']['matches'], 'id');

      /** @var Compilation[] $items */
      $items = Compilation::find()
        ->with('user')
        ->where(['id' => $ids])
        ->orderBy(new Expression('FIELD(id,' . \implode(',', $ids) . ')'))
        ->all();

      foreach ($items as $item) {
        $compilationItemsCount = (int)$item->getCompilationItems()->count();
        $gameIds = $item->frontGameIds;
        if (empty($gameIds)) {
          $gameIds = $item
            ->getCompilationItems()
            ->select('target_id')
            ->leftJoin('stopgame_info', 'stopgame_info.GameId = compilation_items.target_id')
            ->where(['target_type' => 'game'])
            ->andWhere(['not', ['stopgame_info.GameId' => null]])
            ->orderBy(['compilation_items.order' => \SORT_ASC])
            ->limit(5)
            ->column();
        }

        $games = ModelLoader::getBatch(Game::class, $gameIds);

        \usort(
          $games,
          static fn(Game $a, Game $b): int => \array_search(
              $a->GameId,
              $gameIds
            ) <=> \array_search(
              $b->GameId,
              $gameIds
            )
        );
        $games = \array_reverse($games);

        $gameImages = [];
        for ($i = 0; $i < 5; $i++) {
          $itemKey = 4 - $i;
          if (!isset($games[$itemKey])) {
            $gameImages[] = '';
            continue;
          }
          $gameImages[] = isset($games[$itemKey]->mainTitle->poster)
            ? SgImageHelper::thumb($games[$itemKey]->mainTitle->poster, 126, 126)
            : SgImageHelper::placeholderUrl(126, 126);
        }

        $resultItems[] = [
          'id' => $item->id,
          // 'title' => $searchItem['_formatted']['title'],
          'title' => \htmlentities($item->title),
          'url' => $item->getFullUrl(),
          // 'description' => $searchItem['_formatted']['description'],
          'description' => \htmlentities($item->description ?? ''),
          'games_count' => $compilationItemsCount,
          'gameImages' => $gameImages,
          'isFavourite' => !empty($item->currentUserFavourite),
          'rating' => $item->rating,
          'canVote' => Yii::$app->user->id !== $item->user_id,
          'userVote' => $item->voteValue,
          'user' => [
            'name' => $item->user->userName ?? '[Удалённый пользователь]',
            'avatar' => $item->user->avatar ?? SgImageHelper::placeholderUrl(32, 32, 'avatar'),
            'inTeam' => $item->user->inTeam ?? false,
            'isBlogWinner' => $item->user->isBlogWinner ?? false
          ]
        ];
      }
    }

    return [
      'total' => ArrayHelper::getColumn($results, 'total_found'),
      'results' => $resultItems,
      'isLoggedIn' => Yii::$app->user->isLoggedIn,
      'isFinal' => $results['compilations']['total_found'] < 20
    ];
  }

  /**
   * @return array{
   *     success: bool,
   *     message?: string,
   *     result?: array{
   *         id: int,
   *         title: string,
   *         subtitle: string,
   *         poster: string,
   *         url: string,
   *         icon: string,
   *         sectionName: string,
   *         date: string,
   *         dataType: string
   *     }
   * }
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(methods: ['POST'])]
  public function actionGetByUrl(): array
  {
    $url = $this->request->post('url');
    if (empty($url)) {
      return ['success' => false, 'message' => 'Пустая ссылка'];
    }
    $url = (array)\parse_url($url);
    if (empty($url) || empty($url['host']) || (Yii::$app->params['domain'] !== "https://{$url['host']}")) {
      return ['success' => false, 'message' => 'Неверный домен'];
    }
    $path = '';
    if (!empty($url['path'])) {
      $path = \trim($url['path'], '/');
    }
    $path = \explode('/', $path);
    if ($path[0] === 'newsdata') {
      $id = (int)$path[1];
      $item = NewsData::findOne($id);

      if (!$item) {
        return ['success' => false, 'message' => 'Новость не найдена'];
      }

      return [
        'success' => true,
        'result' => [
          'id' => $item->news_id,
          'title' => $item->news_title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->news_date),
          'dataType' => 'news',
        ],
      ];
    }
    if ($path[0] === 'show') {
      $id = (int)$path[1];
      $item = Article::findOne($id);

      if (!$item) {
        return ['success' => false, 'message' => 'Материал не найден'];
      }

      return [
        'success' => true,
        'result' => [
          'id' => $item->base_id,
          'title' => $item->data_title ?? '[материал без заголовка]',
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->data_add),
          'dataType' => 'show',
        ],
      ];
    }
    if (($path[0] === 'blogs') && ($path[1] === 'topic')) {
      $id = (int)$path[2];
      $item = Topic::findOne($id);

      if (!$item) {
        return ['success' => false, 'message' => 'Блог не найден'];
      }

      return [
        'success' => true,
        'result' => [
          'id' => $item->topic_id,
          'title' => $item->title,
          'subtitle' => $item->subtitle,
          'poster' => SgImageHelper::thumb($item->poster, 126, 100),
          'url' => $item->getFullUrl(),
          'icon' => $item->icon,
          'sectionName' => $item->sectionNameSingular ?? '[неизвестный раздел]',
          'date' => SgDateTimeHelper::relativeTimestamp($item->topic_date_published),
          'dataType' => 'topic',
        ],
      ];
    }
    return ['success' => false, 'message' => 'Неверный тип материала'];
  }

  /**
   * @return array{
   *     total_found: int,
   *     total?: int,
   *     matches: array<mixed>
   * }
   * @throws InvalidConfigException
   */
  protected function queryNewsSimple(string $term): array
  {
    $query = NewsData::find()
      ->select(['news_id'])
      ->where(['like', 'news_title', $term])
      ->andWhere(['news_active' => 1])
      ->orderBy(['news_date' => \SORT_DESC]);

    $count = (int)$query->count();

    if ($count === 0) {
      return ['total_found' => 0, 'total' => 0, 'matches' => []];
    }

    $matches = $query->limit(15)->all();
    return [
      'total_found' => $count,
      'total' => \count($matches),
      'matches' => $matches,
    ];
  }

  /**
   * @return array{
   *     total_found: int,
   *     total?: int,
   *     matches: array<mixed>
   * }
   * @throws InvalidConfigException
   */
  protected function queryArticlesSimple(string $term): array
  {
    $query = Article::find()
      ->select(['base_id'])
      ->where(['like', 'data_title', $term])
      ->andWhere(['data_type' => SectionsHelper::getArticleSections()])
      ->andWhere([
        'OR',
        ['NOT', ['data_url' => $this->nonArticleDataUrl]],
        ['data_url' => null],
      ])
      ->andWhere(['data_active' => 1])
      ->orderBy(['data_add' => \SORT_DESC]);

    $count = (int)$query->count();

    if ($count === 0) {
      return ['total_found' => 0, 'total' => 0, 'matches' => []];
    }

    $matches = $query->limit(15)->all();
    return [
      'total_found' => $count,
      'total' => \count($matches),
      'matches' => $matches,
    ];
  }

  /**
   * @return array{
   *     total_found: int,
   *     total?: int,
   *     matches: array<mixed>
   * }
   * @throws InvalidConfigException
   */
  protected function queryVideoSimple(string $term): array
  {
    $query = Article::find()
      ->select(['base_id'])
      ->where(['like', 'data_title', $term])
      ->andWhere([
        'OR',
        ['data_type' => SectionsHelper::getVideoSections(exclude: ['trailers'])],
        [
          'data_type' => SectionsHelper::getArticleSections(),
          'data_url' => $this->nonArticleDataUrl,
        ],
      ])
      ->andWhere(['data_active' => 1])
      ->orderBy(['data_add' => \SORT_DESC]);

    $count = (int)$query->count();

    if ($count === 0) {
      return ['total_found' => 0, 'total' => 0, 'matches' => []];
    }

    $matches = $query->limit(15)->all();
    return [
      'total_found' => $count,
      'total' => \count($matches),
      'matches' => $matches,
    ];
  }

  /**
   * @return array{
   *     total_found: int,
   *     total?: int,
   *     matches: array<mixed>
   * }
   * @throws InvalidConfigException
   */
  protected function queryCheatsSimple(string $term): array
  {
    $query = Article::find()
      ->select(['base_id'])
      ->where(['like', 'data_title', $term])
      ->andWhere(['data_type' => ['cheats', 'solution', 'trainer']])
      ->andWhere(['data_active' => 1])
      ->orderBy(['data_add' => \SORT_DESC]);

    $count = (int)$query->count();

    if ($count === 0) {
      return ['total_found' => 0, 'total' => 0, 'matches' => []];
    }

    $matches = $query->limit(15)->all();
    return [
      'total_found' => $count,
      'total' => \count($matches),
      'matches' => $matches,
    ];
  }

  /**
   * @return array{
   *     total_found: int,
   *     total?: int,
   *     matches: array<mixed>
   * }
   * @throws InvalidConfigException
   */
  protected function queryBlogsSimple(string $term): array
  {
    $query = Topic::find()
      ->where(['like', 'topic_title', $term])
      ->andWhere(['topic_deleted' => 0, 'topic_publish' => 1])
      ->orderBy(['topic_date_published' => \SORT_DESC]);

    $count = (int)$query->count();

    if ($count === 0) {
      return ['total_found' => 0, 'total' => 0, 'matches' => []];
    }

    $matches = $query->limit(15)->all();
    return [
      'total_found' => $count,
      'total' => \count($matches),
      'matches' => $matches,
    ];
  }

  /**
   * @return array{
   *     total_found: int,
   *     total?: int,
   *     matches: array<mixed>
   * }
   * @throws InvalidConfigException
   */
  protected function queryFaqSimple(string $term): array
  {
    $query = Faq::find()
      ->where(['like', 'faq_title', $term])
      ->andWhere(['faq_deleted' => 0])
      ->orderBy(['faq_date_add' => \SORT_DESC]);

    $count = (int)$query->count();

    if ($count === 0) {
      return ['total_found' => 0, 'total' => 0, 'matches' => []];
    }

    $matches = $query->limit(15)->all();
    return [
      'total_found' => $count,
      'total' => \count($matches),
      'matches' => $matches,
    ];
  }

  /**
   * @param null|array{SortModes, string}|array{string} $order
   * @return array{
   *   games: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   news: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   articles: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   video: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   cheats: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   blogs: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   faq: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   users: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   comments: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   },
   *   compilations: array{
   *     total_found: int,
   *     total?: int,
   *     matches: mixed[]
   *   }
   * }
   * @throws Exception
   */
  protected function queryAll(string $term, ?string $category, mixed $order): array
  {
    $options = [];
    if (!empty($category) && !empty($order)) {
      $options[$category]['sort'] = $order;
    }

    return [
      // @phpstan-ignore-next-line
      'games' => SearchHelper::searchGames($term, $options['games'] ?? []),
      // @phpstan-ignore-next-line
      'news' => SearchHelper::searchNews($term, $options['news'] ?? []),
      // @phpstan-ignore-next-line
      'articles' => SearchHelper::searchArticles($term, $options['articles'] ?? []),
      // @phpstan-ignore-next-line
      'video' => SearchHelper::searchVideo($term, $options['video'] ?? []),
      // @phpstan-ignore-next-line
      'cheats' => SearchHelper::searchCheats($term, $options['cheats'] ?? []),
      // @phpstan-ignore-next-line
      'blogs' => SearchHelper::searchBlogs($term, $options['blogs'] ?? []),
      // @phpstan-ignore-next-line
      'faq' => SearchHelper::searchFaq($term, $options['faq'] ?? []),
      // @phpstan-ignore-next-line
      'users' => SearchHelper::searchUsers($term, $options['users'] ?? []),
      // @phpstan-ignore-next-line
      'comments' => SearchHelper::searchComments($term, $options['comments'] ?? []),
      // @phpstan-ignore-next-line
      'compilations' => SearchHelper::searchCompilations($term, $options['compilations'] ?? [])
    ];
  }

  /**
   * @return array{
   *     games: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     news: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     articles: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     video: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     cheats: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     blogs: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     faq: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     users: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     },
   *     comments: array{
   *         total_found: int,
   *         total?: int,
   *         matches: mixed[]
   *     }
   * }
   * @throws Exception
   */
  protected function queryAllSimple(string $term): array
  {
    return [
      'games' => SearchHelper::searchGames($term),
      'news' => $this->queryNewsSimple($term),
      'articles' => $this->queryArticlesSimple($term),
      'video' => $this->queryVideoSimple($term),
      'cheats' => $this->queryCheatsSimple($term),
      'blogs' => $this->queryBlogsSimple($term),
      'faq' => $this->queryFaqSimple($term),
      'users' => SearchHelper::searchUsers($term),
      'comments' => SearchHelper::searchComments($term),
    ];
  }

  protected function getTerm(): string
  {
    $term = $this->request->get('term', '');
    $term = \urldecode($term);
    try {
      $term = SgTypeHelper::ensureString(mb_convert_encoding($term, 'UTF-8', 'auto'));
    } catch (Throwable $e) {
      captureException($e);
      return '';
    }
    $term = \mb_strtolower($term);
    $term = \trim($term);
    $term = \strip_tags($term);
    /** @noinspection UnnecessaryCastingInspection */
    $term = (string)\str_replace(['`', '’'], "'", $term);
    return \str_replace(['!', '(', ')'], '', $term);
  }
}
