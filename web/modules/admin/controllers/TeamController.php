<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use Exception;
use yii\web\Request;
use common\models\User;
use yii\web\UploadedFile;
use frontend\traits\Access;
use common\helpers\SgHelper;
use yii\base\ErrorException;
use common\models\UserTeamInfo;
use common\helpers\ImageHelper;
use yii\db\StaleObjectException;
use yii\web\NotFoundHttpException;
use yii\base\InvalidConfigException;
use frontend\modules\admin\AdminController;
use common\exceptions\ControlFlowException;

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class TeamController extends AdminController
{
  /**
   * @throws Throwable
   * @throws InvalidConfigException
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionIndex(?string $subcategory = 'active'): string
  {
    $query = UserTeamInfo::find()
      ->orderBy(['order' => \SORT_ASC]);

    /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
    switch ($subcategory) {
      case 'active':
        $query->where([
          'hide_from_team' => 0,
          'ex' => 0,
        ]);
        break;
      case 'ex':
        $query->where([
          'hide_from_team' => 0,
          'ex' => 1,
        ]);
        break;
      case 'hidden':
        $query->where([
          'hide_from_team' => 1,
        ]);
        break;
    }

    $users = $query->all();

    return $this->render(($subcategory === 'active') ? 'team' : 'team-ex', \compact('users', 'subcategory'));
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionMoveDown(int $id): void
  {
    /** @var ?UserTeamInfo $userInfo */
    $userInfo = UserTeamInfo::findOne($id);

    if (!$userInfo) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    /** @var ?UserTeamInfo $nextUser */
    $nextUser = UserTeamInfo::find()
      ->where(['>', 'order', $userInfo->order])
      ->andWhere([
        'hide_from_team' => $userInfo->hide_from_team,
        'ex' => $userInfo->ex,
        'small_card' => $userInfo->small_card,
      ])
      ->orderBy(['order' => \SORT_ASC])
      ->one();

    if ($nextUser) {
      $nextOrder = $nextUser->order;
      $nextUser->order = $userInfo->order;
      $userInfo->order = $nextOrder;
      $nextUser->save();
      $userInfo->save();
    }

    $redirect = $this->request->referrer ?: '/admin/team';
    $this->redirect($redirect . '#user_' . $id);
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionMoveUp(int $id): void
  {
    /** @var ?UserTeamInfo $userInfo */
    $userInfo = UserTeamInfo::findOne($id);

    if (!$userInfo) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    /** @var ?UserTeamInfo $nextUser */
    $nextUser = UserTeamInfo::find()
      ->where(['<', 'order', $userInfo->order])
      ->andWhere([
        'hide_from_team' => $userInfo->hide_from_team,
        'ex' => $userInfo->ex,
        'small_card' => $userInfo->small_card,
      ])
      ->orderBy(['order' => \SORT_DESC])
      ->one();

    if ($nextUser) {
      $nextOrder = $nextUser->order;
      $nextUser->order = $userInfo->order;
      $userInfo->order = $nextOrder;
      $nextUser->save();
      $userInfo->save();
    }

    $redirect = $this->request->referrer ?: '/admin/team';
    $this->redirect($redirect . '#user_' . $id);
  }

  /**
   * @throws Throwable
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   */
  #[Access(permissions: ['team_manage'], methods: ['GET', 'POST'])]
  public function actionEdit(int $id): string
  {
    $user = UserTeamInfo::findOne($id);

    if (!$user) {
      if ($id !== 0) {
        throw new NotFoundHttpException('Пользователь не найден');
      }
      $user = new UserTeamInfo();
    }

    if ($this->request->isPost) {
      $post = $this->request->post();
      if (!isset($post['UserTeamInfo']['ex'])) {
        $post['UserTeamInfo']['ex'] = 0;
      }
      $user->load($post);

      /** @var string|int $userId */
      $userId = $user->user_id;
      if (\is_string($userId) && isset($post[$user->formName()]['user_id'])) {
        /** @var ?User $realUser */
        $realUser = User::find()->where(['user_name' => $user->user_id])->one();
        if ($realUser) {
          $user->user_id = $realUser->id;
        }
      }

      try {
        $user->photo = $this->uploadImage($user, 'photo', 500, 500);
      } catch (ControlFlowException) {
        // Ничего не делаем, скипаем дальше
      } catch (Throwable $e) {
        $user->addError('photo', $e->getMessage());
      }

      if ($user->isNewRecord) {
        $user->order = (int)UserTeamInfo::find()->select('MAX(`order`)')->scalar() + 1;
      }

      if ($user->save()) {
        $redirect = $this->request->referrer ?: '/admin/team';
        if (str_contains($redirect, '/admin/team/edit')) {
          $redirect = '/admin/team';
        }
        $this->redirect($redirect . '#user_' . $user->user_id);
      }
    }

    return $this->render('edit', \compact('user'));
  }

  /**
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionHide(int $id): void
  {
    /** @var ?UserTeamInfo $userInfo */
    $userInfo = UserTeamInfo::findOne($id);

    if ($userInfo) {
      $userInfo->hide_from_team = 1;
      $userInfo->save();
    }

    $redirect = $this->request->referrer ?: '/admin/team';
    $this->redirect($redirect . '#user_' . $id);
  }

  /**
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionShow(int $id): void
  {
    /** @var ?UserTeamInfo $userInfo */
    $userInfo = UserTeamInfo::findOne($id);

    if ($userInfo) {
      $userInfo->hide_from_team = 0;
      $userInfo->save();
    }

    $redirect = $this->request->referrer ?: '/admin/team';
    $this->redirect($redirect . '#user_' . $id);
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionRemove(int $id): void
  {
    /** @var ?UserTeamInfo $userInfo */
    $userInfo = UserTeamInfo::findOne($id);

    $userInfo?->delete();

    $redirect = $this->request->referrer ?: '/admin/team';
    $this->redirect($redirect . '#user_' . $id);
  }

  /**
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionContract(int $id): void
  {
    /** @var ?UserTeamInfo $userInfo */
    $userInfo = UserTeamInfo::findOne($id);

    if ($userInfo) {
      $userInfo->small_card = 1;
      $userInfo->save();
    }

    $redirect = $this->request->referrer ?: '/admin/team';
    $this->redirect($redirect . '#user_' . $id);
  }

  /**
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['team_manage'], methods: ['GET'])]
  public function actionExpand(int $id): void
  {
    /** @var ?UserTeamInfo $userInfo */
    $userInfo = UserTeamInfo::findOne($id);

    if ($userInfo) {
      $userInfo->small_card = 0;
      $userInfo->save();
    }

    $redirect = $this->request->referrer ?: '/admin/team';
    $this->redirect($redirect . '#user_' . $id);
  }

  /**
   * @throws ControlFlowException
   * @throws ErrorException
   * @throws Exception
   */
  protected function uploadImage(UserTeamInfo $user, string $attr, int $width, int $height): string
  {
    $file = UploadedFile::getInstance($user, $attr);

    if (!$file) {
      // Ничего не делаем, скипаем дальше
      throw new ControlFlowException('Изображение не загружено');
    }

    if (!\in_array($file->extension, ['jpg', 'jpeg', 'png', 'gif'])) {
      throw new ErrorException('Неверный формат изображения. Поддерживаемые форматы: JPG, PNG, GIF');
    }

    if ($file->size > 1048576) { // 1000kb
      throw new ErrorException('Изображение слишком большое. Максимальный размер — 1000kb');
    }

    ImageHelper::open($file->tempName)
      ->resize($width, $height)
      ->save($file->tempName);

    $paths = SgHelper::getUploadPaths('avatars');
    $file->saveAs($paths['path']);
    return $paths['url'];
  }
}
