<?php

namespace frontend\modules\ajax\controllers;

use yii\web\Response;
use yii\helpers\Json;
use GuzzleHttp\Client;
use yii\web\HttpException;
use frontend\traits\Access;
use yii\web\BadRequestHttpException;
use frontend\modules\ajax\ApiController;
use GuzzleHttp\Exception\GuzzleException;

/** @noinspection PhpUnused */

class SteamGridController extends ApiController
{
  protected string $baseUrl = 'https://www.steamgriddb.com/api/v2';
  protected string $apiKey = '2f58444da882faeeeeecccfc059cd9f9';

  /**
   * @throws HttpException
   * @throws BadRequestHttpException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionItems(): mixed
  {
    $id = $this->request->get('id');
    $type = $this->request->get('type');

    if (empty($id) || empty($type)) {
      throw new BadRequestHttpException('Missing required parameters');
    }

    try {
      $gameId = $this->getGameId($id);
    } catch (BadRequestHttpException $e) {
      return ['error' => $e->getMessage()];
    }

    if (\is_array($gameId)) {
      return [
        'games' => $gameId,
      ];
    }

    return $this->actionItemsById($gameId);
  }

  /**
   * @throws HttpException
   * @throws BadRequestHttpException
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionItemsById(?int $gameId = null): mixed
  {
    if (!$gameId) {
      $gameId = $this->request->get('id');
    }
    $type = $this->request->get('type');

    if (empty($gameId) || empty($type)) {
      throw new BadRequestHttpException('Missing required parameters');
    }

    $url = match ($type) {
      'grid' => '/grids/game/' . $gameId . '?' . \http_build_query([
          'dimensions' => '1024x1024,512x512',
        ]),
      'grid-vertical' => '/grids/game/' . $gameId . '?' . \http_build_query([
          'dimensions' => '600x900',
        ]),
      'hero' => '/heroes/game/' . $gameId . '?' . \http_build_query([
          'dimensions' => '1920x620,3840x1240',
        ]),
      default => throw new BadRequestHttpException('Неправильное значение параметра "type": ' . $type),
    };

    $result = $this->fetch($url);
    if (!$result['success']) {
      throw new BadRequestHttpException('Failed to fetch data');
    }
    return $result['data'];
  }

  /**
   * @throws HttpException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionStealImage(): string
  {
    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $url = $this->request->get('url');

      if (empty($url)) {
        throw new BadRequestHttpException('Missing required parameters');
      }

      $client = new Client();
      $response = $client->request('GET', $url);
      $this->format = Response::FORMAT_RAW;
      /** @var array<string, string[]> $headers */
      $headers = $response->getHeaders();
      foreach ($headers as $name => $valueArray) {
        foreach ($valueArray as $value) {
          $this->response->headers->add($name, $value);
        }
      }
      return $response->getBody()->getContents();
    } catch (GuzzleException $e) {
      throw new HttpException($e->getCode(), $e->getMessage());
    }
  }

  /**
   * @throws BadRequestHttpException
   * @throws HttpException
   */
  protected function getGameId(string|int $id): mixed
  {
    if (\is_numeric($id)) {
      return $this->getGameIdBySteamId($id);
    }
    return $this->getGameIdByName($id);
  }

  /**
   * @throws BadRequestHttpException
   */
  protected function getGameIdBySteamId(string|int $id): mixed
  {
    try {
      $result = $this->fetch('/games/steam/' . $id);
    } catch (HttpException) {
      throw new BadRequestHttpException('Игра не найдена в базе SteamGridDB');
    }
    if (!$result['success']) {
      throw new BadRequestHttpException('Игра не найдена в базе SteamGridDB');
    }
    return $result['data']['id'];
  }

  /**
   * @throws BadRequestHttpException
   * @throws HttpException
   */
  protected function getGameIdByName(string|int $name): mixed
  {
    $result = $this->fetch('/search/autocomplete/' . $name);
    if (!$result['success'] || (\count($result['data']) === 0)) {
      throw new BadRequestHttpException('Game not found');
    }
    if (\count($result['data']) > 1) {
      return $result['data'];
    }
    return $result['data'][0]['id'];
  }

  /**
   * @return array<mixed>
   * @throws HttpException
   */
  protected function fetch(string $url): array
  {
    try {
      $client = new Client();
      $response = $client->request('GET', $this->baseUrl . $url, [
        'headers' => [
          'Authorization' => 'Bearer ' . $this->apiKey,
        ],
      ]);
      return Json::decode($response->getBody()->getContents());
    } catch (GuzzleException $e) {
      throw new HttpException($e->getCode(), $e->getMessage());
    }
  }
}
