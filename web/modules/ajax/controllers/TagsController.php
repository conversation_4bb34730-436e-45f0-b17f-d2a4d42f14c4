<?php

namespace frontend\modules\ajax\controllers;

use frontend\traits\Access;
use yii\base\InvalidConfigException;
use common\components\db\ActiveRecord;
use frontend\modules\ajax\ApiController;
use common\models\{TopicTag, DataTags, BlogsTag};

/**
 * @noinspection PhpUnused
 */

class TagsController extends ApiController
{
  /**
   * @return string[]
   * @throws InvalidConfigException
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionIndex(): array
  {
    $term = $this->request->getQueryParam('term');
    $from = $this->request->getQueryParam('from', '');

    switch ($from) {
      case 'users_video':
      case 'faq':
        $class = DataTags::class;
        $field = 'tags_text';
        break;
      case 'blogs':
        $class = BlogsTag::class;
        $field = 'blog_tag_text';
        break;
      default:
        $class = TopicTag::class;
        $field = 'topic_tag_text';
        break;
    }

    /** @var ActiveRecord[] $tags */
    $tags = $class::find()
      ->where(['like', $field, (string)$term])
      ->limit(20)
      ->distinct()
      ->all();

    if (!$tags) {
      return [];
    }

    $output = [];
    foreach ($tags as $tag) {
      $output[] = \trim(\strtolower($tag[$field]), " \t\n\r\0\x0B\.");
    }

    return \array_unique($output);
  }
}
