<?php

namespace common\models;

use yii\db\Expression;
use yii\web\IdentityInterface;
use yii\db\ActiveQueryInterface;
use common\behaviors\MeiliBehavior;
use common\components\db\ActiveRecord;
use promocat\twofa\behaviors\TwoFaBehavior;

/**
 * @property int $id
 * @property string $user_name
 * @property ?string $show_name
 * @property string $user_mail
 * @property ?float $user_rating
 * @property ?string $user_register
 * @property ?string $user_last_visit
 * @property int $user_comments
 * @property string $user_pass
 * @property ?string $mfa_secret
 * @property string $user_avatar
 * @property int $user_count_vote
 * @property int $user_friends
 * @property string $reg_ip
 * @property string $reg_user_agent
 * @property ?int $time_offset
 * @property ?string $user_dob
 * @property string $user_gender
 * @property int $country_id
 * @property int $region_id
 * @property int $city_id
 * @property string $user_computer
 * @property string $user_signature
 * @property ?string $user_poster
 * @property int $user_warn
 * @property int<0,1> $user_banned
 * @property string $user_banned_final
 * @property string $user_about
 * @property ?string $mass_pm_date
 * @property ?string $feed_date_visit
 * @property string $user_social_id
 * @property ?string $answers_last_visit
 * @property ?string $pay_last_date
 * @property ?string $user_pass_forgot_code
 * @property ?int<0,1> $changed_show_name
 * @property int $credibility
 *
 * @property-read string $userName Смотри {@link self::getUserName}
 * @property-read string $authKey Смотри {@link self::getAuthKey}
 * @property-read ?UserTeamInfo $teamInfo См. {@link self::getTeamInfo}
 */
class BaseUser extends ActiveRecord implements IdentityInterface
{
  public bool $isBanned {
    get => ($this->user_banned === 1)
      && (
        ($this->user_banned_final > \time())
        || ($this->user_banned_final === '0000000000')
      );
  }

  public bool $inTeam {
    get => ($this->teamInfo !== null) && !$this->teamInfo->ex;
  }

  /**
   * {@inheritdoc}
   */
  #[\Override]
  public static function tableName(): string
  {
    return 'itaf_user';
  }

  public function getId(): int
  {
    return $this->id;
  }

  public function getAuthKey(): string
  {
    return \md5($this->user_pass);
  }

  /**
   * @param int|string $id
   */
  public static function findIdentity($id): ?self
  {
    return self::findOne($id);
  }

  /**
   * @param string $token
   * @noinspection PhpMultipleClassDeclarationsInspection
   */
  public static function findIdentityByAccessToken(#[\SensitiveParameter] $token, $type = null): ?self
  {
    $token = \base64_decode($token);
    $tokenParts = \explode(':', $token);
    $userId = \array_shift($tokenParts);
    $token = \implode(':', $tokenParts);
    return self::findOne(['id' => $userId, 'user_pass' => $token]);
  }

  /**
   * @noinspection PhpMultipleClassDeclarationsInspection
   */
  public function validateAuthKey(#[\SensitiveParameter] $authKey): bool
  {
    return $this->getAuthKey() === $authKey;
  }

  public function getUserName(bool $ban_info = false): string
  {
    if (!empty($this->show_name)) {
      $this->show_name = \trim($this->show_name);
      $return = $this->show_name;
    } else {
      $return = $this->user_name;
    }

    if ($this->isBanned && $ban_info) {
      $return = "<s>$return</s>";
    }

    return $return;
  }

  public function getTeamInfo(): ActiveQueryInterface
  {
    return $this->hasOne(UserTeamInfo::class, ['user_id' => 'id']);
  }

  /**
   * {@inheritdoc}
   * @return array<mixed[]>
   */
  #[\Override]
  public function rules(): array
  {
    return [
      [
        [
          'user_rating',
          'user_comments',
          'user_count_vote',
          'user_friends',
          'country_id',
          'region_id',
          'city_id',
        ],
        'default',
        'value' => 0,
      ],
      [
        ['user_computer', 'user_about', 'user_social_id', 'user_signature', 'user_avatar', 'user_gender'],
        'default',
        'value' => '',
      ],
      [['credibility'], 'default', 'value' => 100],
      [['show_name'], 'default', 'value' => null],
      [
        [
          'user_mail',
        ],
        'required',
      ],
      [['user_rating'], 'number'],
      [
        [
          'user_register',
          'user_last_visit',
          'user_dob',
          'mass_pm_date',
          'feed_date_visit',
          'answers_last_visit',
          'pay_last_date',
        ],
        'safe',
      ],
      [
        [
          'user_comments',
          'user_count_vote',
          'user_friends',
          'time_offset',
          'country_id',
          'region_id',
          'city_id',
          'user_warn',
          'user_banned',
          'credibility',
        ],
        'integer',
      ],
      [['user_about'], 'string'],
      [['user_name', 'user_pass'], 'string', 'max' => 32],
      [
        ['show_name', 'reg_ip', 'user_computer', 'user_signature'],
        'string',
        'max' => 255,
      ],
      [['user_mail'], 'string', 'max' => 40],
      [['user_avatar'], 'string', 'max' => 128],
      [['reg_user_agent'], 'string', 'max' => 200],
      [['user_gender'], 'string', 'max' => 3],
      [['user_poster', 'user_social_id'], 'string', 'max' => 100],
      [['user_banned_final', 'user_pass_forgot_code'], 'string', 'max' => 10],
      [['user_name'], 'unique'],
      [
        [
          'user_name',
          'show_name',
          'user_mail',
          'user_avatar',
          'user_dob',
          'user_gender',
          'user_computer',
          'user_signature',
          'user_poster',
          'user_about',
          'user_social_id',
        ],
        'filter',
        'filter' => static function ($value) {
          if (\is_string($value)) {
            return \strip_tags(\trim($value));
          }
          return $value;
        },
      ],
    ];
  }

  /**
   * @return array<class-string|array<string, mixed>>
   */
  #[\Override]
  public function behaviors(): array
  {
    return [
      'meili' => [
        'class' => MeiliBehavior::class,
        'index' => 'users',
        'attributes' => [
          'id',
          'user_name',
          'show_name',
          'user_register',
          'user_rating',
          'city_id',
          'region_id',
          'country_id',
        ],
        'fields' => [
          'id',
          'user_name' => new Expression('LOWER(user_name)'),
          'show_name' => new Expression('LOWER(show_name)'),
          'date' => new Expression('UNIX_TIMESTAMP(user_register)'),
          'user_rating',
          'resulting_name' => new Expression('IFNULL(show_name, user_name)'),
          'city_id',
          'region_id',
          'country_id',
        ],
      ],
      'two_fa' => [
        'class' => TwoFaBehavior::class,
        'secretAttribute' => 'mfa_secret',
      ],
    ];
  }
}
