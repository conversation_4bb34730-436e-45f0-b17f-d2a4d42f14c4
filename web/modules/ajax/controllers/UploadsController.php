<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use yii\web\UploadedFile;
use frontend\traits\Access;
use common\helpers\SgImageHelper;
use frontend\modules\ajax\ApiController;

/** @noinspection PhpUnused */

class UploadsController extends ApiController
{
  /**
   * @return array{
   *     status: int<0,2>,
   *     message?: string,
   *     image?: string,
   *     preview?: string
   * }
   * @throws \Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionImageUpload(): array
  {
    $user = Yii::$app->user;
    $params = Yii::$app->params;
    $date = \date('Y/m/d');
    $path = "uploads/images/$user->id/form/$date/";
    $uploadDir = Yii::getAlias('@root') . "/images/$path";
    $uploadUrl = "{$params['domain.images']}/$path";

    if (!\is_dir($uploadDir) && !\mkdir($uploadDir, 0o755, true) && !\is_dir($uploadDir)) {
      throw new \RuntimeException(\sprintf('Directory "%s" was not created', $uploadDir));
    }

    $file = UploadedFile::getInstanceByName('img_file');
    $url = $this->request->post('img_url');

    if ($file) {
      $fileType = 'local';
      $extension = \pathinfo($file->name, \PATHINFO_EXTENSION);
    } elseif ($url && (\strlen($url) > 10)) {
      $fileType = 'remote';
      $extension = \pathinfo($url, \PATHINFO_EXTENSION);
    } else {
      return ['status' => 2, 'message' => 'Размер файла не должен превышать 8МБ'];
    }

    if (!\in_array($extension, $params['images.allowed_extensions'])) {
      return [
        'status' => 1,
        'message' => 'Недопустимый формат изображения.\nРазрешенные форматы: ' . \implode(
            ', ',
            $params['images.allowed_extensions'],
          ),
      ];
    }

    /** @noinspection ObsoleteFunctionInspection */
    $fileName = \md5(\uniqid((string)\mt_rand(), true));

    $uploadFile = $uploadDir . $fileName . '.' . $extension;

    if ($fileType === 'local') {
      $uploadResult = $file->saveAs($uploadFile);
    } else {
      $uploadResult = \copy(\trim($url), $uploadFile);
    }

    if (!$uploadResult) {
      return ['status' => 2, 'message' => 'При загрузке изображения произошла непредвиденная ошибка'];
    }

    $imgUrl = $uploadUrl . $fileName . '.' . $extension;

    return ['status' => 0, 'image' => $imgUrl, 'preview' => SgImageHelper::thumb($imgUrl, 364, 205, 'r')];
  }
}
