<?php

namespace frontend\controllers;

use Yii;
use yii\web\Request;
use yii\web\Response;
use yii\helpers\Json;
use yii\db\Exception;
use RuntimeException;
use yii\web\Controller;
use common\models\Claim;
use yii\web\HttpException;
use frontend\traits\Access;
use common\models\ActionLog;
use common\helpers\SgHelper;
use common\models\UserReview;
use yii\web\NotFoundHttpException;
use yii\web\BadRequestHttpException;

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class UserReviewController extends Controller
{
  /**
   * @return array<string, mixed>|Response
   * @throws HttpException
   * @throws NotFoundHttpException|Exception
   * @throws \Exception
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  #[Access(permissions: ['admin_access'], methods: ['POST'])]
  public function actionDelete(string $id): array|Response
  {
    $isAjax = $this->request->acceptableContentTypes
      && \array_key_exists('application/json', $this->request->acceptableContentTypes);
    if ($isAjax) {
      Yii::$app->response->format = Response::FORMAT_JSON;
    }

    $review = UserReview::findOne($id);
    if ($review === null) {
      throw new NotFoundHttpException('Отзыв не найден');
    }

    if (($review->user_id !== Yii::$app->user->id) && !Yii::$app->user->can('admin_access')) {
      throw new HttpException(403, 'Недостаточно прав');
    }

    $review->deleted = 1;
    $review->delete_user_id = Yii::$app->user->id;

    if ($review->user_id !== Yii::$app->user->id) {
      $post = $this->request->post();
      if (!$this->request->isPost || empty($post['reason'])) {
        throw new BadRequestHttpException('Необходимо указать причину удаления');
      }

      $review->delete_reason = $post['reason'];
      if ($review->had_offence || !empty($post['delete_final'])) {
        $review->deleted_final = 1;
      }
      $review->had_offence = 1;
    } else {
      $review->deleted_final = 1;
    }

    if (!$review->save()) {
      throw new RuntimeException('Не удалось удалить отзыв');
    }
    $review->unnotify();

    if ($review->user_id !== Yii::$app->user->id) {
      $post = $this->request->post();
      $log = new ActionLog([
        'user_id' => Yii::$app->user->id,
        'action_type' => ActionLog::REVIEW_DELETE,
        'payload' => Json::encode([
          'review_id' => $review->id,
        ]),
      ]);
      $log->save();

      $gameTitle = $review->game->mainTitle->title ?? '[удалённая игра]';
      $reviewTitle = $review->title;
      if (!empty($reviewTitle) && (\mb_strlen($reviewTitle) > 150)) {
        $reviewTitle = \substr($reviewTitle, 0, 150) . '…';
      }
      $title = !empty($reviewTitle) ? "«{$reviewTitle}»" : "к игре «{$gameTitle}»";
      $message = "Отзыв <a href='$review->fullUrlWithDomain' target='_blank'>$title</a> удалён модератором по причине:";
      $message .= "<blockquote>{$post['reason']}</blockquote>";

      if (!$review->deleted_final) {
        $message .= '<br/>Ты можешь исправить нарушение и восстановить его. При повторном нарушении отзыв будет удалён окончательно.';
      } else {
        $message .= '<br/>Это было повторное нарушение правил, а потому отзыв был удалён без возможности восстановления.';
      }

      SgHelper::newPm(
        toUserId: $review->user_id,
        subject: "Отзыв $title удалён модератором",
        message: $message,
        fromUserId: Yii::$app->params['robot_user_id']
      );
    }

    Claim::updateAll([
      'claim_status' => 1,
    ], [
      'target_type' => $review->targetType,
      'target_id' => $review->targetId,
    ]);

    if ($isAjax) {
      return [
        'success' => true,
      ];
    }

    if ($this->request->referrer && ($review->fullUrlWithDomain !== $this->request->referrer)) {
      return $this->redirect($this->request->referrer);
    }

    $redirect = (string)$review->sectionUrl;
    $gameReviewsCount = (int)$review->game?->getUserReviews()->andWhere(['deleted' => 0])->count();
    if (($gameReviewsCount === 0) && !empty($review->game->mainTitle)) {
      $redirect = $review->game->mainTitle->fullUrl;
    }
    return $this->redirect($redirect);
  }
}
