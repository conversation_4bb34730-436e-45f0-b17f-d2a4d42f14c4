<?php

namespace frontend\controllers;

use Yii;
use yii\db\Exception;
use frontend\traits\Access;
use yii\helpers\ArrayHelper;
use frontend\components\View;
use yii\base\{InvalidConfigException, Action};
use common\helpers\{SgTextHelper, SgStringHelper};
use common\models\{Blog, User, Topic, UserContact};
use yii\web\{Request, Response, Controller, NotFoundHttpException, ForbiddenHttpException, BadRequestHttpException};

/**
 * @property-read Request $request
 * @property-read View $view
 * @noinspection PhpUnused
 */
class UserEditController extends Controller
{
  public ?User $user = null;
  public $layout = '@frontend/views/v9/user-edit/layout.php';

  /**
   * @param Action<UserEditController> $action
   * @param array<string, mixed> $params
   * @return array<string, mixed>
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException
   * @throws BadRequestHttpException
   */
  #[\Override]
  public function bindActionParams($action, $params): array
  {
    if ($params['userName']) {
      $this->user = User::findOne(['user_name' => $params['userName']]);
    }
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не существует');
    }
    if (($this->user->id !== Yii::$app->user->id)
      && !(
        Yii::$app->user->can('admin_access_rbac')
        || Yii::$app->user->can('moderate_users')
      )
    ) {
      throw new ForbiddenHttpException('Нельзя редактировать чужой профиль');
    }
    return parent::bindActionParams($action, $params);
  }

  /**
   * @param array<string, mixed> $params
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   */
  #[\Override]
  public function render($view, $params = []): string
  {
    $user = $this->user;
    if (!$user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $userName = $user->userName;

    $this->view->title = "$userName — Настройки // Пользователь StopGame";
    $this->view->registerMetaTag(['name' => 'keywords', 'content' => "$userName, пользователь StopGame"]);
    $this->view->registerMetaTag(['name' => 'description', 'content' => "$userName - Все данные пользователя StopGame"]
    );

    $totalSubscribers = 0;
    $subscribed = false;
    /** @var ?Blog $personalBlog */
    $personalBlog = $user->getOwnedBlogs()
      ->where(['blog_type' => Blog::TYPE_PERSONAL])
      ->one();
    if ($personalBlog) {
      $totalSubscribers = $personalBlog->getSubscribedUsers()->count();
      $subscribed = $personalBlog->isSubscribed;
    }
    $blogWins = Topic::find()
      ->where([
        'user_id' => $user->id,
        'topic_on_main' => 1,
        'topic_publish' => 1,
        'topic_deleted' => 0,
      ])
      ->count();

    $this->view->params = ArrayHelper::merge(
      $this->view->params,
      \compact('user', 'blogWins', 'totalSubscribers', 'subscribed'),
    );

    return parent::render($view, $params);
  }

  /**
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionReg(): string
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $contacts = [];
    if ($this->request->isPost) {
      if (Yii::$app->user->identity->isBanned) {
        Yii::$app->session->addFlash('error', 'Ты в бане и не можешь менять профиль');
      } else {
        $post = self::filterPost($this->request->post());
        $this->user->load($post);

        $jevix = ['show_name', 'user_computer', 'user_about'];
        $htmlencode = ['user_dob', 'user_gender'];

        foreach ($this->user->dirtyAttributes as $attr => $value) {
          if (\in_array($attr, $jevix)) {
            $this->user->$attr = \strip_tags(SgTextHelper::jevix($value));
          }
          if (\in_array($attr, $htmlencode)) {
            $this->user->$attr = \strip_tags(\htmlspecialchars($value));
          }
        }

        if ($this->user->getDirtyAttributes(['show_name'])) {
          $this->user->show_name = \trim($this->user->show_name ?? '');

          if ($this->user->oldAttributes['show_name'] !== $this->user->show_name) {
            if (empty($this->user->show_name)) {
              $this->user->show_name = null;
            } elseif (SgStringHelper::strlenu($this->user->show_name) <= 4) {
              $this->user->addError('show_name', 'Отображаемое имя должно быть длиннее 4 символов');
            } elseif ($this->user->changed_show_name && !Yii::$app->user->can('moderate_users')) {
              $this->user->addError('show_name', 'Менять имя можно только один раз');
            } elseif (\preg_match('/[А-я]/iu', $this->user->show_name) && \preg_match(
                '/[a-zA-Z]/i',
                $this->user->show_name,
              )) {
              $this->user->addError('show_name', 'Нельзя сочетать в имени русский и латинский алфавиты');
            } elseif (User::find()->where(
              ['OR', ['user_name' => $this->user->show_name, 'show_name' => $this->user->show_name]],
            )->exists()) {
              $this->user->addError('show_name', 'Выбранное имя уже занято');
            } else {
              $this->user->changed_show_name = 1;
            }
          }
        }

        if ($this->user->getDirtyAttributes(['city_id'])) {
          if (empty($this->user->city_id)) {
            $this->user->city_id = 0;
          } elseif (!\is_numeric($this->user->city_id)) { // @phpstan-ignore-line
            $this->user->addError('city_id', 'Выбери город из выпадающего списка');
          } else {
            $city = $this->user->city;
            if ($city) {
              $this->user->region_id = $city->region_id;
              $this->user->country_id = $city->country_id;
            }
          }
        }

        /** @var UserContact[] $oldContacts */
        $oldContacts = $this->user->getContacts()->indexBy('id')->all();
        /**
         * @var array<int|string, array{
         *     type: string,
         *     contact: string
         * }>|mixed $contactsInfo
         */
        $contactsInfo = $this->request->post('UserContact', []);
        $hasContactsErrors = false;

        if (!empty($contactsInfo) && \is_array($contactsInfo)) {
          foreach ($contactsInfo as $contactId => $contactInfo) {
            if (\is_numeric($contactId) && isset($oldContacts[(int)$contactId])) {
              $contactId = (int)$contactId;
              $contact = $oldContacts[$contactId];
              unset($oldContacts[$contactId]);
            } else {
              if (empty($contactInfo['type'])) {
                continue;
              }
              $contact = new UserContact([
                'user_id' => $this->user->id,
                'type' => $contactInfo['type'],
              ]);
            }

            $contact->contact = $contactInfo['contact'];

            if (!$contact->save()) {
              $hasContactsErrors = true;
            }

            $contacts[] = $contact;
          }

          if (\count($oldContacts) > 0) {
            UserContact::deleteAll(['id' => \array_keys($oldContacts)]);
          }
        }

        if (!$hasContactsErrors && !$this->user->hasErrors() && $this->user->save()) {
          Yii::$app->session->setFlash('success', 'Изменения сохранены');
        }
      }
    }

    $this->view->params['show-save-buttons'] = true;

    return $this->render('@frontend/views/v9/user-edit/reg', \compact('contacts'));
  }

  /**
   * @throws NotFoundHttpException
   * @throws InvalidConfigException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionNotice(): string
  {
    return $this->render('@frontend/views/v9/user-edit/notifications');
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionInterface(): string
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $user = $this->user;
    $this->view->params['show-save-buttons'] = true;

    if ($this->request->isPost) {
      $settings = $user->settings;
      $settings->show_admin_panel = $this->request->post('show_admin_panel') ? 'true' : 'false';
      foreach (
        [
          'games_grid_type',
          'profile_grid_type',
          'compilation_grid_type',
          'company_games_grid_type',
          'games_series_grid_type',
        ] as $settingName
      ) {
        $settings->$settingName = $this->request->post($settingName);
      }
    }

    return $this->render('@frontend/views/v9/user-edit/interface', \compact('user'));
  }

  /**
   * @throws InvalidConfigException
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionImport(): string
  {
    return $this->render('@frontend/views/v9/user-edit/import');
  }

  /**
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionRemoveAvatar(): Response
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $user = $this->user;

    if (!$user->isCurrentUser && !(Yii::$app->user->can('admin_access_rbac') || Yii::$app->user->can(
          'moderate_users',
        ))) {
      throw new ForbiddenHttpException('Нельзя удалять чужие аватары!');
    }

    if (!empty($user->user_avatar)) {
      $avatarUrl = \parse_url($user->user_avatar);
      if (\is_array($avatarUrl) && !empty($avatarUrl['path'])) {
        $avatarPath = (string)Yii::getAlias('@root/images' . $avatarUrl['path']);
        if (\file_exists($avatarPath)) {
          \unlink($avatarPath);
        }
      }
    }

    $user->user_avatar = '';
    $user->save();

    if ($this->request->referrer) {
      return $this->redirect($this->request->referrer);
    }

    return $this->redirect(['user-edit/index', 'id' => $user->id]);
  }

  /**
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true)]
  public function actionRemovePoster(): Response
  {
    if (!$this->user) {
      throw new NotFoundHttpException('Пользователь не найден');
    }

    $user = $this->user;

    if (!$user->isCurrentUser) {
      throw new ForbiddenHttpException('Нельзя удалять чужие обложки!');
    }

    $user->user_poster = '';
    $user->save();

    if ($this->request->referrer) {
      return $this->redirect($this->request->referrer);
    }

    return $this->redirect(['user-edit/index', 'id' => $user->id]);
  }

  /**
   * @param array<string, mixed> $post
   * @return array<string, mixed>
   */
  public static function filterPost(array $post): array
  {
    $allowedFields = [
      'user_name',
      'show_name',
      'user_dob',
      'user_avatar',
      'time_offset',
      'user_gender',
      'country_id',
      'region_id',
      'city_id',
      'user_computer',
      'user_signature',
      'user_poster',
      'user_about',
    ];

    $newPost = $post;
    $newPost['User'] = \array_filter(
      $post['User'],
      static fn($key) => \in_array($key, $allowedFields),
      \ARRAY_FILTER_USE_KEY,
    );

    return $newPost;
  }
}
