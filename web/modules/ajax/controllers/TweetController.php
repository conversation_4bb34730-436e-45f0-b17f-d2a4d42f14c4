<?php

namespace frontend\modules\ajax\controllers;

use Exception;
use yii\httpclient\Client;
use yii\httpclient\Request;
use frontend\traits\Access;
use yii\web\BadRequestHttpException;
use frontend\modules\ajax\ApiController;

/** @noinspection PhpUnused */

class TweetController extends ApiController
{
  /**
   * @throws BadRequestHttpException
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionIndex(): mixed
  {
    $url = $this->request->post('url');

    if (empty($url)) {
      throw new BadRequestHttpException('url не указан');
    }

    $url = \preg_replace('/^https?:\/\/x\.com\//', 'https://twitter.com/', $url);
    if (\is_array($url)) {
      $url = \implode('', $url);
    }
    if (empty($url)) {
      $url = '';
    }
    $encodedUrl = \urlencode($url);

    $client = new Client();
    $request = new Request(\compact('client'));
    try {
      $response = $request->setMethod('GET')
        ->setUrl("https://publish.twitter.com/oembed?url=$encodedUrl")
        ->send();
      return $response->getData();
    } catch (Exception $e) {
      echo $e;
      return [];
    }
  }
}
