<?php

namespace common\models;

use Yii;
use DateTime;
use Exception;
use yii\helpers\Json;
use RuntimeException;
use yii\rbac\ManagerInterface;
use yii\base\InvalidConfigException;
use common\components\db\ActiveQuery;
use frontend\components\user\SettingsCollection;
use common\helpers\{SgTypeHelper, SgImageHelper};
use yii\db\{ActiveQueryInterface, Expression, Transaction};

use function Sentry\captureMessage;

/**
 * This is the model class for table "itaf_user".
 *
 * @property-read Bestgames[] $bestgames Смотри {@link self::getBestgames}
 * @property-read Blog[] $blogs Смотри {@link self::getBlogs}
 * @property-read Faq[] $faqs Смотри {@link self::getFaqs}
 * @property-read Favourite[] $favourites Смотри {@link self::getFavourites}
 * @property-read GiveawayKey[] $gameKeys Смотри {@link self::getGameKeys}
 * @property-read Gift[] $gifts Смотри {@link self::getGifts}
 * @property-read Warn[] $warns Смотри {@link self::getWarns}
 * @property-read Like[] $likes Смотри {@link self::getLikes}
 * @property-read TalkBlacklist[] $talkBlacklists Смотри {@link self::getTalkBlacklists}
 * @property-read Talk[] $talks Смотри {@link self::getTalks}
 * @property-read Topic[] $topics Смотри {@link self::getTopics}
 * @property-read Friend[] $receivedFriends Смотри {@link self::getReceivedFriends}
 * @property-read Comment[] $inboundComments Смотри {@link self::getInboundComments}
 * @property-read Blog[] $ownedBlogs Смотри {@link self::getOwnedBlogs}
 * @property-read Friend[] $sentFriends Смотри {@link self::getSentFriends}
 * @property-read Comment[] $comments Смотри {@link self::getComments}
 * @property-read BlogUser[] $blogUsers Смотри {@link self::getBlogUsers}
 * @property-read StopgameRead[] $readData Смотри {@link self::getReadData}
 * @property-read string $profileUrl Смотри {@link self::getProfileUrl}
 * @property-read string $avatar Смотри {@link self::getAvatar}
 * @property-read UserSocial[] $socials Смотри {@link self::getSocials}
 * @property-read Game[] $trackedGames Смотри {@link self::getTrackedGames}
 * @property-read Track[] $tracks Смотри {@link self::getTracks}
 * @property-read TalkUser[] $talkUsers Смотри {@link self::getTalkUsers}
 * @property-read BlogSubscription[] $blogSubscriptions Смотри {@link self::getBlogSubscriptions}
 * @property-read NewsData[] $news Смотри {@link self::getNews}
 * @property-read ?NewsUser $newsUser Смотри {@link self::getNewsUser}
 * @property-read Vote[] $votes Смотри {@link self::getVotes}
 * @property-read UserAchievement[] $achievements Смотри {@link self::getAchievements}
 * @property-read ?Country $country Смотри {@link self::getCountry}
 * @property-read ?Region $region Смотри {@link self::getRegion}
 * @property-read ?City $city Смотри {@link self::getCity}
 * @property-read string $accessToken Смотри {@link self::$accessToken}
 * @property-read UserContact[] $contacts Смотри {@link self::getContacts}
 * @property-read UserReview[] $reviews Смотри {@link self::getReviews}
 * @property-read PushToken[] $pushTokens Смотри {@link self::getPushTokens}
 * @property-read Article[] $articles Смотри {@link self::getArticles}
 * @property-read ?Blog $personalBlog Смотри {@link self::getPersonalBlog}
 * @property-read ?StalcraftUser $stalcraftUser См. {@link self::getStalcraftUser}
 * @property-read Compilation[] $compilations См. {@link self::getCompilations}
 */
class User extends BaseUser
{
  protected ?SettingsCollection $_settings = null;
  protected bool $_subscribed;
  protected ManagerInterface $accessChecker;
  public bool $isCurrentUser {
    get {
      if (!isset(Yii::$app->user)) {
        return false;
      }
      return Yii::$app->user->id === $this->id;
    }
  }
  public string $gender {
    get => Yii::$app->params['user_gender'][$this->user_gender] ?? '?';
  }
  public string $genderFull {
    get => Yii::$app->params['user_gender_full'][$this->user_gender] ?? '?';
  }
  public string $age {
    get {
      $age = '?';

      if ($this->user_dob) {
        $origin = new DateTime($this->user_dob);
        $target = new DateTime();
        $age = $origin->diff($target)->y;
      }

      return SgTypeHelper::ensureString($age);
    }
  }
  public SettingsCollection $settings {
    get {
      if ($this->_settings === null) {
        $this->_settings = SettingsCollection::instance([
          'userId' => $this->id,
        ]);
      }
      return $this->_settings;
    }
  }
  public bool $mfaEnabled {
    get => !empty($this->mfa_secret);
  }
  public ?string $mfaSecret {
    get {
      if ($this->mfa_secret) {
        return $this->mfa_secret;
      }
      $session = Yii::$app->session;
      if ($session->has('mfa_secret_temp')) {
        return $session->get('mfa_secret_temp');
      }
      $session->set('mfa_secret_temp', Yii::$app->twoFa->generateSecret());
      return $session->get('mfa_secret_temp');
    }
  }
  public bool $isBlogWinner {
    get {
      $cacheKey = 'blog_winners_users';
      if (!Yii::$app->cache?->exists($cacheKey)) {
        $winners = Topic::find()
          ->select('user_id')
          ->where(['>=', 'topic_date_rewarded', new Expression('date_sub(now(), INTERVAL 1 WEEK)')])
          ->column();
        Yii::$app->cache?->set($cacheKey, $winners, 3600);
      }
      $winners = Yii::$app->cache?->get($cacheKey);

      if (!\is_array($winners)) {
        return false;
      }

      return \in_array($this->id, $winners);
    }
  }
  public bool $isSubscribed {
    get {
      if (Yii::$app->user->isGuest) {
        return false;
      }
      if (!isset($this->_subscribed)) {
        $this->_subscribed = false;
        if ($this->personalBlog) {
          $this->_subscribed = $this->personalBlog->isSubscribed;
        }
      }
      return $this->_subscribed;
    }
  }
  public ?int $timestamp {
    get {
      $dateModify = \strtotime($this->user_register ?? '');
      if (!$dateModify) {
        $dateModify = null;
      }
      return $dateModify;
    }
  }
  /**
   * @var array<string, bool>
   */
  protected array $_access = [];

  /** @var string[] $safeForFrontend */
  protected static array $safeForFrontend = [
    'id',
    'user_name',
    'show_name',
    'user_rating',
    'user_register',
    'user_dob',
    'user_gender',
  ];

  /** @var array<string, string> $_lastReadDate */
  private array $_lastReadDate = [];

  /**
   * {@inheritdoc}
   * @return array<string, string>
   */
  #[\Override]
  public function attributeLabels(): array
  {
    return [
      'id' => 'ID',
      'user_name' => 'Логин',
      'show_name' => 'Имя на сайте',
      'user_mail' => 'Email',
      'user_rating' => 'Рейтинг',
      'user_register' => 'User Register',
      'user_last_visit' => 'User Last Visit',
      'user_comments' => 'User Comments',
      'user_pass' => 'Пароль',
      'user_avatar' => 'User Avatar',
      'user_count_vote' => 'User Count Vote',
      'user_friends' => 'User Friends',
      'reg_ip' => 'Reg Ip',
      'reg_user_agent' => 'Reg User Agent',
      'time_offset' => 'Часовой пояс',
      'user_dob' => 'Дата рождения',
      'user_gender' => 'Пол',
      'country_id' => 'Country ID',
      'region_id' => 'Region ID',
      'city_id' => 'Город',
      'user_computer' => 'Конфигурация твоего компьютера',
      'user_signature' => 'User Signature',
      'user_poster' => 'User Poster',
      'user_warn' => 'User Warn',
      'user_banned' => 'User Banned',
      'user_banned_final' => 'User Banned Final',
      'user_about' => 'Коротко о себе',
      'mass_pm_date' => 'Mass Pm Date',
      'feed_date_visit' => 'Feed Date Visit',
      'user_social_id' => 'User Social ID',
      'answers_last_visit' => 'Answers Last Visit',
      'pay_last_date' => 'Pay Last Date',
      'user_pass_forgot_code' => 'User Pass Forgot Code',
    ];
  }

  /**
   * @return ActiveQuery<Bestgames>
   */
  public function getBestgames(): ActiveQueryInterface
  {
    return $this->hasMany(Bestgames::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Blog>
   */
  public function getBlogs(): ActiveQueryInterface
  {
    return $this->hasMany(Blog::class, ['blog_id' => 'blog_id'])->via('blogUsers');
  }

  /**
   * @return ActiveQuery<Comment>
   */
  public function getInboundComments(): ActiveQueryInterface
  {
    return $this
      ->hasMany(Comment::class, ['to_user_id' => 'id'])
      ->andOnCondition(['comment_publish' => 1])
      ->andOnCondition(['NOT', ['user_id' => $this->id]])
      ->inverseOf('toUser');
  }

  /**
   * @return ActiveQuery<Comment>
   */
  public function getComments(): ActiveQueryInterface
  {
    return $this
      ->hasMany(Comment::class, ['user_id' => 'id'])
      ->leftJoin(
        ['topic' => 'ls_topic'],
        ['AND', 'topic.topic_id = comments.target_id', ['comments.target_type' => 'topic']],
      )
      ->leftJoin('faq', ['AND', 'faq.faq_id = comments.target_id', ['comments.target_type' => 'faq']])
      ->leftJoin(
        ['news' => 'news_data'],
        ['AND', 'news.news_id = comments.target_id', ['comments.target_type' => 'news']],
      )
      ->leftJoin(
        ['game' => 'stopgame_info'],
        ['AND', 'game.GameId = comments.target_id', ['comments.target_type' => 'games']],
      )
      ->leftJoin(
        ['article' => 'stopgame'],
        ['AND', 'article.base_id = comments.target_id', ['comments.target_type' => 'show']],
      )
      ->leftJoin(
        'user_reviews',
        ['AND', 'user_reviews.id = comments.target_id', ['comments.target_type' => 'user_review']],
      )
      ->leftJoin(
        'compilations',
        ['AND', 'compilations.id = comments.target_id', ['comments.target_type' => 'compilation']],
      )
      ->andOnCondition([
        'comments.comment_delete' => 0,
        'comments.comment_publish' => 1,
      ])
      ->andOnCondition([
        'OR',
        ['comments.target_type' => 'happy_new_year'],
        [
          'AND',
          ['comments.target_type' => 'topic'],
          ['NOT', ['topic.topic_id' => null]],
          [
            'topic.topic_publish' => 1,
            'topic.topic_deleted' => 0,
          ],
        ],
        [
          'AND',
          ['comments.target_type' => 'faq'],
          ['NOT', ['faq.faq_id' => null]],
          ['faq.faq_deleted' => 0],
        ],
        [
          'AND',
          ['comments.target_type' => 'news'],
          ['NOT', ['news.news_id' => null]],
          ['news.news_active' => 1],
        ],
        [
          'AND',
          ['comments.target_type' => 'games'],
          ['NOT', ['game.GameId' => null]],
        ],
        [
          'AND',
          ['comments.target_type' => 'show'],
          ['NOT', ['article.base_id' => null]],
          ['article.data_active' => 1],
        ],
        [
          'AND',
          ['comments.target_type' => 'user_review'],
          ['NOT', ['user_reviews.id' => null]],
          ['user_reviews.deleted' => 0],
        ],
        [
          'AND',
          ['comments.target_type' => 'compilation'],
          ['NOT', ['compilations.id' => null]],
          ['compilations.deleted' => 0],
        ],
      ])
      ->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Faq>
   */
  public function getFaqs(): ActiveQueryInterface
  {
    return $this->hasMany(Faq::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Favourite>
   */
  public function getFavourites(): ActiveQueryInterface
  {
    return $this->hasMany(Favourite::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<GiveawayKey>
   */
  public function getGameKeys(): ActiveQueryInterface
  {
    return $this->hasMany(GiveawayKey::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Gift>
   */
  public function getGifts(): ActiveQueryInterface
  {
    return $this->hasMany(Gift::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Warn>
   */
  public function getWarns(): ActiveQueryInterface
  {
    return $this->hasMany(Warn::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Like>
   */
  public function getLikes(): ActiveQueryInterface
  {
    return $this->hasMany(Like::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<BlogUser>
   */
  public function getBlogUsers(): ActiveQueryInterface
  {
    return $this->hasMany(BlogUser::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Blog>
   */
  public function getOwnedBlogs(): ActiveQueryInterface
  {
    return $this->hasMany(Blog::class, ['user_owner_id' => 'id'])->inverseOf('userOwner');
  }

  /**
   * @return ActiveQuery<Friend>
   */
  public function getSentFriends(): ActiveQueryInterface
  {
    return $this->hasMany(Friend::class, ['user_from' => 'id'])->inverseOf('userFrom');
  }

  /**
   * @return ActiveQuery<Friend>
   */
  public function getReceivedFriends(): ActiveQueryInterface
  {
    return $this->hasMany(Friend::class, ['user_to' => 'id'])->inverseOf('userTo');
  }

  /**
   * @return ActiveQuery<TalkBlacklist>
   */
  public function getTalkBlacklists(): ActiveQueryInterface
  {
    return $this->hasMany(TalkBlacklist::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<TalkUser>
   */
  public function getTalkUsers(): ActiveQueryInterface
  {
    return $this
      ->hasMany(TalkUser::class, ['user_id' => 'id'])
      ->andOnCondition(['talk_user_active' => 1])
      ->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Topic>
   */
  public function getTopics(): ActiveQueryInterface
  {
    return $this->hasMany(Topic::class, ['user_id' => 'id'])->inverseOf('user');
  }

  /**
   * @return ActiveQuery<Talk>
   */
  public function getTalks(): ActiveQueryInterface
  {
    return $this->hasMany(Talk::class, ['talk_id' => 'talk_id'])->via('talkUsers');
  }

  /**
   * Получаем аватар юзера в нужном нам размере
   *
   * @param int $size Размер аватара (по умолчанию 25px)
   * @throws Exception
   */
  public function getAvatar(int $size = 25): string
  {
    if (!$this->user_avatar) {
      return SgImageHelper::placeholderUrl($size, $size, 'avatar');
    }
    /** @noinspection HttpUrlsUsage */
    $avatar = \str_replace('http://', 'https://', $this->user_avatar);

    // Ужимаем до нужных размеров
    return SgImageHelper::thumb($avatar, $size, $size);
  }

  /**
   * Проверяем, является ли юзер модератором топика (или автором)
   * @throws InvalidConfigException
   */
  public function isTopicModer(int|Topic $topic): bool
  {
    if (!$this->isCurrentUser) {
      return false;
    }

    $blogModer = Yii::$app->user->can('moderate_blogs');

    if (\is_numeric($topic)) {
      /** @var ?Topic $topic */
      $topic = Topic::find()
        ->select('user_id')
        ->where(['topic_id' => $topic])
        ->one();
    }
    $isOwner = $topic && ($topic->user_id === $this->id);

    return $blogModer || $isOwner;
  }

  /**
   * Проверяем, является ли юзер модератором топика FAQ (или автором)
   */
  public function isFaqModer(int $faqId): bool
  {
    $faqModer = Yii::$app->user->can('moderate_faq');

    /** @var ?Faq $faq */
    $faq = Faq::findOne($faqId);
    $isOwner = $faq && ($faq->user_id === $this->id);

    return $faqModer || $isOwner;
  }

  public function getProfileUrl(bool $show_domain = true): string
  {
    $params = Yii::$app->params;
    $url = "/user/$this->user_name";
    if ($show_domain) {
      return "{$params['domain']}$url";
    }
    return $url;
  }

  /**
   * @return ActiveQuery<StopgameRead>
   */
  public function getReadData(): ActiveQueryInterface
  {
    return $this->hasMany(StopgameRead::class, ['user_id' => 'id']);
  }

  /**
   * @param string[] $fields
   * @param string[] $expand
   * @param bool $recursive
   * @return mixed[]
   * @throws Exception
   */
  #[\Override]
  public function toArray(array $fields = [], array $expand = [], $recursive = true, bool $forFrontend = false): array
  {
    $array = parent::toArray($fields, $expand, $recursive);

    if (!$forFrontend) {
      return $array;
    }

    // Safety first!
    foreach ($array as $key => $value) {
      if (!\in_array($key, self::$safeForFrontend)) {
        unset($array[$key]);
      }
    }

    $array['avatars'] = [
      '24' => $this->getAvatar(24),
      '25' => $this->avatar,
      '44' => $this->getAvatar(44),
      '1x-webp' => $this->getAvatar(32),
      '2x-webp' => $this->getAvatar(64),
      '1x-avif' => $this->getAvatar(32),
      '2x-avif' => $this->getAvatar(64),
    ];
    $array['userName'] = $this->getUserName();
    $array['profileUrl'] = $this->getProfileUrl(false);
    $array['isBanned'] = $this->isBanned;
    $array['gender'] = $this->gender;
    $array['age'] = $this->age;
    $array['user_comments'] = $this->user_comments;
    $array['inTeam'] = $this->inTeam;
    $array['isBlogWinner'] = $this->isBlogWinner;

    return $array;
  }

  /**
   * @throws Exception
   */
  public function getLastReadDate(string|int $targetId, string $targetType, bool $asObject = true): DateTime|string
  {
    $cacheKey = "{$targetId}_$targetType";

    if (!isset($this->_lastReadDate[$cacheKey])) {
      /** @var ?string $readData */
      $readData = $this
        ->getReadData()
        ->select('date_read')
        ->where(
          [
            'target_type' => $targetType,
            'target_id' => $targetId,
          ],
        )->scalar();

      if (!$readData) {
        $this->_lastReadDate[$cacheKey] = new DateTime()->format('Y-m-d H:i:s');
      } else {
        $this->_lastReadDate[$cacheKey] = $readData;
      }
    }

    $result = SgTypeHelper::ensureString($this->_lastReadDate[$cacheKey]);

    if ($asObject) {
      $result = new DateTime($result);
    }

    return $result;
  }

  public function setReadDate(int $targetId, string $targetType): bool
  {
    /** @var Transaction $transaction */
    $transaction = Yii::$app->db->beginTransaction();
    /** @noinspection BadExceptionsProcessingInspection */
    try {
      /** @var ?StopgameRead $readData */
      $readData = StopgameRead::find()
        ->where([
          'user_id' => $this->id,
          'target_type' => $targetType,
          'target_id' => $targetId,
        ])
        ->one();

      if (!$readData) {
        $readData = new StopgameRead([
          'user_id' => $this->id,
          'target_type' => $targetType,
          'target_id' => $targetId,
        ]);
      }

      $readData->date_read = new Expression('NOW()');
      $readData->comment_count_last = Comment::getCommentTarget($targetType, $targetId)->commentCount ?? 0;
      $readData->comment_new_notice = 0;
      $result = $readData->save(false);

      if (!$result) {
        captureMessage(Json::encode($readData->errors));
      }
      $transaction->commit();
    } catch (Exception $e) {
      $transaction->rollBack();
      captureMessage($e->getMessage());
      $result = false;
    }
    return $result;
  }

  /**
   * @return ActiveQuery<UserSocial>
   */
  public function getSocials(): ActiveQueryInterface
  {
    return $this->hasMany(UserSocial::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<Track>
   */
  public function getTracks(): ActiveQueryInterface
  {
    return $this->hasMany(Track::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<Game>
   */
  public function getTrackedGames(): ActiveQueryInterface
  {
    return $this
      ->hasMany(Game::class, ['GameId' => 'GameId'])
      ->via('tracks');
  }

  /**
   * @return ActiveQuery<NewsData>
   */
  public function getNews(): ActiveQueryInterface
  {
    return $this->hasMany(NewsData::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<Article>
   */
  public function getArticles(): ActiveQueryInterface
  {
    return $this->hasMany(Article::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<BlogSubscription>
   */
  public function getBlogSubscriptions(): ActiveQueryInterface
  {
    return $this->hasMany(BlogSubscription::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<NewsUser>
   */
  public function getNewsUser(): ActiveQueryInterface
  {
    return $this->hasOne(NewsUser::class, ['user_id' => 'id']);
  }

  public function verb(string $male, string $female): string
  {
    if (\in_array($this->gender, ['f', 'w'])) {
      return $female;
    }

    return $male;
  }

  /**
   * @return ActiveQuery<UserAchievement>
   */
  public function getAchievements(): ActiveQueryInterface
  {
    return $this->hasMany(UserAchievement::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<Vote>
   */
  public function getVotes(): ActiveQueryInterface
  {
    return $this->hasMany(Vote::class, ['user_voter_id' => 'id']);
  }

  /**
   * @return ActiveQuery<Country>
   */
  public function getCountry(): ActiveQueryInterface
  {
    return $this->hasOne(Country::class, ['country_id' => 'country_id']);
  }

  /**
   * @return ActiveQuery<Region>
   */
  public function getRegion(): ActiveQueryInterface
  {
    return $this->hasOne(Region::class, ['region_id' => 'region_id']);
  }

  /**
   * @return ActiveQuery<City>
   */
  public function getCity(): ActiveQueryInterface
  {
    return $this->hasOne(City::class, ['city_id' => 'city_id']);
  }

  public function getAccessToken(): string
  {
    return \base64_encode("$this->id:$this->user_pass");
  }

  /**
   * @return ActiveQuery<UserContact>
   */
  public function getContacts(): ActiveQueryInterface
  {
    return $this->hasMany(UserContact::class, ['user_id' => 'id']);
  }

  public function totalPubs(): int
  {
    /** @var int $articles */
    $articles = $this
      ->getArticles()
      ->where(['data_active' => 1])
      ->andWhere([
        'NOT',
        ['data_type' => ['infact', 'podcast', 'live', 'cheats', 'trainers', 'trailers']],
      ])
      ->count();

    /** @var int $news */
    $news = $this
      ->getNews()
      ->where(['news_active' => 1])
      ->count();

    return ($articles + $news);
  }

  /**
   * @return ActiveQuery<UserReview>
   */
  public function getReviews(): ActiveQueryInterface
  {
    return $this->hasMany(UserReview::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<PushToken>
   */
  public function getPushTokens(): ActiveQueryInterface
  {
    return $this->hasMany(PushToken::class, ['user_id' => 'id']);
  }

  /**
   * @throws \yii\db\Exception
   */
  public function activateMfa(): bool
  {
    $session = Yii::$app->session;
    if (!$session->has('mfa_secret_temp')) {
      throw new RuntimeException('Нет временного секрета!');
    }
    $this->mfa_secret = $session->get('mfa_secret_temp');
    $session->remove('mfa_secret_temp');
    Session::deleteAll([
      'AND',
      ['user_id' => Yii::$app->user->id],
      ['NOT', ['id' => Yii::$app->session->id]],
    ]);
    return $this->save();
  }

  /**
   * @throws \yii\db\Exception
   */
  public function disableMfa(): bool
  {
    Yii::$app->session->remove('mfa_secret_temp');
    $this->mfa_secret = null;
    return $this->save();
  }

  /**
   * @return ActiveQuery<Blog>
   */
  public function getPersonalBlog(): ActiveQueryInterface
  {
    return $this
      ->hasOne(Blog::class, ['user_owner_id' => 'id'])
      ->andWhere(['blog_type' => Blog::TYPE_PERSONAL]);
  }

  /**
   * @throws InvalidConfigException
   * @throws \yii\db\Exception
   */
  public function recalculateCredibility(): void
  {
    /** @var int $gameRequestsProcessed */
    $gameRequestsProcessed = GameRequest::find()
      ->where(['user_id' => $this->id])
      ->andWhere([
        'OR',
        ['NOT', ['game_id' => null]],
        ['NOT', ['closed_by' => null]],
      ])
      ->count();

    /** @var int $gameFixesProcessed */
    $gameFixesProcessed = GameFixSuggestion::find()
      ->where(['user_id' => $this->id])
      ->andWhere(['NOT', ['status' => GameFixSuggestion::STATUS_NEW]])
      ->count();

    /** @var int $gameImagesProcessed */
    $gameImagesProcessed = GameImageRequest::find()
      ->where(['user_id' => $this->id])
      ->andWhere(['NOT', ['status' => GameImageRequest::STATUS_NEW]])
      ->count();

    $totalProcessed = $gameRequestsProcessed + $gameFixesProcessed + $gameImagesProcessed;

    if ($totalProcessed >= 10) {
      /** @var int $gameRequestsRequested */
      $gameRequestsRequested = GameRequest::find()
        ->where(['user_id' => $this->id])
        ->count();

      /** @var int $gameFixesRequested */
      $gameFixesRequested = GameFixSuggestion::find()
        ->where(['user_id' => $this->id])
        ->count();

      /** @var int $gameImagesRequested */
      $gameImagesRequested = GameImageRequest::find()
        ->where(['user_id' => $this->id])
        ->count();

      $totalRequested = $gameRequestsRequested + $gameFixesRequested + $gameImagesRequested;

      /** @var int $gameRequestsAccepted */
      $gameRequestsAccepted = GameRequest::find()
        ->where(['user_id' => $this->id])
        ->andWhere(['NOT', ['game_id' => null]])
        ->count();

      /** @var int $gameFixesAccepted */
      $gameFixesAccepted = GameFixSuggestion::find()
        ->where(['user_id' => $this->id])
        ->andWhere(['status' => GameFixSuggestion::STATUS_ACCEPTED])
        ->count();

      /** @var int $gameImagesAccepted */
      $gameImagesAccepted = GameImageRequest::find()
        ->where(['user_id' => $this->id])
        ->andWhere(['status' => GameImageRequest::STATUS_ACCEPTED])
        ->count();

      $totalAccepted = $gameRequestsAccepted + $gameFixesAccepted + $gameImagesAccepted;

      $this->credibility = (int)\round(($totalAccepted / $totalRequested) * 100);
      $this->save();
    }
  }

  /**
   * @return ActiveQuery<StalcraftUser>
   */
  public function getStalcraftUser(): ActiveQuery
  {
    return $this->hasOne(StalcraftUser::class, ['user_id' => 'id']);
  }

  /**
   * @return ActiveQuery<Compilation>
   */
  public function getCompilations(): ActiveQuery
  {
    return $this->hasMany(Compilation::class, ['user_id' => 'id']);
  }

  protected function getAccessChecker(): ?ManagerInterface
  {
    return $this->accessChecker ?? Yii::$app->getAuthManager();
  }

  /**
   * @param array<string, mixed> $params
   */
  public function can(string $permissionName, array $params = [], bool $allowCaching = true): bool
  {
    /** @var ?ManagerInterface $accessChecker */
    $accessChecker = $this->getAccessChecker();
    if (empty($accessChecker) || empty($this->id)) {
      return false;
    }

    if ($allowCaching && empty($params) && !isset($this->_access[$permissionName])) {
      $allPermissions = $accessChecker->getPermissions();
      $userPermissions = $accessChecker->getPermissionsByUser($this->id);

      foreach ($allPermissions as $name => $model) {
        $this->_access[$name] = \array_key_exists($name, $userPermissions);
      }
    }

    if ($allowCaching && empty($params) && isset($this->_access[$permissionName])) {
      return $this->_access[$permissionName];
    }

    return $accessChecker->checkAccess($this->id, $permissionName, $params);
  }
}
