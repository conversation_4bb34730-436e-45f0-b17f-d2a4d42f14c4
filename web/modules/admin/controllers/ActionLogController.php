<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use frontend\traits\Access;
use yii\data\{Sort, ActiveDataProvider};
use frontend\modules\admin\AdminController;
use yii\web\{Request, Response, BadRequestHttpException};
use common\models\{User, Vote, Topic, ActionLog, UserReview};

/**
 * @property Request $request
 * @noinspection PhpUnused
 */
class ActionLogController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['action_log_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $dataProvider = new ActiveDataProvider([
      'query' => ActionLog::find()->orderBy(['created_at' => \SORT_DESC]),
    ]);

    return $this->render('index', \compact('dataProvider'));
  }

  /**
   * @throws BadRequestHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['action_log_access'], methods: ['GET'])]
  public function actionRating(): string|Response
  {
    $entity = $this->request->get('entity', 'user');

    $methodName = 'rating' . \ucfirst($entity);

    if (!\method_exists($this, $methodName)) {
      throw new BadRequestHttpException('Неверный тип сущности');
    }

    return $this->{$methodName}();
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  public function ratingUser(): string
  {
    $userName = $this->request->get('user_name');
    $type = $this->request->get('type', 'rating');
    $user = null;
    $dataProvider = null;

    if ($userName) {
      /** @var ?User $user */
      $user = User::find()
        ->where(['user_name' => $userName])
        ->one();

      if (!$user) {
        throw new BadRequestHttpException('Пользователь не найден');
      }

      if (!\in_array($type, ['rating', 'actions'])) {
        throw new BadRequestHttpException('Неверный тип логов');
      }

      if ($type === 'rating') {
        $query = Vote::find()
          ->where([
            'target_type' => ['user', 'user_pid'],
            'target_id' => $user->id,
          ]);
      } else {
        $query = Vote::find()
          ->where([
            'user_voter_id' => $user->id,
          ]);
      }

      $dataProvider = new ActiveDataProvider(\compact('query'));
      $dataProvider->setSort(
        new Sort([
          'defaultOrder' => [
            'vote_date' => \SORT_DESC,
          ],
        ])
      );
    }

    return $this->render('rating-user', \compact('user', 'type', 'dataProvider'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  public function ratingTopic(): string
  {
    $topicId = $this->request->get('topic_id');
    $topic = null;
    $dataProvider = null;

    if ($topicId) {
      /** @var ?Topic $topic */
      $topic = Topic::find()
        ->where(['topic_id' => $topicId])
        ->one();
      if (!$topic) {
        throw new BadRequestHttpException('Топик не найден');
      }
      $dataProvider = new ActiveDataProvider([
        'query' => Vote::find()
          ->where([
            'target_type' => 'topic',
            'target_id' => $topic->topic_id,
          ]),
      ]);
      $dataProvider->setSort(
        new Sort([
          'defaultOrder' => [
            'vote_date' => \SORT_DESC,
          ],
        ])
      );
    }

    return $this->render('rating-topic', \compact('topic', 'dataProvider'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  public function ratingReview(): string
  {
    $reviewId = $this->request->get('review_id');
    $review = null;
    $dataProvider = null;

    if ($reviewId) {
      /** @var ?UserReview $review */
      $review = UserReview::find()
        ->where(['id' => $reviewId])
        ->one();
      if (!$review) {
        throw new BadRequestHttpException('Отзыв не найден');
      }
      $dataProvider = new ActiveDataProvider([
        'query' => Vote::find()
          ->where([
            'target_type' => 'user_review',
            'target_id' => $review->id,
          ]),
      ]);
      $dataProvider->setSort(
        new Sort([
          'defaultOrder' => [
            'vote_date' => \SORT_DESC,
          ],
        ])
      );
    }

    if (!$review) {
      throw new BadRequestHttpException('Отзыв не найден');
    }

    return $this->render('rating-review', \compact('review', 'dataProvider'));
  }
}
