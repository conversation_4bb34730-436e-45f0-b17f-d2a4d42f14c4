<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Throwable;
use RuntimeException;
use yii\db\Exception;
use frontend\traits\Access;
use frontend\components\View;
use yii\data\ActiveDataProvider;
use frontend\modules\admin\AdminController;
use common\widgets\formatters\EditorJSFormatter;
use common\models\{Giveaway, GameTitle, GiveawayKey};
use yii\web\{Request, Response, UploadedFile, NotFoundHttpException};

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class GiveawaysController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['giveaways_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $this->view->title = 'Раздачи ключей ⋅ Админка';
    $query = Giveaway::find()
      ->where(['NOT', ['title' => null]])
      ->orderBy(['keys_descr_id' => \SORT_DESC]);

    $dataProvider = new ActiveDataProvider(\compact('query'));

    return $this->render('index', \compact('dataProvider'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['giveaways_access'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $giveaway = Giveaway::findOne($id);

    if (!$giveaway) {
      throw new NotFoundHttpException('Раздача не найдена');
    }

    return $this->render('editor', \compact('giveaway'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['giveaways_access'], methods: ['GET'])]
  public function actionAdd(): string
  {
    $giveaway = new Giveaway();
    return $this->render('editor', \compact('giveaway'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['giveaways_access'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    $post = $this->request->post();

    $giveaway = new Giveaway();

    if ($post['Giveaway']['keys_descr_id']) {
      $giveaway = Giveaway::findOne($post['Giveaway']['keys_descr_id']);
      if (!$giveaway) {
        throw new NotFoundHttpException('Раздача не найдена');
      }
    }

    $giveaway->load($post);

    $giveaway->keys_descr = new EditorJSFormatter([
      'data' => $giveaway->keys_descr,
    ])();

    if (!$giveaway->keys_hash) {
      $giveaway->keys_hash = \md5($giveaway->title . \time());
    }

    if (!$giveaway->save()) {
      return $this->render('editor', \compact('giveaway'));
    }

    return $this->redirect('index');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['giveaways_access'], methods: ['GET'])]
  public function actionKeys(string $id): string
  {
    $giveaway = Giveaway::findOne($id);

    if (!$giveaway) {
      throw new NotFoundHttpException('Раздача не найдена');
    }

    $dataProvider = new ActiveDataProvider([
      'query' => $giveaway->getKeys(),
    ]);

    $this->view->title = "Ключи раздачи «{$giveaway->title}» ⋅ Админка";

    return $this->render('keys', \compact('giveaway', 'dataProvider'));
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['giveaways_access'], methods: ['POST'])]
  public function actionAddKeys(string $id): Response
  {
    $file = UploadedFile::getInstancesByName('keys_file');
    $giveaway = Giveaway::findOne($id);
    $gameId = $this->request->post('game_id');
    $game = GameTitle::findOne(['GameId' => $gameId]);

    if (!$giveaway) {
      throw new NotFoundHttpException('Раздача не найдена');
    }

    if (!isset($file[0])) {
      Yii::$app->session->setFlash('error', 'Файл с ключами не загружен');
      return $this->redirect('/admin/giveaways/keys/' . $id);
    }

    if (!$game) {
      Yii::$app->session->setFlash('error', 'Игра не указана');
      return $this->redirect('/admin/giveaways/keys/' . $id);
    }

    $filename = $file[0]->tempName;

    if (!\file_exists($filename)) {
      Yii::$app->session->setFlash('error', 'Файл с ключами не обнаружен на сервере');
      return $this->redirect('/admin/giveaways/keys/' . $id);
    }

    $reader = \fopen($filename, 'rb');
    if (!$reader) {
      throw new RuntimeException('Failed to open file');
    }
    $lineCount = 0;
    $duplicateCount = 0;
    $addedCount = 0;
    $errorCount = 0;
    /** @noinspection PhpAssignmentInConditionInspection */
    while ($line = \fgets($reader)) {
      $line = \trim($line);
      $lineCount++;

      $duplicate = GiveawayKey::findOne([
        'keys_data' => $line,
        'GameId' => $game->GameId,
      ]);

      if ($duplicate) {
        $duplicateCount++;
        continue;
      }

      $key = new GiveawayKey([
        'keys_data' => $line,
        'keys_hash' => $giveaway->keys_hash,
        'GameId' => $game->GameId,
      ]);

      if (!$key->save()) {
        $errorCount++;
        continue;
      }

      $addedCount++;
    }

    $errorFlash = [];
    if ($duplicateCount) {
      $errorFlash[] = "$duplicateCount из $lineCount ключей не было добавлено т.к. такие ключи для этой игры уже есть в базе";
    }

    if ($errorCount) {
      $errorFlash[] = "$errorCount ключей не добавлено из-за непредвиденных ошибок. Обратись к программисту.";
    }

    if (\count($errorFlash) > 0) {
      Yii::$app->session->setFlash('error', \implode('<br/><br/>', $errorFlash));
    }

    if ($addedCount) {
      Yii::$app->session->setFlash('success', "Добавлено $addedCount из $lineCount ключей");
    }

    return $this->redirect('/admin/giveaways/keys/' . $id);
  }
}
