<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use frontend\traits\Access;
use frontend\components\View;
use frontend\modules\admin\AdminController;

/**
 * @property-read View $view
 * @noinspection PhpUnused
 */
class IndexController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $this->view->title = 'Админка';
    return $this->render('index');
  }
}
