<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use common\models\ShortUrl;
use Random\RandomException;
use frontend\traits\Access;
use yii\data\ActiveDataProvider;
use yii\db\StaleObjectException;
use yii\base\InvalidConfigException;
use frontend\modules\admin\AdminController;
use yii\web\{Response, NotFoundHttpException};
use common\validators\RouteConflictValidator;

/**
 * Short URL management controller for admin
 * @noinspection PhpUnused
 */
class ShortUrlController extends AdminController
{
  /**
   * Lists all short URLs
   * @throws Throwable
   * @throws InvalidConfigException
   */
  #[Access(permissions: ['admin'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $dataProvider = new ActiveDataProvider([
      'query' => ShortUrl::find()->orderBy(['short_code' => \SORT_ASC]),
      'pagination' => [
        'pageSize' => 20,
      ],
    ]);

    return $this->render('index', \compact('dataProvider'));
  }

  /**
   * Creates a new short URL
   * @throws InvalidConfigException
   * @throws Throwable
   * @throws RandomException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin'], methods: ['GET', 'POST'])]
  public function actionCreate(): Response|string
  {
    $model = new ShortUrl();

    if ($model->load(Yii::$app->request->post())) {
      // Generate short code if not provided
      if (empty($model->short_code)) {
        $model->short_code = ShortUrl::generateUniqueCode();
      }

      if ($model->save()) {
        Yii::$app->session->setFlash('success', 'Ссылка создана');
        return $this->redirect(['index']);
      }
      Yii::$app->session->setFlash(
        'error',
        'Не удалось создать ссылку: ' . \implode(', ', $model->getFirstErrors())
      );
    }

    return $this->render('create', \compact('model'));
  }

  /**
   * Updates an existing short URL
   * @throws NotFoundHttpException
   * @throws Throwable
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin'], methods: ['GET', 'POST'])]
  public function actionUpdate(string $code): Response|string
  {
    $model = $this->findModel($code);
    $model->scenario = ShortUrl::SCENARIO_UPDATE;

    /** @noinspection NotOptimalIfConditionsInspection */
    if ($model->load(Yii::$app->request->post()) && $model->save()) {
      Yii::$app->session->setFlash('success', 'Ссылка обновлена');
      return $this->redirect(['index']);
    }

    return $this->render('update', \compact('model'));
  }

  /**
   * Deletes an existing short URL
   * @throws NotFoundHttpException
   * @throws Throwable
   * @throws StaleObjectException
   */
  #[Access(permissions: ['admin'], methods: ['GET', 'POST'])]
  public function actionDelete(string $code): Response
  {
    $model = $this->findModel($code);

    if ($model->delete()) {
      Yii::$app->session->setFlash('success', 'Short URL deleted successfully.');
    } else {
      Yii::$app->session->setFlash('error', 'Failed to delete short URL.');
    }

    return $this->redirect(['index']);
  }

  /**
   * Generate a new random short code
   * @return array{success: bool, code?: string, error?: string}
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin'], methods: ['GET'])]
  public function actionGenerateCode(): array
  {
    Yii::$app->response->format = Response::FORMAT_JSON;

    try {
      $code = ShortUrl::generateUniqueCode();
      return ['success' => true, 'code' => $code];
    } catch (Exception $e) {
      return ['success' => false, 'error' => $e->getMessage()];
    }
  }

  /**
   * Check if a short code is available
   * @return array{available: bool, message: string}
   * @throws InvalidConfigException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin'], methods: ['GET'])]
  public function actionCheckCode(string $code): array
  {
    Yii::$app->response->format = Response::FORMAT_JSON;

    $exists = ShortUrl::find()->where(['short_code' => $code])->exists();
    $hasConflict = new RouteConflictValidator()->isConflictingWithRoutes($code);
    $available = !$exists && !$hasConflict;

    return [
      'available' => $available,
      'message' => $available ? 'Короткий адрес доступен' : 'Короткий адрес уже занят'
    ];
  }

  /**
   * Finds the ShortUrl model based on its primary key value
   * @throws NotFoundHttpException
   */
  protected function findModel(string $code): ShortUrl
  {
    $model = ShortUrl::findOne($code);
    if ($model !== null) {
      return $model;
    }

    throw new NotFoundHttpException('The requested short URL does not exist.');
  }
}
