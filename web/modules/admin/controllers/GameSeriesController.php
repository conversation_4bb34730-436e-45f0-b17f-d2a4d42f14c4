<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use frontend\traits\Access;
use common\helpers\SgHelper;
use yii\data\ActiveDataProvider;
use yii\base\InvalidConfigException;
use yii\db\{Expression, StaleObjectException};
use common\models\{GameSeries, GameSeriesGame};
use yii\web\{Request, Response, NotFoundHttpException};
use frontend\modules\admin\{AdminController, models\GameSearchForm};

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class GameSeriesController extends AdminController
{
  /**
   * @throws InvalidConfigException
   * @throws Throwable
   * @throws \ReflectionException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $searchModel = new GameSearchForm();
    $get = $this->request->get();
    $isLast = false;

    if (isset($get['term']) && !empty(\trim($get['term']))) {
      $term = \trim($get['term']);
      $searchModel->title = $term;
      $term = SgHelper::stripThe($term);
      $seriesQuery = GameSeries::find()
        ->andWhere(['LIKE', 'name', $term])
        ->orderBy(new Expression('CHAR_LENGTH(name) ASC'));
    } else {
      $isLast = true;
      $seriesQuery = GameSeries::find()
        ->orderBy(['id' => \SORT_DESC]);
    }

    $dataProvider = new ActiveDataProvider([
      'query' => $seriesQuery,
      'pagination' => [
        'defaultPageSize' => 24,
      ],
    ]);

    return $this->render(
      'index',
      \compact(
        'dataProvider',
        'searchModel',
        'isLast',
      ),
    );
  }

  /**
   * @throws NotFoundHttpException
   * @throws StaleObjectException
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['games_access'], methods: ['GET'])]
  public function actionSeriesDelete(int $id): Response
  {
    $series = GameSeries::find()
      ->where(\compact('id'))
      ->one();

    if (!$series) {
      throw new NotFoundHttpException('Серия игр не найдена');
    }

    GameSeriesGame::deleteAll(['game_series_id' => $id]);

    $series->delete();

    $url = '/admin/games/series-list';
    if (!empty($this->request->referrer)) {
      $url = $this->request->referrer;
    }

    return $this->redirect($url);
  }
}
