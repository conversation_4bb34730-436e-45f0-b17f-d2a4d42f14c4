<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Throwable;
use yii\web\Request;
use yii\web\Response;
use yii\db\Expression;
use yii\data\Pagination;
use yii\web\UploadedFile;
use frontend\traits\Access;
use common\helpers\SgHelper;
use common\models\Compilation;
use yii\data\ActiveDataProvider;
use yii\db\StaleObjectException;
use yii\web\NotFoundHttpException;
use common\models\CompilationItem;
use yii\base\InvalidConfigException;
use frontend\modules\admin\AdminController;

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class CollectionsController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionIndex(string $id = Compilation::TYPE_TEXT): string
  {
    if ($id === Compilation::TYPE_TEXT) {
      $id = [
        Compilation::TYPE_TEXT,
        Compilation::TYPE_BEST_ARTICLES,
        Compilation::TYPE_WHAT_TO_PLAY,
      ];
    }

    $dataProvider = new ActiveDataProvider([
      'query' => Compilation::find()
        ->select(['compilations.*', 'last_update' => new Expression('MAX(compilation_items.updated_at)')])
        ->joinWith('compilationItems')
        ->where(['type' => $id])
        ->orderBy(['last_update' => \SORT_DESC])
        ->groupBy(['compilations.id']),
    ]);
    $dataProvider->setPagination(
      new Pagination([
        'defaultPageSize' => 21,
      ])
    );

    return $this->render('index', \compact('dataProvider'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionAdd(): Response|string
  {
    $compilation = new Compilation();
    $items = [];

    if ($this->request->isPost) {
      $compilation->load($this->request->post());
      $compilation->user_id = 631906; // Пользователь «Редакция»
      $primary = $this->request->post('primary', []);
      $order = $this->request->post('order', []);

      $poster = UploadedFile::getInstance($compilation, 'poster');
      if ($poster) {
        $paths = SgHelper::getUploadPaths('compilations');
        $poster->saveAs($paths['path']);
        $compilation->poster = $paths['url'];
      }

      if ($compilation->save()) {
        $newItems = $this->request->post('items', []);
        foreach ($newItems as $newItem) {
          $item = new CompilationItem([
            'compilation_id' => $compilation->id,
            'target_type' => \substr($newItem, 0, (int)\strpos($newItem, '/')),
            'target_id' => (int)\substr($newItem, \strpos($newItem, '/') + 1),
            'primary' => ($primary[$newItem] === 'true') ? 1 : 0,
            'order' => $order[$newItem] ?? 0,
          ]);
          $item->save();
        }
        Yii::$app->session->setFlash('success', 'Коллекция сохранена');
        return $this->redirect('/admin/collections');
      }
      $itemsKeys = $this->request->post('items', []);
      foreach ($itemsKeys as $key) {
        $items[] = new CompilationItem([
          'compilation_id' => $compilation->id,
          'target_type' => \substr($key, 0, (int)\strpos($key, '/')),
          'target_id' => (int)\substr($key, \strpos($key, '/') + 1),
          'primary' => ($primary[$key] === 'true') ? 1 : 0,
        ]);
      }
    }

    return $this->render('edit', \compact('compilation', 'items'));
  }

  /**
   * @throws Throwable
   * @throws InvalidConfigException
   * @throws StaleObjectException
   * @throws NotFoundHttpException
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionEdit(string $id): Response|string
  {
    $compilation = Compilation::findOne($id);
    if (!$compilation) {
      throw new NotFoundHttpException('Подборка не найдена');
    }

    /** @var CompilationItem[] $items */
    $items = $compilation->getCompilationItems()->orderBy(['updated_at' => \SORT_ASC])->all();

    if ($this->request->isPost) {
      $compilation->load($this->request->post());
      $primary = $this->request->post('primary', []);
      $order = $this->request->post('order', []);

      $poster = UploadedFile::getInstance($compilation, 'poster');
      if ($poster) {
        $paths = SgHelper::getUploadPaths('compilations');
        $poster->saveAs($paths['path']);
        $compilation->poster = $paths['url'];
      }

      if ($compilation->save()) {
        $newItems = $this->request->post('items', []);
        $itemsHash = [];
        foreach ($items as $item) {
          $hash = "$item->target_type/$item->target_id";
          $primaryBool = (int)(isset($primary[$hash]) && ($primary[$hash] === 'true'));
          if (!\in_array($hash, $newItems)) {
            $item->delete();
          } else {
            $itemsHash[] = $hash;
            if ($item->primary !== $primaryBool) {
              $item->primary = $primaryBool;
            }
            $item->order = $order[$hash];
            $item->save();
          }
        }
        foreach ($newItems as $newItem) {
          if (!\in_array($newItem, $itemsHash)) {
            $item = new CompilationItem([
              'compilation_id' => $compilation->id,
              'target_type' => \substr($newItem, 0, (int)\strpos($newItem, '/')),
              'target_id' => (int)\substr($newItem, \strpos($newItem, '/') + 1),
              'primary' => ($primary[$newItem] === 'true') ? 1 : 0,
              'order' => $order[$newItem] ?? 0,
            ]);
            $item->save();
          }
        }
        Yii::$app->session->setFlash('success', 'Коллекция сохранена');
        return $this->redirect('/admin/collections');
      }
      $itemsKeys = $this->request->post('items', []);
      $items = [];
      foreach ($itemsKeys as $key) {
        $items[] = new CompilationItem([
          'compilation_id' => $compilation->id,
          'target_type' => \substr($key, 0, (int)\strpos($key, '/')),
          'target_id' => (int)\substr($key, \strpos($key, '/') + 1),
          'primary' => ($primary[$key] === 'true') ? 1 : 0,
        ]);
      }
    }

    return $this->render('edit', \compact('compilation', 'items'));
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @throws NotFoundHttpException
   */
  #[Access(permissions: ['admin_access'], methods: ['GET', 'POST'])]
  public function actionDelete(string $id): Response
  {
    $compilation = Compilation::findOne($id);
    if (!$compilation || ($compilation->id === null)) {
      throw new NotFoundHttpException('Подборка не найдена');
    }
    CompilationItem::deleteAll(['compilation_id' => $compilation->id]);
    $compilation->delete();
    Yii::$app->session->setFlash('success', 'Коллекция удалена');
    return $this->redirect('/admin/collections');
  }
}
