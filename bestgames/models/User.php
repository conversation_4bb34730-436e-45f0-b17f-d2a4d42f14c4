<?php

namespace bestgames\models;

use common\models\BaseUser;
use common\models\Bestgames;
use yii\db\ActiveQueryInterface;
use common\components\db\ActiveQuery;
use common\models\BestgamesNomination;

/**
 * This is the model class for table "itaf_user".
 *
 * @property-read Bestgames[] $votes См. {@link self::getVotes}
 */
class User extends BaseUser
{
  public ?BestgamesNomination $lastNomination {
    get {
      /** @var ?Bestgames $lastVote */
      $lastVote = $this->getVotes()->orderBy(['date' => \SORT_DESC])->one();
      return $lastVote?->nomination;
    }
  }

  public bool $hasResults {
    get => !!$this->votes;
  }

  /**
   * @return ActiveQuery<Bestgames>
   */
  public function getVotes(): ActiveQueryInterface
  {
    return $this->hasMany(Bestgames::class, ['user_id' => 'id'])->inverseOf('user');
  }
}
