<?php

namespace frontend\modules\admin\controllers;

use Yii;
use DateTime;
use Override;
use Throwable;
use Exception;
use frontend\traits\Access;
use common\helpers\SgHelper;
use frontend\components\View;
use frontend\widgets\CssModule;
use frontend\modules\admin\AdminController;
use common\widgets\formatters\PresentationFormatter;
use yii\web\{Request, Response, UploadedFile, NotFoundHttpException, ForbiddenHttpException};
use common\models\{Stream, Article, GameTitle, GameRequest, ArticleGame, RecurringStream, GameRequestAttachment};

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class StreamController extends AdminController
{
  #[Override]
  public function render($view, $params = []): string
  {
    $this->view->params['catalog-tabs'] = [
      [
        'label' => 'Стримы',
        'url' => '/admin/stream',
        'active' => Yii::$app->requestedRoute === 'admin/stream',
      ],
      [
        'label' => 'Регулярные стримы',
        'url' => '/admin/stream/recurring',
        'active' => Yii::$app->requestedRoute === 'admin/stream/recurring',
      ]
    ];
    return parent::render($view, $params);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['stream_access'], methods: ['GET'])]
  public function actionIndex(?int $year = null, ?int $month = null): string
  {
    $this->view->title = 'Стримы';

    $year ??= (int)\date('Y');
    $month ??= (int)\date('m');

    $selectStartDate = new DateTime("$year-$month-01 00:00:00")->modify('monday this week 00:00:00');
    $selectEndDate = new DateTime("$year-$month-01 00:00:00")->modify('last day of this month')
      ->modify('sunday this week 23:59:59');

    $startDate = new DateTime("$year-$month-01 00:00:00");
    $endDate = new DateTime("$year-$month-01 23:59:59")->modify('last day of this month');

    $streams = Stream::find()
      ->where(['>=', 'date_start', $selectStartDate->format('Y-m-d H:i:s')])
      ->andWhere(['<=', 'date_start', $selectEndDate->format('Y-m-d H:i:s')])
      ->orderBy(['date_start' => \SORT_ASC])
      ->all();

    $recurringStreams = RecurringStream::find()
      ->orderBy(['priority' => \SORT_ASC])
      ->all();

    return $this->render('calendar', \compact('streams', 'year', 'month', 'startDate', 'endDate', 'recurringStreams'));
  }

  /** @noinspection PhpUnused */
  #[Access(permissions: ['stream_access'], methods: ['GET'])]
  public function actionToggle(): Response
  {
    Yii::$app->config->twitch_steam_online = (string)(int)!Yii::$app->config->twitch_steam_online;
    return $this->redirect('/admin');
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['stream_create'], methods: ['GET', 'POST'])]
  public function actionAdd(): Response|string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Стримы', '/admin/stream');
    Yii::$app->breadcrumbs->addElement('Новый стрим');
    $this->view->title = 'Новый стрим ⋅ Админка';

    $stream = new Stream();
    $date = null;

    if ($this->request->get('date')) {
      $date = new DateTime($this->request->get('date'));
    }

    if ($this->request->get('recurring_id')) {
      $recurring = RecurringStream::findOne($this->request->get('recurring_id'));
      if ($recurring) {
        if ($date) {
          $date = $date->modify($recurring->nextDate->format('H:i'));
        } else {
          $date = $recurring->nextDate;
        }
        $stream->title = $recurring->title;
        $stream->editor_js_content = $recurring->editor_js_content;
        $stream->broadcaster_ids = $recurring->broadcaster_ids;
        $stream->poster = $recurring->poster;
      }
    }

    if ($date) {
      $stream->date_start = $date->format('Y-m-d H:i:s');
      $stream->date_end = $date->modify('+2 hours')->format('Y-m-d H:i:s');
    }

    if ($this->request->isPost) {
      $this->saveStream($stream);
      if (!$stream->hasErrors()) {
        return $this->redirect('/admin/stream');
      }
    }

    return $this->render('editor', \compact('stream'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['stream_create'], methods: ['GET', 'POST'])]
  public function actionEdit(string $id): Response|string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Стримы', '/admin/stream');
    Yii::$app->breadcrumbs->addElement('Редактирование стрима');
    $this->view->title = 'Редактирование стрима ⋅ Админка';

    $stream = Stream::findOne($id);

    if (!$stream) {
      throw new NotFoundHttpException('Стрим не найден');
    }

    if (!$stream->canEdit) {
      throw new ForbiddenHttpException('Нельзя редактировать чужой стрим');
    }

    if ($this->request->isPost) {
      $this->saveStream($stream);
      if (!$stream->hasErrors()) {
        return $this->redirect('/admin/stream');
      }
    }

    if (empty($stream->date_end)) {
      $stream->date_end = new DateTime($stream->date_start)->modify('+2 hours')->format('Y-m-d H:i:s');
    }

    return $this->render('editor', \compact('stream'));
  }

  /**
   * @throws Exception
   */
  protected function saveStream(Stream $stream): void
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Стримы', '/admin/stream');
    $post = $this->request->post();
    $post['Stream']['game_id'] ??= null;
    if (\is_array($post['Stream']['game_id'])) {
      $post['Stream']['game_id'] = $post['Stream']['game_id'][0] ?? null;
    }
    $stream->load($post);

    if ($stream->isNewRecord) {
      $stream->user_id = (int)Yii::$app->user->id;
    }

    Yii::$app->breadcrumbs->addElement($stream->isNewRecord ? 'Новый стрим' : 'Редактирование стрима');

    if (isset($post['submit_stream_publish'])) {
      $stream->is_published = 1;
    } elseif (isset($post['submit_stream_draft'])) {
      $stream->is_published = 0;
    }

    $poster = UploadedFile::getInstance($stream, 'poster');
    if ($poster) {
      $paths = SgHelper::getUploadPaths('streams');
      $poster->saveAs($paths['path']);
      $stream->poster = $paths['url'];
    }
    $recPoster = UploadedFile::getInstance($stream, 'poster_rec');
    if ($recPoster) {
      $paths = SgHelper::getUploadPaths('streams');
      $recPoster->saveAs($paths['path']);
      $stream->poster_rec = $paths['url'];
    }
    $livePoster = UploadedFile::getInstance($stream, 'poster_live');
    if ($livePoster) {
      $paths = SgHelper::getUploadPaths('streams');
      $livePoster->saveAs($paths['path']);
      $stream->poster_live = $paths['url'];
    }

    if (!\is_numeric($stream->game_id)) {
      $stream->game_id = null;
    }

    $stream->updated_at = \time();
    $stream->save();

    $requestIds = $post['game_request_id'] ?? [];
    $requestTitles = $post['game_request_title'] ?? [];

    GameRequestAttachment::deleteAll([
      'target_type' => 'stream',
      'target_id' => $stream->id,
    ]);

    // Привязываем запросы на добавление игр
    foreach ($requestTitles as $requestTitle) {
      $foundGame = GameTitle::findOne(['GameName' => $requestTitle]);

      // Если уже есть игра с таким названием, то просто привязываем игру
      if ($foundGame) {
        $stream->game_id = $foundGame->GameId;
      } else {
        $foundRequest = GameRequest::findOne(['title' => $requestTitle]);

        // Если уже есть запрос с таким названием
        if ($foundRequest) {
          // И он уже обработан — привязываем игру
          if ($foundRequest->game_id) {
            $stream->game_id = $foundRequest->game_id;
            // И он ещё не обработан — привязываем материал к запросу
          } else {
            $requestIds[] = $foundRequest->id;
          }
          // Если запроса нет — создаём его и привязываем материал к запросу
        } else {
          $request = new GameRequest([
            'title' => $requestTitle,
            'user_id' => Yii::$app->user->id,
          ]);
          $request->save();
          $request->user?->recalculateCredibility();
          $requestIds[] = $request->id;
        }
      }
    }

    $requestIds = \array_unique($requestIds);
    foreach ($requestIds as $requestId) {
      $attachment = new GameRequestAttachment([
        'request_id' => $requestId,
        'target_type' => 'stream',
        'target_id' => $stream->id,
      ]);
      $attachment->save();
    }

    if (\count($stream->dirtyAttributes) > 0) {
      $stream->save();
    }
  }

  /**
   * @param int<0,1> $value
   * @throws ForbiddenHttpException
   * @throws NotFoundHttpException|\yii\db\Exception
   */
  protected function setPublished(string $id, int $value): Response
  {
    $stream = Stream::findOne($id);

    if (!$stream) {
      throw new NotFoundHttpException('Стрим не найден');
    }

    if (!$stream->canEdit) {
      throw new ForbiddenHttpException('Нельзя редактировать чужой стрим');
    }

    $stream->is_published = $value;
    $stream->save();

    return $this->redirect('/admin/stream');
  }

  /**
   * @throws NotFoundHttpException
   * @throws ForbiddenHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['POST', 'GET'])]
  public function actionPublish(string $id): Response
  {
    return $this->setPublished($id, 1);
  }

  /**
   * @throws NotFoundHttpException
   * @throws ForbiddenHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['POST', 'GET'])]
  public function actionUnpublish(string $id): Response
  {
    return $this->setPublished($id, 0);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    $stream = Stream::findOne($id);

    if (!$stream) {
      throw new NotFoundHttpException('Стрим не найден');
    }

    if (!$stream->canEdit || $stream->is_published) {
      throw new ForbiddenHttpException('Нельзя удалить опубликованный стрим');
    }

    $stream->delete();
    return $this->redirect('/admin/stream');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['GET', 'POST'])]
  public function actionAddRecord(string $id): Response|string
  {
    $stream = Stream::findOne($id);

    if (!$stream) {
      throw new NotFoundHttpException('Стрим не найден');
    }

    // SG::$app->breadcrumbs->addElement('Стримы')

    if (new DateTime($stream->date_start) >= new DateTime()) {
      $commonCss = CssModule::register('common');
      return $this->renderContent(
        "<section>
                <h1 class='{$commonCss['h2']}'>Ошибка</h1>
                <p>Стрим ещё не прошёл</p>
            </section>",
      );
    }

    $article = new Article([
      'data_type' => 'live',
      'data_title' => $stream->title,
      'data_text' => \strip_tags(
        new PresentationFormatter([
          'title' => $stream->title,
          'data' => $stream->editor_js_content,
          'page' => 'all',
        ])(),
      ),
      'video_attr' => 'b:0;',
      'user_id' => $stream->user_id,
      'AuthorName' => $stream->user->userName ?? '',
      'data_logo' => $stream->poster,
      'editor_js_content' => $stream->editor_js_content,
      'data_active' => 1,
      'data_logo_original' => $stream->poster,
    ]);

    if ($this->request->isPost) {
      $article->load($this->request->post());

      if ($article->save()) {
        if ($stream->game_id) {
          $articleGame = new ArticleGame([
            'article_id' => $article->base_id,
            'game_id' => $stream->game_id,
          ]);
          $articleGame->save();
        }
        $stream->record_id = $article->base_id;
        $stream->save();
        return $this->redirect('/admin/video');
      }
    }

    return $this->render('add-record', \compact('article'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['GET'])]
  public function actionRecurring(): string
  {
    $recurringStreams = RecurringStream::find()->orderBy(['priority' => \SORT_ASC])->all();

    return $this->render('recurring', \compact('recurringStreams'));
  }

  /**
   * @throws Throwable
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['GET', 'POST'])]
  public function actionRecurringEdit(?int $id = null): Response|string
  {
    $stream = new RecurringStream();

    if ($id) {
      $stream = RecurringStream::findOne($id);
      if (!$stream) {
        throw new NotFoundHttpException('Регулярный стрим не найден');
      }
    }

    if ($this->request->isPost) {
      $post = $this->request->post();
      $stream->load($post);
      $repetitionCondition = $post['repetition_condition_2'] . ', ' . $post['repetition_condition_3'];
      if (\in_array($post['repetition_condition_1'], ['first', 'last'])) {
        $repetitionCondition = $post['repetition_condition_1'] . ' ' . $post['repetition_condition_2'] . ' of month, ' . $post['repetition_condition_3'];
      }
      $stream->repetition_condition = $repetitionCondition;

      if ($stream->isNewRecord) {
        $stream->user_id = (int)Yii::$app->user->id;
      }

      $stream->save();
      return $this->redirect('/admin/stream/recurring');
    }

    return $this->render('recurring-editor', \compact('stream'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['GET'])]
  public function actionRecurringDelete(int $id): Response
  {
    $stream = RecurringStream::findOne($id);
    if (!$stream) {
      throw new NotFoundHttpException('Регулярный стрим не найден');
    }
    $stream->delete();
    return $this->redirect('/admin/stream/recurring');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['GET'])]
  public function actionRecurringMove(string $direction, int $id): Response
  {
    $stream = RecurringStream::findOne($id);
    if (!$stream) {
      throw new NotFoundHttpException('Регулярный стрим не найден');
    }
    if ($direction === 'up') {
      $stream2 = RecurringStream::find()
        ->where(['<', 'priority', $stream->priority])
        ->orderBy(['priority' => \SORT_DESC])
        ->one();
    } else {
      $stream2 = RecurringStream::find()
        ->where(['>', 'priority', $stream->priority])
        ->orderBy(['priority' => \SORT_ASC])
        ->one();
    }
    if ($stream2) {
      $currentPriority = $stream->priority;
      $stream->priority = $stream2->priority;
      $stream2->priority = $currentPriority;
      $stream->save();
      $stream2->save();
    }
    return $this->redirect('/admin/stream/recurring');
  }

  /**
   * @throws Throwable
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_access'], methods: ['GET'])]
  public function actionImages(int $id): string
  {
    $stream = Stream::findOne($id);
    if (!$stream) {
      throw new NotFoundHttpException('Стрим не найден');
    }
    return $this->render('images', \compact('stream'));
  }

  /**
   * @throws NotFoundHttpException
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['stream_create'], methods: ['GET'])]
  public function actionRecurringToggle(int $id): Response
  {
    $stream = RecurringStream::findOne($id);
    if (!$stream) {
      throw new NotFoundHttpException('Регулярный стрим не найден');
    }
    $stream->paused = (int)!$stream->paused;
    $stream->save();
    return $this->redirect('/admin/stream/recurring');
  }
}
