<?php

namespace frontend\modules\admin\controllers;

use Yii;
use DateTime;
use Exception;
use Throwable;
use yii\db\Expression;
use frontend\traits\Access;
use frontend\components\View;
use yii\caching\TagDependency;
use yii\web\{Request, Response};
use yii\base\InvalidConfigException;
use frontend\modules\admin\AdminController;
use common\helpers\{SgHelper, SgStringHelper};
use common\models\{User, Config, Payment, Article, GameRequest, GameImageRequest, AdditionalPayment, GameFixSuggestion};

use function Sentry\captureException;

/**
 * Class PaymentsController
 *
 * @property-read View $view
 * @property-read Request $request
 *
 * @phpstan-type UserInfoType array{
 *      total: number,
 *      unpaid: number,
 *      articles: Payment[],
 *      editor: Payment[],
 *      news: Payment[],
 *      facts: Payment[],
 *      cheats: Payment[],
 *      trainers: Payment[],
 *      trailers: Payment[],
 *      additional: Payment[],
 *      gameRequests:GameRequest[],
 *      gameFixSuggestions: GameFixSuggestion[],
 *      gameImageRequests: GameImageRequest[],
 *  }
 */
class PaymentsController extends AdminController
{
  /** @var int Число месяца до которого мы платим за предыдущий месяц. После него — аванс за текущий. */
  public int $avanceDay = 25;

  // Пользователь, которому считаем редакторскую надбавку
  public static int $editorUser = 161162; // Маковеев
  // Кол-во килобайт по которому считаем ставку редактору
  // Если статья больше указанного лимита, то ставка за редактуру двойная
  public static int $editorSizeLimit = 15;

  public int $endDate {
    get {
      $day = \date('d');
      if ($day < $this->avanceDay) {
        return new DateTime()
          ->modify('last day of previous month 23:59:59')
          ->getTimestamp();
      }
      // Если платим 20 или позже — это «аванс» за текущий
      return \time();
    }
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $month = $this->request->get('month', \date('m'));
    $year = $this->request->get('year', \date('Y'));
    $start = new DateTime("$year-$month-01 00:00:00");
    $end = new DateTime("$year-$month-01 00:00:00");
    $end->modify('last day of this month 23:59:59');

    $paymentsQuery = Payment::find()
      ->where(['>=', 'created_at', $start->getTimestamp()])
      ->andWhere(['<=', 'created_at', $end->getTimestamp()])
      ->orderBy(['created_at' => \SORT_ASC]);
    $gameRequestsQuery = GameRequest::find()
      ->select(['closed_by', 'total' => new Expression('COUNT(*)')])
      ->where(['NOT', ['closed_by' => null]])
      ->andWhere(['>=', 'updated_at', $start->getTimestamp()])
      ->andWhere(['<=', 'updated_at', $end->getTimestamp()])
      ->andWhere(['NOT', ['game_id' => null]])
      ->groupBy(['closed_by'])
      ->asArray();
    $gameFixSuggestionsQuery = GameFixSuggestion::find()
      ->select(['closed_by', 'status', 'total' => new Expression('COUNT(*)')])
      ->where(['NOT', ['closed_by' => null]])
      ->andWhere(['>=', 'updated_at', $start->getTimestamp()])
      ->andWhere(['<=', 'updated_at', $end->getTimestamp()])
      ->groupBy(['closed_by', 'status'])
      ->asArray();
    $gameImageRequestsQuery = GameImageRequest::find()
      ->select(['closed_by', 'status', 'total' => new Expression('COUNT(*)')])
      ->where(['NOT', ['closed_by' => null]])
      ->andWhere(['>=', 'updated_at', $start->getTimestamp()])
      ->andWhere(['<=', 'updated_at', $end->getTimestamp()])
      ->groupBy(['closed_by', 'status'])
      ->asArray();

    if (!Yii::$app->user->can('payments_access') && !Yii::$app->user->can('payments_view_all')) {
      $paymentsQuery->andWhere(['user_id' => Yii::$app->user->id]);
      $gameRequestsQuery->andWhere(['closed_by' => Yii::$app->user->id]);
      $gameFixSuggestionsQuery->andWhere(['closed_by' => Yii::$app->user->id]);
      $gameImageRequestsQuery->andWhere(['closed_by' => Yii::$app->user->id]);
    }

    /** @var Payment[] $payments */
    $payments = $paymentsQuery->all();

    /**
     * @var array<int, UserInfoType> $users
     */
    $users = [];

    foreach ($payments as $payment) {
      if (!isset($users[$payment->user_id])) {
        $users[$payment->user_id] = [
          'total' => 0,
          'unpaid' => 0,
          'articles' => [],
          'editor' => [],
          'news' => [],
          'facts' => [],
          'cheats' => [],
          'trainers' => [],
          'trailers' => [],
          'additional' => [],
          'gameRequests' => 0,
          'gameFixSuggestions' => [],
          'gameImageRequests' => [],
        ];
      }

      if (!$payment->rate) {
        $payment->rate = self::getRate($payment);
      }

      $category = 'other';

      /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
      switch ($payment->target_type) {
        case 'article':
          /** @var Article $target */
          $target = $payment->target;
          $category = 'articles';
          /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
          switch ($target->data_type) {
            case 'cheats':
              $category = 'cheats';
              break;
            case 'trainers':
              $category = 'trainers';
              break;
            case 'trailers':
              $category = 'trailers';
              break;
          }

          if ($payment->is_editor_payment) {
            $payment->size = 1;
            $category = 'editor';
          }
          break;
        case 'facts':
          $category = 'facts';
          break;
        case 'news':
          $category = 'news';
          break;
        case 'additional':
          $category = 'additional';
          break;
      }
      $users[$payment->user_id][$category][] = $payment;
      $sum = $payment->getSum();
      $users[$payment->user_id]['total'] += $sum;
      if (!$payment->is_paid) {
        $users[$payment->user_id]['unpaid'] += $sum;
      }
    }

    /** @var array<array{
     *   closed_by: int,
     *   total: int
     * }> $gameRequests
     */
    $gameRequests = $gameRequestsQuery->all();
    /** @var array<array{
     *   closed_by: int,
     *   status: int,
     *   total: int
     * }> $gameFixSuggestions
     */
    $gameFixSuggestions = $gameFixSuggestionsQuery->all();
    /** @var array<array{
     *   closed_by: int,
     *   status: int,
     *   total: int
     * }> $gameImageRequests
     */
    $gameImageRequests = $gameImageRequestsQuery->all();

    foreach ($gameRequests as $request) {
      if (!isset($users[$request['closed_by']])) {
        $users[$request['closed_by']] = [
          'total' => 0,
          'unpaid' => 0,
          'articles' => [],
          'editor' => [],
          'news' => [],
          'facts' => [],
          'cheats' => [],
          'trainers' => [],
          'trailers' => [],
          'additional' => [],
          'gameFixSuggestions' => [],
          'gameImageRequests' => [],
        ];
      }
      $users[$request['closed_by']]['gameRequests'] = $request['total'];
    }
    unset($gameRequests);

    foreach ($gameFixSuggestions as $request) {
      if (!isset($users[$request['closed_by']])) {
        $users[$request['closed_by']] = [
          'total' => 0,
          'unpaid' => 0,
          'articles' => [],
          'editor' => [],
          'news' => [],
          'facts' => [],
          'cheats' => [],
          'trainers' => [],
          'trailers' => [],
          'additional' => [],
          'gameRequests' => 0,
          'gameFixSuggestions' => [],
          'gameImageRequests' => [],
        ];
      }
      $users[$request['closed_by']]['gameFixSuggestions'][$request['status']] = $request['total'];
    }
    unset($gameFixSuggestions);

    foreach ($gameImageRequests as $request) {
      if (!isset($users[$request['closed_by']])) {
        $users[$request['closed_by']] = [
          'total' => 0,
          'unpaid' => 0,
          'articles' => [],
          'editor' => [],
          'news' => [],
          'facts' => [],
          'cheats' => [],
          'trainers' => [],
          'trailers' => [],
          'additional' => [],
          'gameRequests' => 0,
          'gameFixSuggestions' => [],
          'gameImageRequests' => [],
        ];
      }
      $users[$request['closed_by']]['gameImageRequests'][$request['status']] = $request['total'];
    }
    unset($gameImageRequests);

    /** @var User[] $userInfo */
    $userInfo = User::find()
      ->where(['id' => \array_keys($users)])
      ->all();

    \usort($userInfo, static function (User $userA, User $userB) {
      $namePartsA = \explode(' ', $userA->userName);
      $namePartsB = \explode(' ', $userB->userName);
      $lastNameA = \array_pop($namePartsA);
      $lastNameB = \array_pop($namePartsB);
      if ($lastNameA === $lastNameB) {
        return $userA->userName <=> $userB->userName;
      }
      return $lastNameA <=> $lastNameB;
    });

    return $this->render('index', \compact('payments', 'end', 'start', 'users', 'userInfo'));
  }

  /**
   * @throws InvalidConfigException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['payments_access'], methods: ['POST'])]
  public function actionPay(): Response
  {
    $post = $this->request->post();
    $redirectUrl = $this->request->referrer ?: '/admin/payments';

    if (!isset($post['user_id'])) {
      Yii::$app->session->setFlash('error', 'Не указан ID пользователя');
      return $this->redirect($redirectUrl);
    }

    $user = User::findOne($post['user_id']);

    if (!$user) {
      Yii::$app->session->setFlash('error', 'Указанного пользователя не существует');
      return $this->redirect($redirectUrl);
    }

    $startDate = $post['date_start'];
    $endDate = $post['date_end'];

    Payment::updateAll([
      'is_paid' => 1,
      'paid_at' => new Expression('NOW()'),
    ], [
      'AND',
      ['user_id' => $user->id],
      ['>=', 'created_at', new Expression("UNIX_TIMESTAMP('$startDate')")],
      ['<=', 'created_at', new Expression("UNIX_TIMESTAMP('$endDate')")],
    ]);

    $month = \date('m', \strtotime($startDate) ?: 0);
    $year = \date('Y', \strtotime($startDate) ?: 0);
    $monthName = Yii::$app->formatter->asDate(\strtotime($startDate), 'LLLL');

    $title = "Вознаграждение за $monthName";

    $message = "Твои деньги за прошедший месяц подсчитаны. Оплата до 10 числа включительно.\nПосмотреть детализацию можно перейдя по <a href='https://stopgame.ru/admin/payments?month=$month&year=$year'>этой ссылке</a>";

    if ($user->user_mail) {
      Yii::$app->mailer->compose()
        ->setFrom(['<EMAIL>' => 'StopGame.ru'])
        ->setTo($user->user_mail)
        ->setSubject($title)
        ->setHtmlBody($message)
        ->send();
    }

    SgHelper::newPm(
      $user->id,
      null,
      $title,
      \str_replace("\n", '<br>', $message),
      false,
      Yii::$app->params['robot_user_id'],
    );

    $user->pay_last_date = \date('Y-m-d H:i:s', $this->endDate);
    $user->save();

    Yii::$app->session->setFlash(
      'success',
      "<b>$user->userName</b> {$user->verb('получил', 'получила')} вознаграждение",
    );
    if (Yii::$app->cache) {
      TagDependency::invalidate(Yii::$app->cache, 'payments');
    }

    return $this->redirect($redirectUrl);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['payments_access'], methods: ['GET', 'POST'])]
  public function actionSettings(): Response|string
  {
    $this->view->title = 'Настройки выплат ⋅ Админка';
    /** @var Config[] $settings */
    $settings = Config::find()
      ->where([
        'config_name' => [
          'price_article',
          'price_facts',
          'price_interview',
          'price_news',
          'price_preview',
          'price_review',
          'price_solution',
          'price_tactics',
          'price_trailers',
          'price_video',
          'price_cheats',
          'price_trainers',
          'price_editor_per_article',
        ],
      ])
      ->all();

    if ($this->request->isPost) {
      $post = $this->request->post();

      foreach ($post as $name => $value) {
        foreach ($settings as $setting) {
          if ($setting->config_name === $name) {
            $setting->config_value = $value;
            $setting->save();
            break;
          }
        }
      }
      return $this->redirect('/admin/payments/settings');
    }

    return $this->render('settings', \compact('settings'));
  }

  /**
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['payments_access'], methods: ['POST', 'GET'])]
  public function actionRemoveAdditional(?string $id = null): Response
  {
    /** @var ?AdditionalPayment $payment */
    $payment = AdditionalPayment::findOne($id);

    if (empty($payment)) {
      Yii::$app->session->addFlash('error', 'Платёж не найден');
    } else {
      try {
        $payment->delete();
      } catch (Throwable $e) {
        captureException($e);
        Yii::$app->session->addFlash('error', 'При удалении платежа произошла ошибка');
      }
    }

    return $this->redirect($this->request->referrer ?? '/admin/payments');
  }

  /**
   * @return array{
   *     price: number,
   *     priceFor1K: int,
   *     size: string,
   *     length: number,
   *     fineInfo: string
   * }
   * @noinspection PhpUnused
   * @noinspection PhpMethodMayBeStaticInspection
   */
  public function getArticleCost(Article $article): array
  {
    $length = SgStringHelper::strlenu(\strip_tags(\str_replace(' ', '', $article->data_text ?? ''))) / 1000;
    $priceFor1K = 0;

    /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
    switch ($article->data_type) {
      case 'review':
        $priceFor1K = Yii::$app->config->price_review;
        break;
      case 'preview':
        $priceFor1K = Yii::$app->config->price_preview;
        break;
      case 'solutions':
        $priceFor1K = Yii::$app->config->price_solution;
        break;
      case 'tactics':
        $priceFor1K = Yii::$app->config->price_tactics;
        break;
      case 'interview':
        $priceFor1K = Yii::$app->config->price_interview;
        break;
      case 'analytics':
      case 'frontpage':
      case 'game_compilations':
      case 'hard':
        $priceFor1K = Yii::$app->config->price_article;
        break;
    }

    if ($article->video_attr || $article->video_external) {
      $priceFor1K = 0;
    }

    $fineInfo = '';

    // Двойная ставка для партнёрских материалов
    if ($article->press_release) {
      $priceFor1K *= 2;
      $fineInfo .= ' (партнёрский материал)';
    }

    $price = $length * $priceFor1K;

    if ($article->user_penalty) {
      $price -= $price * ($article->user_penalty / 100);
      $fineInfo .= ", штраф - $article->user_penalty%";
    }

    $size = \number_format($length, 1, ',', '<span style="user-select: none"> </span>');

    return \compact('price', 'priceFor1K', 'size', 'length', 'fineInfo');
  }

  public static function getRate(Payment $payment): int
  {
    $config = Yii::$app->config;

    /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
    switch ($payment->target_type) {
      case 'article':
        $rate = $config->price_article;
        /** @var Article $article */
        $article = $payment->target;
        /** @noinspection PhpSwitchCaseWithoutDefaultBranchInspection */
        switch ($article->data_type) {
          case 'review':
            $rate = $config->price_review;
            break;
          case 'preview':
            $rate = $config->price_preview;
            break;
          case 'solutions':
            $rate = $config->price_solution;
            break;
          case 'tactics':
            $rate = $config->price_tactics;
            break;
          case 'interview':
            $rate = $config->price_interview;
            break;
          case 'cheats':
            $rate = $config->price_cheats;
            break;
          case 'trainers':
            $rate = $config->price_trainers;
            break;
          case 'trailers':
            $rate = $config->price_trailers;
            break;
        }

        if ($payment->is_ad) {
          $rate *= 2;
        }

        if ($payment->is_editor_payment) {
          $rate = $config->price_editor_per_article;
          if ($payment->size >= self::$editorSizeLimit) {
            $rate *= 2;
          }
        }

        return $rate;
      case 'facts':
        return $config->price_facts;
      case 'news':
        return $config->price_news;
      case 'additional':
        return 1;
    }

    return 0;
  }

  /**
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['payments_access'], methods: ['POST', 'GET'])]
  public function actionIgnoreLimit(string $id): Response
  {
    $payment = Payment::findOne($id);
    if (!$payment) {
      return $this->redirect('/admin/payments');
    }
    $payment->ignore_limit = 1;
    $payment->save();
    return $this->redirect('/admin/payments');
  }

  /**
   * @throws \yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['payments_access'], methods: ['POST', 'GET'])]
  public function actionForceLimit(string $id): Response
  {
    $payment = Payment::findOne($id);
    if (!$payment) {
      return $this->redirect('/admin/payments');
    }
    $payment->ignore_limit = 0;
    $payment->save();
    return $this->redirect('/admin/payments');
  }
}
