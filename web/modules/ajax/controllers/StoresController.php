<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use frontend\traits\Access;
use common\helpers\SgHelper;
use Tustin\PlayStation\Client;
use frontend\modules\ajax\ApiController;
use Tustin\PlayStation\Iterator\StoreSearchIterator;

/**
 * @noinspection PhpUnused
 */

class StoresController extends ApiController
{
  /**
   * @return array{
   *   results: array<array{
   *     id: string,
   *     name: string,
   *   }>,
   *   total: int,
   *   isFinal: bool
   * }
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionSteamSearch(): array
  {
    $term = $this->request->get('term');

    if (empty($term)) {
      return [
        'results' => [],
        'total' => 0,
        'isFinal' => true,
      ];
    }

    $offset = (int)$this->request->get('offset', 0);

    $steamRegEx = '/^https?:\/\/store\.steampowered\.com\/app\/(\d+)\/?/i';
    if (\preg_match($steamRegEx, $term)) {
      $appinfo = SgHelper::steamParser($term);
      if (!$appinfo || !isset($appinfo['data']['steam_appid'], $appinfo['data']['name'])) {
        // @phpstan-ignore-next-line
        $id = (string)\preg_replace($steamRegEx, '$1', $term);
        return [
          'results' => [
            [
              'id' => $id,
              'name' => '[Не удалось получить название игры]'
            ]
          ],
          'total' => 0,
          'isFinal' => true,
        ];
      }
      Yii::$app->meili->index('steam')->addDocuments([
        [
          'appid' => $appinfo['data']['steam_appid'],
          'name' => $appinfo['data']['name'],
        ]
      ]);
      return [
        'results' => [
          [
            'id' => $appinfo['data']['steam_appid'],
            'name' => $appinfo['data']['name'],
          ]
        ],
        'total' => 1,
        'isFinal' => true,
      ];
    }

    $result = Yii::$app->meili->index('steam')->search($term, [
      'limit' => 15,
      'offset' => $offset,
    ]);

    $total = $result->getEstimatedTotalHits();

    if ($total === null) {
      $total = 0;
    }

    return [
      'results' => \array_map(static fn($hit) => [
        'id' => $hit['appid'],
        'name' => $hit['name'],
      ], $result->getHits()),
      'total' => $total,
      'isFinal' => \count($result->getHits()) < 15,
    ];
  }

  /**
   * @return array{
   *   results: array<array{
   *     id: string,
   *     image: string,
   *     name: string,
   *   }>,
   *   total: int,
   *   isFinal: bool
   * }
   * @noinspection PhpUnused
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['GET'])]
  public function actionPsnSearch(): array
  {
    $npsso = 'OSiZbQx4N8srZdp2978oJuAUoEzzKO3Bq405A5RjXXu64WCXSaI78DID518SJSWt';
    $term = $this->request->get('term');

    if (empty($term)) {
      return [
        'results' => [],
        'total' => 0,
        'isFinal' => true,
      ];
    }

    $client = new Client();
    $client->loginWithNpsso($npsso);
    try {
      /**
       * @var StoreSearchIterator $games
       */
      $games = $client->store()->search($term);
    } catch (\Throwable) {
      return [
        'results' => [],
        'total' => 0,
        'isFinal' => true,
      ];
    }

    $results = [];
    foreach ($games as $game) {
      $masterImage = '';
      $images = $game->pluck('media.images');
      foreach ($images as $image) {
        if ($image['type'] === 'MASTER') {
          $masterImage = $image['url'];
        }
      }
      $results[] = [
        'id' => $game->conceptId(),
        'image' => $masterImage,
        'name' => $game->name(),
      ];
    }

    return [
      'results' => $results,
      'total' => $games->getTotalResults(),
      'isFinal' => true
    ];
  }
}
