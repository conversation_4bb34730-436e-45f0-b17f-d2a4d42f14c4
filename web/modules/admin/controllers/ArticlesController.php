<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use common\models\{User,
  Article,
  GameTitle,
  GameRating,
  PublishSchedule
};
use frontend\traits\Access;
use frontend\components\View;
use yii\db\ActiveQueryInterface;
use common\helpers\SectionsHelper;
use yii\base\InvalidConfigException;
use common\components\db\ActiveQuery;
use yii\data\{Sort, ActiveDataProvider};
use frontend\modules\admin\AdminController;
use yii\web\{Request, Response, NotFoundHttpException, ForbiddenHttpException};

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class ArticlesController extends AdminController
{
  /** @var string[] */
  protected array $nonArticleDataUrl = ['video'];

  /**
   * @var ActiveQuery<Article> $articlesQuery
   */
  public ActiveQueryInterface $articlesQuery {
    get => Article::find()
      ->with('user', 'gameTitle', 'announce')
      ->where(['data_type' => SectionsHelper::getArticleSections()])
      ->andWhere(['NOT', ['editor_js_content' => null]])
      ->andWhere([
        'OR',
        ['NOT', ['data_url' => $this->nonArticleDataUrl]],
        ['data_url' => null],
      ]);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['articles_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $query = $this->articlesQuery;
    $query->andWhere([
      'data_active' => 1,
    ]);
    $this->view->title = 'Статьи ⋅ Админка';

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws InvalidConfigException
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['articles_access'], methods: ['GET'])]
  public function actionReadyToPublish(): string
  {
    $this->view->title = 'Статьи ⋅ Админка';
    $query = $this->articlesQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ]);

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['articles_access'], methods: ['GET'])]
  public function actionDrafts(): string
  {
    $this->view->title = 'Статьи ⋅ Админка';
    $user = Yii::$app->user;
    $query = $this->articlesQuery
      ->andWhere([
        'data_active' => false,
        'ready_to_publish' => false,
      ]);

    if (!$user->can('articles_manage')) {
      $query->andWhere(['user_id' => $user->id]);
    }

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['articles_create'], methods: ['GET'])]
  public function actionAdd(): string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Статьи', '/admin/articles');
    Yii::$app->breadcrumbs->addElement('Новая статья');
    $this->view->title = 'Новая статья ⋅ Админка';
    $article = new Article();
    $article->user_id = (int)Yii::$app->user->id;
    $gameTitle = new GameTitle();
    $gameRating = new GameRating();
    $article->setScenario(Article::SCENARIO_DRAFT);

    return $this->renderEditor($article, $gameTitle, $gameRating);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['articles_create'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Статьи', '/admin/articles');
    Yii::$app->breadcrumbs->addElement('Редактирование статьи');
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя редактировать чужую статью');
    }

    if (!$article->data_active && !$article->ready_to_publish) {
      $article->setScenario(Article::SCENARIO_DRAFT);
    }

    $gameTitle = $article->gameTitle ?? new GameTitle();
    $gameRating = $article->gameRating ?? new GameRating();
    $this->view->title = "$article->title ⋅ Админка";

    return $this->renderEditor($article, $gameTitle, $gameRating);
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['articles_manage'], methods: ['POST', 'GET'])]
  public function actionPublish(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    try {
      $article->publish();
      $article->notify();
    } catch (Exception $e) {
      Yii::$app->session->setFlash('error', $e->getMessage());
    }

    return $this->redirect('/admin/articles/index');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['articles_manage'], methods: ['POST', 'GET'])]
  public function actionUnpublish(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    $article->ready_to_publish = 0;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/articles/drafts');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['articles_create'], methods: ['POST', 'GET'])]
  public function actionToReview(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    $article->ready_to_publish = 1;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/articles/ready-to-publish');
  }

  /**
   * @throws Throwable
   */
  protected function renderEditor(Article $article, ?GameTitle $gameTitle, ?GameRating $gameRating): string
  {
    $gameTitle?->setScenario(GameTitle::SCENARIO_ATTACH);
    $gameRating?->setScenario(GameRating::SCENARIO_ATTACH);

    $viewParams = \compact('article', 'gameTitle', 'gameRating');

    if (Yii::$app->user->can('articles_manage')) {
      /** @var User[] $allUsers */
      $allUsers = User::find()
        ->innerJoin('auth_assignment', 'auth_assignment.user_id = itaf_user.id')
        ->all();

      $viewParams['users'] = [];

      foreach ($allUsers as $user) {
        if (Yii::$app->authManager?->checkAccess($user->id, 'articles_create')) {
          $viewParams['users'][$user->id] = $user->userName;
        }
      }
    }

    return $this->render('editor', $viewParams);
  }

  /**
   * @param ActiveQuery<Article> $query
   * @throws Throwable
   */
  protected function renderArticlesTable(ActiveQuery $query): string
  {
    $searchModel = new Article();
    $searchModel->setScenario(Article::SCENARIO_SEARCH);
    $searchModel->load($this->request->get());

    if (!empty($searchModel->data_type)) {
      $query->andWhere(['data_type' => $searchModel->data_type]);
    }

    if (!empty($searchModel->data_title)) {
      $query
        ->joinWith('gameTitle')
        ->andWhere([
          'or',
          ['LIKE', 'data_title', $searchModel->data_title],
          ['LIKE', 'stopgame_title.GameName', $searchModel->data_title],
        ]);
    }

    if (!empty($searchModel->data_add)) {
      $query->andWhere([
        'between',
        'data_add',
        "$searchModel->data_add 00:00:00",
        "$searchModel->data_add 23:59:59",
      ]);
    }

    if (!empty($searchModel->user_id)) {
      $query->andWhere(['user_id' => $searchModel->user_id]);
    }

    if (!empty($searchModel->data_active)) {
      $query->andWhere(['data_active' => $searchModel->data_active]);
    }

    $dataProvider = new ActiveDataProvider(\compact('query'));
    $dataProvider->setSort(
      new Sort([
        'defaultOrder' => ['data_add' => \SORT_DESC],
      ]),
    );

    $readyCount = $this->articlesQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ])->count();

    if ($readyCount === '0') {
      $readyCount = null;
    }

    return $this->render('index', \compact('dataProvider', 'searchModel', 'readyCount'));
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['announcer_access'], methods: ['GET', 'POST'])]
  public function actionAnnounce(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    try {
      $article->announce();
    } catch (Exception $e) {
      Yii::$app->session->setFlash('error', $e->getMessage());
    }

    return $this->redirect('/admin/articles');
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['articles_create'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя удалить чужую статью');
    }

    if ($article->data_active || $article->ready_to_publish) {
      Yii::$app->session->setFlash('error', 'Нельзя удалить статью не являющуюся черновиком');
      return $this->redirect('/admin/articles');
    }

    $article->delete();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/articles/drafts');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['articles_manage'], methods: ['POST', 'GET'])]
  public function actionSchedule(string $id): Response|string
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    $schedule = new PublishSchedule([
      'target_id' => $article->base_id,
      'target_type' => 'show',
      'announce' => 1,
      'notify' => 1,
    ]);

    if ($this->request->isPost) {
      $post = $this->request->post();
      $schedule->load($post);

      if (!isset($post['PublishSchedule']['announce'])) {
        $schedule->announce = 0;
      }

      if ($schedule->save()) {
        $article->clearUnpublishedCache();
        return $this->redirect('/admin/articles/ready-to-publish');
      }
    }

    $article->setScenario(Article::SCENARIO_PUBLISH);
    $isValid = $article->validate();

    return $this->render('schedule', \compact('article', 'schedule', 'isValid'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['articles_manage'], methods: ['POST', 'GET'])]
  public function actionUnschedule(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Статья не найдена');
    }

    if ($article->schedule) {
      $article->schedule->delete();
      $article->clearUnpublishedCache();
    }

    return $this->redirect('/admin/articles/ready-to-publish');
  }
}
