<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use yii\db\Expression;
use frontend\traits\Access;
use common\helpers\SgHelper;
use frontend\components\View;
use yii\data\ActiveDataProvider;
use yii\web\{Request, NotFoundHttpException};
use common\models\{Game, GameFact, GameTitle};
use frontend\modules\admin\{AdminController, models\GameSearchForm};

/**
 * @property-read Request $request
 * @property-read View $view
 * @noinspection PhpUnused
 */
class FactsController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['facts_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $this->view->title = 'Факты ⋅ Админка';

    $searchModel = new GameSearchForm();
    $get = $this->request->get();
    $isLast = false;

    if (isset($get['term']) && !empty(\trim($get['term']))) {
      $term = \trim($get['term']);
      $searchModel->title = $term;
      $term = SgHelper::stripThe($term);
      $ids = GameTitle::find()
        ->select('GameId')
        ->andWhere([
          'OR',
          ['LIKE', 'GameName', $term],
          ['LIKE', 'GameKeywords', $term],
        ])
        ->groupBy(['GameId'])
        ->column();

      $gamesQuery = GameTitle::find()
        ->where([
          'GameId' => $ids,
          'GameNamePrimary' => 1,
        ])
        ->orderBy(new Expression('CHAR_LENGTH(GameName) ASC'));
    } else {
      $isLast = true;
      $ids = GameFact::find()
        ->select('GameId')
        ->orderBy(['fact_id' => \SORT_DESC])
        ->distinct()
        ->limit(10)
        ->column();

      $idsList = \implode(',', $ids);

      $gamesQuery = GameTitle::find()
        ->where([
          'GameId' => $ids,
          'GameNamePrimary' => 1,
        ])
        ->orderBy(new Expression("FIELD(GameId, $idsList)"));
    }

    $gamesDataProvider = new ActiveDataProvider([
      'query' => $gamesQuery,
    ]);

    return $this->render(
      'index',
      \compact(
        'gamesDataProvider',
        'searchModel',
        'isLast',
      ),
    );
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['facts_access'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $this->view->title = 'Факты ⋅ Админка';
    $game = Game::findOne([$id]);

    if (!$game) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    return $this->render('editor', \compact('game'));
  }
}
