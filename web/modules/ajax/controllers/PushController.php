<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use yii\db\Exception;
use frontend\traits\Access;
use common\models\PushToken;
use frontend\modules\ajax\ApiController;

/**
 * @noinspection PhpUnused
 */

class PushController extends ApiController
{
  /**
   * @return array{
   *     permission: string
   * }
   * @noinspection PhpUnused
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionCheck(): array
  {
    $token = $this->request->post('token');
    $user = Yii::$app->user;

    $permission = 'denied-by-token';

    /** @var ?PushToken $tokenIsRegistered */
    $tokenIsRegistered = $user->identity->getPushTokens()
      ->where(\compact('token'))
      ->one();

    if ($tokenIsRegistered?->enabled) {
      $permission = 'granted';
    } elseif (!$tokenIsRegistered) {
      $permission = 'default';
    }

    return \compact('permission');
  }

  /**
   * @return array{
   *     permission: string
   * }
   * @noinspection PhpUnused
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionRegister(): array
  {
    if (Yii::$app->session->get('original_user')) {
      return ['permission' => 'denied-by-token'];
    }

    $token = $this->request->post('token');
    $user = Yii::$app->user;

    /** @var ?PushToken $tokenModel */
    $tokenModel = $user->identity->getPushTokens()
      ->where(\compact('token'))
      ->one();

    if (!$tokenModel) {
      $tokenModel = new PushToken([
        'user_id' => $user->id,
        'token' => $token,
      ]);
    } else {
      $tokenModel->enabled = 1;
    }
    $tokenModel->save();

    return ['permission' => 'granted'];
  }

  /**
   * @return array{
   *     permission: string
   * }
   * @noinspection PhpUnused
   * @throws Exception
   */
  #[Access(requiresAuth: true, methods: ['POST'])]
  public function actionUnregister(): array
  {
    $token = $this->request->post('token');
    $user = Yii::$app->user;

    /** @var ?PushToken $tokenModel */
    $tokenModel = $user->identity->getPushTokens()
      ->where([
        'user_id' => $user->id,
        'token' => $token,
      ])
      ->one();

    if (!$tokenModel) {
      return ['permission' => 'denied-by-token'];
    }

    $tokenModel->enabled = 0;
    $tokenModel->save();
    return ['permission' => 'denied-by-token'];
  }
}
