<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use RuntimeException;
use yii\db\Expression;
use PHPHtmlParser\Dom;
use frontend\traits\Access;
use frontend\components\View;
use yii\data\ActiveDataProvider;
use yii\helpers\{<PERSON><PERSON>, <PERSON><PERSON>yHelper};
use GuzzleHttp\{Client, Exception\GuzzleException};
use common\helpers\{ShellExecutor, SgHelper, SgImageHelper};
use frontend\modules\admin\{AdminController, models\GameSearchForm};
use yii\web\{Request, Response, UploadedFile, NotFoundHttpException, BadRequestHttpException};
use common\models\{ArticleGame, Game, Article, NewsData, GameTitle, GameGallery, GameStoreAttachment};
use PHPHtmlParser\Exceptions\{StrictException, CircularException, NotLoadedException, ChildNotFoundException};

/**
 * Контроллер админки скриншотов
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class ScreenshotsController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['screens_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $this->view->title = 'Скриншоты ⋅ Админка';

    $searchModel = new GameSearchForm();
    $get = $this->request->get();
    $isLast = false;

    if (isset($get['term']) && !empty(\trim($get['term']))) {
      $term = \trim($get['term']);
      $searchModel->title = $term;
      $term = SgHelper::stripThe($term);
      $ids = GameTitle::find()
        ->select('GameId')
        ->andWhere([
          'OR',
          ['LIKE', 'GameName', $term],
          ['LIKE', 'GameKeywords', $term],
        ])
        ->groupBy(['GameId'])
        ->column();

      $gamesQuery = GameTitle::find()
        ->where([
          'GameId' => $ids,
          'GameNamePrimary' => 1,
        ])
        ->orderBy(new Expression('CHAR_LENGTH(GameName) ASC'));
    } else {
      $isLast = true;
      $ids = GameGallery::find()
        ->select('GameId')
        ->orderBy(['screen_id' => \SORT_DESC])
        ->distinct()
        ->limit(10)
        ->column();

      $idsList = \implode(',', $ids);

      $gamesQuery = GameTitle::find()
        ->where([
          'GameId' => $ids,
          'GameNamePrimary' => 1,
        ])
        ->orderBy(new Expression("FIELD(GameId, $idsList)"));
    }

    $gamesDataProvider = new ActiveDataProvider([
      'query' => $gamesQuery,
    ]);

    return $this->render(
      'index',
      \compact(
        'gamesDataProvider',
        'searchModel',
        'isLast',
      ),
    );
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['screens_access'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    $game = Game::findOne($id);

    if (!$game) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    return $this->render('editor', \compact('game'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['screens_access'], methods: ['GET', 'POST'])]
  public function actionParse(string $id): string
  {
    /** @var ?Game $game */
    $game = Game::findOne($id);

    if (!$game) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    if ($this->request->isPost) {
      $url = $this->request->post('url');

      if (\preg_match('/steampowered\.com/i', $url)) {
        $images = $this->parseFromSteam($game, $url);
      } else {
        $images = $this->parseImagesFromUrl($url);
      }
      return $this->render('parse_step2', \compact('game', 'images'));
    }

    return $this->render('parse', \compact('game'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['screens_access'], methods: ['GET', 'POST'])]
  public function actionParseSteam(string $id): string
  {
    $game = Game::findOne($id);

    if (!$game) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    $images = [];
    foreach ($game->steam as $steam) {
      $images = [...$images, ...$this->parseFromSteam($game, "https://store.steampowered.com/app/$steam->store_id/")];
    }
    return $this->render('parse_step2', \compact('game', 'images'));
  }

  /**
   * @return list<string>
   */
  #[Access(permissions: ['screens_access'], methods: ['GET', 'POST'])]
  protected function parseFromSteam(Game $game, string $url): array
  {
    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $data = SgHelper::steamParser($url);
      $images = [];

      if (!$data) {
        return [];
      }

      if (!$game->getSteam()->andWhere(['store_id' => $data['data']['steam_appid']])->exists()) {
        $steam = new GameStoreAttachment([
          'game_id' => $game->GameId,
          'store_id' => $data['data']['steam_appid'],
          'store' => 'steam',
          'hidden' => 0,
        ]);
        $steam->save();
      }

      if ($data['data']['screenshots']) {
        foreach ($data['data']['screenshots'] as $screen) {
          $results = \preg_replace('/\?t=(.*?)$/', '', $screen['path_full']);
          if (\is_array($results)) {
            $results = \implode('', $results);
          }
          if (!empty($results)) {
            $images[] = $results;
          }
        }
      }

      return $images;
    } catch (Exception $e) {
      Yii::$app->session->addFlash('error', $e->getMessage());
      return [];
    }
  }

  /**
   * @return string[]
   * @throws GuzzleException
   * @throws ChildNotFoundException
   * @throws CircularException
   * @throws NotLoadedException
   * @throws StrictException
   */
  protected function parseImagesFromUrl(string $url): array
  {
    $images = [];
    if (\preg_match('/itunes.apple.com/', $url)) {
      $data = \file_get_contents($url);

      if ($data === false) {
        return [];
      }

      if (str_contains($data, 'metrics-loc="iPad"')) {
        \preg_match_all(/** @lang RegExp */ '/<div metrics-loc="iPad"(.*?)<\/div><\/div><\/div>/', $data, $out);
      } elseif (str_contains($data, 'metrics-loc="iPhone"')) {
        \preg_match_all(
        /** @lang RegExp */ '/<div metrics-loc="iPhone"(.*?)<\/div><\/div><\/div>/',
          $data,
          $out,
        );
      }

      if (isset($out)) {
        \preg_match_all('/src="(.*?)"/', $out[1][0], $out2);

        $temp_dir = Yii::getAlias('@root/images/temp/pics_get/');
        $temp_url = 'https://images.stopgame.ru/temp/pics_get/';

        $files = \glob($temp_dir . '*');
        if ($files === false) {
          $files = [];
        }

        // Очистим временную папку
        foreach ($files as $del_file) {
          \unlink($del_file);
        }

        $i = 1;
        foreach ($out2[1] as $pic) {
          $new_pic = $temp_dir . $i . '_' . \basename($pic);
          \copy($pic, $new_pic);
          $images[] = $temp_url . \basename($new_pic);
          $i++;
        }
      }
    } elseif (\preg_match('/play.google.com/', $url)) {
      $data = \file_get_contents($url);

      if ($data === false) {
        return [];
      }

      \preg_match_all('/<img([^>]+)data-screenshot-index[^>]+>/', $data, $out);

      $pic_array = [];
      foreach ($out[1] as $out2) {
        \preg_match_all('/src="([^"]+)"/', $out2, $out3);
        $pic_array[] = $out3[1][0];
      }

      $temp_dir = Yii::getAlias('@root/images/temp/pics_get/');
      $temp_url = Yii::$app->params['domain.images'] . '/temp/pics_get/';

      $files = \glob($temp_dir . '*');
      if ($files === false) {
        $files = [];
      }

      // Очистим временную папку
      foreach ($files as $del_file) {
        if (!\is_dir($del_file)) {
          \unlink($del_file);
        }
      }

      $i = 1;

      foreach ($pic_array as $pic) {
        $new_pic = $temp_dir . $i . '_' . \basename($pic) . '.jpg';
        \copy($pic, $new_pic);
        $i++;

        $images[] = $temp_url . \basename($new_pic);
      }
    } elseif (\preg_match('/steamcommunity.com/', $url)) {
      $data = \file_get_contents($url);

      if ($data === false) {
        return [];
      }

      \preg_match_all(
      /** @lang RegExp */
        "/<div class=\"highlight_player_item highlight_screenshot\"(.*?)ShowEnlargedImagePreview\( '(.*?)' \);(.*?)<\/div>/si",
        $data,
        $out,
      );

      foreach ($out[2] as $pic) {
        $images[] = $pic;
      }
    } elseif (\preg_match('/stopgame\.ru\/newsdata/', $url)) {
      $newsId = \explode('/', $url)[4];
      $news = NewsData::findOne($newsId);
      if (!$news) {
        return [];
      }

      if (!empty($news->editor_js_content)) {
        $editorJsContent = Json::decode($news->editor_js_content, false);
        foreach ($editorJsContent->blocks as $block) {
          if ($block->type === 'gallery') {
            $images = ArrayHelper::merge($images, $block->data->images);
          }
        }
      } else {
        $data = $news->news_text;

        \preg_match_all('/<div class=["\']fotorama(.*?)<\/div>/si', $data, $out);

        foreach ($out[1] as $pics) {
          \preg_match_all('/<img src=["\'](.*?)["\']/si', $pics, $out2);
          foreach ($out2[1] as $pic) {
            $images[] = \str_replace('/click/?', '', $pic);
          }
        }
      }
    } elseif (\preg_match('/epicgames.com\/store\//', $url)) {
      $client = new Client();
      $response = $client->request('GET', $url);

      $dom = new Dom();
      $dom->loadStr($response->getBody());

      /** @var Dom\Collection $containers */
      $containers = $dom->find('[data-component="PDPCarouselImageThumbnail"]');
      $containers->each(function (Dom\HtmlNode $container) use (&$images) {
        $container->find('img')?->each(function (Dom\HtmlNode $image) use (&$images) {
          $src = $image->getAttribute('src');
          if (!empty($src)) {
            $src = \explode('?', $src);
            $images[] = $src[0];
          }
        });
      });
    }

    return $images;
  }

  /**
   * @throws NotFoundHttpException
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['screens_access'], methods: ['POST'])]
  public function actionBulk(string $id): Response
  {
    /** @var ?Game $game */
    $game = Game::findOne($id);

    if (!$game || !$game->mainTitle) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    $images = $this->request->post('images');

    if (empty($images)) {
      Yii::$app->session->setFlash('error', 'Изображения для загрузки не указаны');
      return $this->redirect("/admin/screenshots/edit/$game->GameId");
    }

    $referer = ((array)\parse_url($images[0]))['host'] ?? null;

    $fullDir = (string)Yii::getAlias("@root/images/screenshots/$game->GameId");
    $smallDir = (string)Yii::getAlias("@root/images/screenshots/$game->GameId/small/");

    // Создаем необходимые каталоги
    if (!\is_dir($smallDir) && !\mkdir($smallDir, 0o777, true) && !\is_dir($smallDir)) {
      throw new RuntimeException(\sprintf('Directory "%s" was not created', $smallDir));
    }

    $md5InFolder = [];
    $countFiles = 0;
    $handle = \opendir($fullDir);
    $maxScreenId = 0;

    if (!$handle) {
      throw new RuntimeException("Не удалось открыть директорию $fullDir");
    }

    /** @noinspection PhpAssignmentInConditionInspection */
    while ($file = \readdir($handle)) {
      if (($file === '.') || ($file === '..') || \is_dir("$fullDir/$file")) {
        continue;
      }
      $md5InFolder[$file] = \md5_file("$fullDir/$file");
      $fname = \explode('-', \explode('.', $file)[0]);
      $screenId = \array_pop($fname);
      if (\is_numeric($screenId) && ($screenId > $maxScreenId)) {
        $maxScreenId = $screenId;
      }
      $countFiles++;
    }

    $newImages = [];

    // Если была маска
    foreach ($images as $image) {
      if (!\preg_match('/%\d+-\d+%/', $image)) {
        $newImages[] = $image;
        continue;
      }

      \preg_match_all('/%(.*?)-(.*?)%/s', $image, $mask);
      $mask_start = $mask[1][0];
      $mask_end = $mask[2][0];

      for ($i = $mask_start; $i <= $mask_end; $i++) {
        $newImages[] = \preg_replace('/%(.*?)-(.*?)%/s', $i, $image);
      }
    }

    // Качаем
    $files_down = $newImages;

    $file_temp = '/tmp/download_' . \md5(\time() . \implode("\n", $images));
    \file_put_contents($file_temp, \implode("\n", $files_down));

    ShellExecutor::run(
      "/usr/bin/aria2c -d '$fullDir' -s10 -j10 --user-agent='Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)' --check-certificate=false --referer='$referer' -i '$file_temp'",
    );

    \unlink($file_temp);

    $errors = [];
    $success = [];

    foreach ($newImages as $image) {
      $fileName = \basename($image);
      $filePath = "$fullDir/$fileName";
      if (!\file_exists($filePath)) {
        continue;
      }

      $duplicate = \array_search(\md5_file($filePath), $md5InFolder);

      if (!SgHelper::callSafe('getimagesize', $filePath)) {
        $errors[] = "$fileName — некорректное изображение";
        SgHelper::callSafe('unlink', $filePath);
      } elseif ($duplicate && ($duplicate !== $fileName)) {
        $errors[] = "$fileName — дубликат";
        SgHelper::callSafe('unlink', $filePath);
      } else {
        $countFiles++;
        $maxScreenId++;
        $newName = "{$game->mainTitle->GameNameUrl}-$maxScreenId.jpg";
        $fullPath = "$fullDir/$newName";
        $smallPath = "$smallDir/$newName";

        if (\filesize($filePath) > 20000000) {
          $errors[] = "$fileName — больше 20 мегабайт";
          SgHelper::callSafe('unlink', $filePath);
          $countFiles--;
          $maxScreenId--;
          continue;
        }

        \rename($filePath, $fullPath);
        SgImageHelper::cropCentered($fullPath, $smallPath);
        $resolution = \getimagesize($fullPath);

        if (!$resolution) {
          continue;
        }

        $screen = new GameGallery([
          'data_type' => 'screenshots',
          'GameId' => $game->GameId,
          'screen_file' => $newName,
          'pic_resolution' => "$resolution[0]x$resolution[1]",
          'pic_size' => \filesize($fullPath) ?: 0,
        ]);

        if (!$screen->save()) {
          throw new RuntimeException(Json::encode($screen->errors));
        }

        $success[] = "$fileName — загружен";
      }
    }

    $isNew = false;
    /** @var ?Article $screenPost */
    $screenPost = Article::find()
      ->joinWith('articleGames')
      ->where([
        'game_id' => $game->GameId,
        'data_type' => 'screenshots',
      ])
      ->one();

    if (!$screenPost) {
      $screenPost = new Article([
        'data_type' => 'screenshots',
        'data_url' => 'screenshots',
        'data_title' => 'скриншоты',
      ]);
      $isNew = true;
    }

    $screenPost->pic_count = $countFiles;
    $screenPost->setScenario(Article::SCENARIO_DRAFT);
    if (!$screenPost->save()) {
      throw new RuntimeException(Json::encode($screenPost->errors));
    }

    if (!empty($success)) {
      $success = \array_map(static fn($item) => "<li>$item</li>", $success);
      $success = \implode($success);
      Yii::$app->session->setFlash('success', "<ul>$success</ul>");
    }

    if (!empty($errors)) {
      $errors = \array_map(static fn($item) => "<li>$item</li>", $errors);
      $errors = \implode($errors);
      Yii::$app->session->setFlash('error', "<ul>$errors</ul>");
    }

    if ($isNew) {
      $articleGame = new ArticleGame([
        'article_id' => $screenPost->base_id,
        'game_id' => $game->GameId,
      ]);
      $articleGame->save();
      $screenPost->notify();
    }

    return $this->redirect("/admin/screenshots/edit/$game->GameId");
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['screens_access'], methods: ['POST'])]
  public function actionAddArchive(): string
  {
    $gameId = $this->request->post('game_id');
    $game = Game::findOne($gameId);

    if (!$game) {
      throw new NotFoundHttpException('Игра не найдена');
    }

    $archive = UploadedFile::getInstanceByName('archive');

    if (!$archive) {
      throw new BadRequestHttpException('Архив не загружен');
    }

    $images = [];

    if ($archive->tempName && \is_file($archive->tempName)) {
      $key = \md5($archive->tempName);
      $tempDir = (string)Yii::getAlias("@root/images/temp/pics_get/$key");
      ShellExecutor::run("/usr/bin/unzip -UU $archive->tempName -d $tempDir");
      $handle = \opendir($tempDir);

      if (!$handle) {
        throw new RuntimeException("Не удалось открыть директорию $tempDir");
      }

      /** @noinspection PhpAssignmentInConditionInspection */
      while ($file = \readdir($handle)) {
        if (\is_dir("$tempDir/$file")) {
          continue;
        }
        $mime = \mime_content_type("$tempDir/$file");
        if ($mime && str_starts_with($mime, 'image/')) {
          $images[] = Yii::$app->params['domain.images'] . "/temp/pics_get/$key/$file";
        }
      }
    }

    return $this->render('parse_step2', \compact('game', 'images'));
  }
}
