<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use yii\db\Expression;
use frontend\traits\Access;
use common\models\StalcraftItem;
use common\models\StalcraftUser;
use common\helpers\SgImageHelper;
use yii\base\InvalidConfigException;
use frontend\modules\ajax\ApiController;

/**
 * @noinspection PhpUnused
 */

class StalcraftController extends ApiController
{
  /**
   * @return array{
   *     user_team?: string|null,
   *     stalkers?: array<array{name: string, user_name: string, avatar: string}>,
   *     bandits?: array<array{name: string, user_name: string, avatar: string}>,
   *     stalkersCount?: int,
   *     banditsCount?: int,
   *     stalkersScore?: int,
   *     banditsScore?: int,
   *     collectedArtifacts?: array<string, int>,
   *     userPoints?: int
   * }
   * @noinspection PhpUnused
   * @throws InvalidConfigException
   */
  #[Access(methods: ['GET'])]
  public function actionInfo(): array
  {
    $allowedHosts = [
      'https://promo.dotterian.ru',
      'https://promo.stopgame.ru',
      'http://localhost:5173',
    ];
    $origin = $this->request->origin;

    if (!empty($origin) && \in_array($origin, $allowedHosts)) {
      $this->response->headers->add('Access-Control-Allow-Origin', $origin);
      $this->response->headers->add('Access-Control-Allow-Headers', '*');
      $this->response->headers->add('Access-Control-Allow-Credentials', 'true');
      $this->response->headers->add('Access-Control-Allow-Methods', 'GET, OPTIONS');
    }

    if ($this->request->method === 'OPTIONS') {
      return [];
    }


    $userTeam = Yii::$app->user->identity->stalcraftUser->team ?? null;
    $collectedArtifacts = [];
    $userPoints = Yii::$app->user->identity->stalcraftUser->score ?? 0;

    $stalkersCount = (int)StalcraftUser::find()
      ->where(['team' => 'stalker'])
      ->count();

    $banditsCount = (int)StalcraftUser::find()
      ->where(['team' => 'bandit'])
      ->count();

    $stalkersScore = StalcraftUser::find()
      ->where(['team' => 'stalker'])
      ->sum('score') ?? 0;

    $banditsScore = StalcraftUser::find()
      ->where(['team' => 'bandit'])
      ->sum('score') ?? 0;

    /**
     * @var array<array{
     *     name: string,
     *     user_name: string,
     *     avatar: string
     * }> $stalkers
     */
    $stalkers = StalcraftUser::find()
      ->select([
        'name' => new Expression('COALESCE(itaf_user.show_name, itaf_user.user_name)'),
        'itaf_user.user_name',
        'avatar' => 'itaf_user.user_avatar',
        'in_team' => new Expression('IF(user_team_info.user_id IS NOT NULL AND user_team_info.ex = 0, 1, 0)'),
      ])
      ->joinWith(['user', 'user.teamInfo'])
      ->where(['team' => 'stalker'])
      ->asArray()
      ->limit(100)
      ->orderBy(['in_team' => \SORT_DESC, 'score' => \SORT_DESC])
      ->all();

    /**
     * @var array<array{
     *     name: string,
     *     user_name: string,
     *     avatar: string
     * }> $bandits
     */
    $bandits = StalcraftUser::find()
      ->select([
        'name' => new Expression('COALESCE(itaf_user.show_name, itaf_user.user_name)'),
        'itaf_user.user_name',
        'avatar' => 'itaf_user.user_avatar',
        'in_team' => new Expression('IF(user_team_info.user_id IS NOT NULL AND user_team_info.ex = 0, 1, 0)'),
      ])
      ->joinWith(['user', 'user.teamInfo'])
      ->where(['team' => 'bandit'])
      ->asArray()
      ->limit(100)
      ->orderBy(['in_team' => \SORT_DESC, 'score' => \SORT_DESC])
      ->all();

    if (Yii::$app->user->isLoggedIn) {
      $collectedArtifacts = StalcraftItem::find()
        ->select(['COUNT(*) AS count', 'name'])
        ->where(['user_id' => Yii::$app->user->id])
        ->groupBy('name')
        ->indexBy('name')
        ->column();
    }

    $fillUser = static fn(array $user): array => [
      'name' => $user['name'],
      'user_name' => $user['user_name'],
      'avatar' => !empty($user['avatar']) ? $user['avatar'] : SgImageHelper::placeholderUrl(25, 25, 'avatar'),
    ];

    $stalkers = \array_map($fillUser, $stalkers);
    $bandits = \array_map($fillUser, $bandits);

    return [
      'user_team' => $userTeam,
      'stalkers' => $stalkers,
      'bandits' => $bandits,
      'stalkersCount' => $stalkersCount,
      'banditsCount' => $banditsCount,
      'stalkersScore' => $stalkersScore,
      'banditsScore' => $banditsScore,
      'collectedArtifacts' => $collectedArtifacts,
      'userPoints' => $userPoints,
    ];
  }
}
