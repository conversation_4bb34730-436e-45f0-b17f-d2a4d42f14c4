<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use frontend\traits\Access;
use yii\db\StaleObjectException;
use frontend\modules\admin\AdminController;
use yii\web\{Request, Response, NotFoundHttpException};
use common\models\{Platform, GamePlatform, PlatformSection};

/**
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class DictionariesController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $platforms = Platform::find()
      ->orderBy(['title' => \SORT_ASC])
      ->all();
    return $this->render('platforms', \compact('platforms'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET', 'POST'])]
  public function actionEditPlatform(string $id): Response|string
  {
    $errors = [];
    if ($id === 'new') {
      $platform = new Platform();
    } else {
      /** @var ?Platform $platform */
      $platform = Platform::findOne($id);
      if (!$platform) {
        throw new NotFoundHttpException('Платформа не найдена');
      }
      $oldCode = $platform->code;
      $sections = $platform->sections;
    }

    if ($this->request->isPost) {
      $post = $this->request->post();
      $post['Platform']['altTitles'] = \explode(',', $post['Platform']['altTitles']);
      $platform->load($post);
      if (!$platform->save()) {
        $errors[] = $platform->errors;
        goto render;
      }

      if (isset($oldCode, $sections) && ($oldCode !== $platform->code)) {
        foreach ($sections as $section) {
          $section->platforms = \array_map(static fn($code) => ($code === $oldCode) ? $platform->code : $code,
            $section->platforms ?? []);
          if (!$section->save()) {
            $errors[] = $section->errors;
            goto render;
          }
        }
      }
      return $this->redirect(['index']);
    }

    render:
    return $this->render('platform-editor', \compact('platform', 'errors'));
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionDeletePlatform(string $id): Response
  {
    $platform = Platform::findOne($id);
    if ($platform) {
      if (\count($platform->sections)) {
        foreach ($platform->sections as $section) {
          $section->platforms = \array_filter($section->platforms ?? [], static fn($code) => $code !== $platform->code);
          $section->save();
        }
      }

      GamePlatform::deleteAll(['platform_id' => $platform->id]);

      $platform->delete();
    }
    return $this->redirect(['index']);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionSections(): string
  {
    $sections = PlatformSection::find()
      ->all();
    return $this->render('sections', \compact('sections'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET', 'POST'])]
  public function actionEditSection(string $id): Response|string
  {
    if ($id === 'new') {
      $section = new PlatformSection();
    } else {
      /** @var ?PlatformSection $section */
      $section = PlatformSection::findOne($id);
      if (!$section) {
        throw new NotFoundHttpException('Группа не найдена');
      }
    }

    if ($this->request->isPost) {
      $section->load($this->request->post());
      if ($section->save()) {
        return $this->redirect(['sections']);
      }
    }

    return $this->render('section-editor', \compact('section'));
  }

  /**
   * @throws Throwable
   * @throws StaleObjectException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionDeleteSection(string $id): Response
  {
    $section = PlatformSection::findOne($id);
    $section?->delete();
    return $this->redirect(['sections']);
  }

  // Жанры игр

  // Жанры игрового кино

  // Жанры машинимы

  // Разделы новостей

  // Ачивки(?)
}
