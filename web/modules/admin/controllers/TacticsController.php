<?php

namespace frontend\modules\admin\controllers;

use Yii;
use Exception;
use Throwable;
use frontend\traits\Access;
use common\helpers\SgHelper;
use frontend\components\View;
use yii\db\ActiveQueryInterface;
use common\components\db\ActiveQuery;
use common\og_generators\LogoGenerator;
use yii\data\{Sort, ActiveDataProvider};
use frontend\modules\admin\AdminController;
use common\exceptions\ControlFlowException;
use common\widgets\formatters\PresentationFormatter;
use yii\web\{Request, Response, UploadedFile, NotFoundHttpException, ForbiddenHttpException};
use common\models\{ArticleGame, User, Article, GameTitle, GameRequest, PublishSchedule, GameRequestAttachment};

use function Sentry\captureException;

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class TacticsController extends AdminController
{
  /**
   * @var ActiveQuery<Article>
   */
  public ActiveQueryInterface $articlesQuery {
    get => Article::find()
      ->with('user', 'gameTitle', 'announce')
      ->where(['data_type' => 'tactics']);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['tactics_access'], methods: ['GET'])]
  public function actionIndex(): string
  {
    $query = $this->articlesQuery;
    $query->andWhere([
      'data_active' => 1,
    ]);
    $this->view->title = 'Советы и тактика ⋅ Админка';

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['tactics_access'], methods: ['GET'])]
  public function actionReadyToPublish(): string
  {
    $this->view->title = 'Советы и тактика ⋅ Админка';
    $query = $this->articlesQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ]);

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['tactics_access'], methods: ['GET'])]
  public function actionDrafts(): string
  {
    $this->view->title = 'Советы и тактика ⋅ Админка';
    $user = Yii::$app->user;
    $query = $this->articlesQuery
      ->andWhere([
        'data_active' => false,
        'ready_to_publish' => false,
      ]);

    if (!$user->can('articles_manage')) {
      $query->andWhere(['user_id' => $user->id]);
    }

    return $this->renderArticlesTable($query);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['tactics_create'], methods: ['GET'])]
  public function actionAdd(): string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Советы и тактика', '/admin/tactics');
    Yii::$app->breadcrumbs->addElement('Новый гайд');
    $this->view->title = 'Новый гайд ⋅ Админка';
    $article = new Article([
      'data_type' => 'tactics',
    ]);
    $article->user_id = (int)Yii::$app->user->id;
    $gameTitle = new GameTitle();
    $article->setScenario(Article::SCENARIO_DRAFT);

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['tactics_create'], methods: ['GET'])]
  public function actionEdit(string $id): string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Советы и тактика', '/admin/tactics');
    Yii::$app->breadcrumbs->addElement('Редактирование гайда');
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Гайд не найден');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя редактировать чужой гайд');
    }

    if (!$article->data_active && !$article->ready_to_publish) {
      $article->setScenario(Article::SCENARIO_DRAFT);
    }

    $gameTitle = $article->gameTitle ?? new GameTitle();
    $this->view->title = "$article->title ⋅ Админка";

    return $this->renderEditor($article, $gameTitle);
  }

  /**
   * @throws Throwable
   * @throws NotFoundHttpException
   * @throws ForbiddenHttpException
   */
  #[Access(permissions: ['tactics_create'], methods: ['POST'])]
  public function actionSave(): Response|string
  {
    Yii::$app->breadcrumbs->addElement('Админка', '/admin');
    Yii::$app->breadcrumbs->addElement('Советы и тактика', '/admin/tactics');
    $post = $this->request->post();

    $article = new Article([
      'data_type' => 'tactics',
    ]);
    $id = $post['Article']['base_id'];
    if ($id) {
      $article = Article::findOne($id);

      if (!$article) {
        throw new NotFoundHttpException('Гайд не найден');
      }

      if (!$article->canEdit) {
        throw new ForbiddenHttpException('Нельзя редактировать чужой гайд');
      }
    }

    $gameTitle = new GameTitle();
    $articleGames = [];

    $isNew = $article->isNewRecord;
    Yii::$app->breadcrumbs->addElement($isNew ? 'Новый гайд' : 'Редактирование гайда');

    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $gameId = $post['GameTitle']['GameId'] ?? null;
      if (\is_array($gameId)) {
        $gameId = $gameId[0];
      }
      $article->load($this->request->post());

      $skipValidation = false;
      if (isset($post['submit_article_draft'])) {
        $skipValidation = true;
        $article->setScenario(Article::SCENARIO_DRAFT);
      }

      $gameTitle = GameTitle::findOne(['GameId' => $gameId]);

      if ($gameTitle) {
        $articleGames[] = $gameTitle->GameId;
      } elseif (!$skipValidation) {
        $gameTitle = new GameTitle(['GameId' => $gameId]);
        $gameTitle->addError('GameId', 'Игра с таким названием не найдена в базе');
      }

      if (empty($articleGames) && !$skipValidation && $gameTitle) {
        $gameTitle->addError('GameId', 'Игра не указана');
      }

      $poster = UploadedFile::getInstance($article, 'data_logo_original');
      if ($poster) {
        $paths = SgHelper::getUploadPaths('articles');
        $poster->saveAs($paths['path']);
        $article->data_logo_original = $paths['url'];
      }

      if (!empty($article->data_logo_original)
        && ($article->isAttributeChanged('data_title')
          || $article->isAttributeChanged('data_short_text')
          || $article->isAttributeChanged('data_logo_original')
          || $article->isAttributeChanged('user_id')
          || $article->isAttributeChanged('data_type'))
      ) {
        $oldLogo = $article->getOldAttribute('data_logo');
        $oldLogo = \str_replace([Yii::$app->params['domain.images'], 'https://images.stopgame.ru'], '', $oldLogo);
        if (\is_array($oldLogo)) {
          $oldLogo = \implode('', $oldLogo);
        }
        $oldLogo = (string)Yii::getAlias('@root/images' . $oldLogo);
        if (\file_exists($oldLogo) && !\is_dir($oldLogo)) {
          \unlink($oldLogo);
        }
        $paths = SgHelper::getUploadPaths('articles');
        new LogoGenerator([
          'target' => $article,
          'path' => $paths['path']
        ])->run();
        $article->data_logo = $paths['url'];
      }

      if (!$article->data_active) {
        $article->data_active = 0;
        $article->ready_to_publish = (int)isset($post['submit_article_publish']);
      } else {
        $article->setScenario(Article::SCENARIO_PUBLISH);
      }

      /* @phpstan-ignore-next-line */
      if (!$article->user_id && $article->isNewRecord && ($article->user_id !== '0')) {
        $article->user_id = (int)Yii::$app->user->id;
      }

      /* @phpstan-ignore-next-line */
      if ($article->user_id === '0') {
        $article->user_id = null;
      }

      $article->data_text = new PresentationFormatter([
        'title' => $article->data_title,
        'data' => $article->editor_js_content ?? '[]',
        'page' => 'all',
      ])();

      if ($article->hasErrors() || $gameTitle?->hasErrors()
        || !$article->validate() || !$article->save()
      ) {
        throw new ControlFlowException('Не удалось сохранить гайд');
      }

      $article->refresh();

      $requestIds = $post['game_request_id'] ?? [];
      $requestTitles = $post['game_request_title'] ?? [];

      GameRequestAttachment::deleteAll([
        'target_type' => 'show',
        'target_id' => $article->base_id,
      ]);

      ArticleGame::deleteAll([
        'article_id' => $article->base_id,
      ]);

      // Привязываем запросы на добавление игр
      foreach ($requestTitles as $requestTitle) {
        $foundGame = GameTitle::findOne(['GameName' => $requestTitle]);

        // Если уже есть игра с таким названием, то просто привязываем игру
        if ($foundGame) {
          $articleGames[] = $foundGame->GameId;
        } else {
          $foundRequest = GameRequest::findOne(['title' => $requestTitle]);

          // Если уже есть запрос с таким названием
          if ($foundRequest) {
            // И он уже обработан — привязываем игру
            if ($foundRequest->game_id) {
              $articleGames[] = $foundRequest->game_id;
              // И он ещё не обработан — привязываем материал к запросу
            } else {
              $requestIds[] = $foundRequest->id;
            }
            // Если запроса нет — создаём его и привязываем материал к запросу
          } else {
            $request = new GameRequest([
              'title' => $requestTitle,
              'user_id' => Yii::$app->user->id,
            ]);
            $request->save();
            $request->user?->recalculateCredibility();
            $requestIds[] = $request->id;
          }
        }
      }

      $requestIds = \array_unique($requestIds);
      foreach ($requestIds as $requestId) {
        $attachment = new GameRequestAttachment([
          'request_id' => $requestId,
          'target_type' => 'show',
          'target_id' => $article->base_id,
        ]);
        $attachment->save();
      }

      $articleGames = \array_unique($articleGames);
      foreach ($articleGames as $gameId) {
        $articleGame = new ArticleGame([
          'article_id' => $article->base_id,
          'game_id' => $gameId,
        ]);
        $articleGame->save();
      }

      if (\count($article->dirtyAttributes) > 0) {
        $article->save();
      }

      $article->clearShowCache();
      $article->clearUnpublishedCache();

      $url = '/admin/tactics/drafts';
      if ($article->ready_to_publish) {
        $url = '/admin/tactics/ready-to-publish';
      } elseif ($article->data_active) {
        $url = '/admin/tactics';
      }
      $autosaveId = $isNew ? 'new' : $article->base_id;
      Yii::$app->session->setFlash('remove-autosave', 'tactic-form-' . $autosaveId);
      return $this->redirect($url);
    } catch (Exception $exception) {
      captureException($exception);
      $this->view->title = "$article->title ⋅ Админка";
      $article->setScenario(Article::SCENARIO_DRAFT);
      $url = '/admin/tactics/add';
      if (!$isNew) {
        $url = "/admin/tactics/edit/$article->base_id";
      }

      $message = $exception->getMessage();
      if (!empty($message)) {
        $article->addError('data_id', $message);
      }

      $this->view->registerJs("window.history.replaceState({}, '$article->title ⋅ Админка', '$url')");
      return $this->renderEditor($article, $gameTitle);
    }
  }

  /**
   * @throws NotFoundHttpException
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['tactics_manage'], methods: ['POST', 'GET'])]
  public function actionPublish(string $id): Response
  {
    $article = Article::findOne($id);
    if (!$article) {
      throw new NotFoundHttpException('Гайд не найден');
    }

    try {
      $article->publish();
      $article->notify();
    } catch (Exception $e) {
      Yii::$app->session->setFlash('error', $e->getMessage());
    }

    return $this->redirect('/admin/tactics/index');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['tactics_manage'], methods: ['POST', 'GET'])]
  public function actionUnpublish(string $id): Response
  {
    $article = Article::findOne($id);
    if (!$article) {
      throw new NotFoundHttpException('Гайд не найден');
    }
    $article->ready_to_publish = 0;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/tactics/drafts');
  }

  /**
   * @throws NotFoundHttpException|\yii\db\Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['tactics_create'], methods: ['POST', 'GET'])]
  public function actionToReview(string $id): Response
  {
    $article = Article::findOne($id);
    if (!$article) {
      throw new NotFoundHttpException('Гайд не найден');
    }
    $article->ready_to_publish = 1;
    $article->data_active = 0;
    if (!$article->save()) {
      Yii::$app->session->setFlash(
        'error',
        '<ul>'
        . \implode('\n', \array_map(static fn($item) => "<li>$item</li>", $article->getErrorSummary(true))),
      )
      . '</ul>';
    }
    $article->unnotify();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/tactics/ready-to-publish');
  }

  /**
   * @throws Throwable
   */
  protected function renderEditor(Article $article, ?GameTitle $gameTitle): string
  {
    $gameTitle?->setScenario(GameTitle::SCENARIO_ATTACH);

    $viewParams = \compact('article', 'gameTitle');

    if (Yii::$app->user->can('tactics_manage')) {
      /** @var User[] $allUsers */
      $allUsers = User::find()
        ->innerJoin('auth_assignment', 'auth_assignment.user_id = itaf_user.id')
        ->all();

      $viewParams['users'] = [];

      foreach ($allUsers as $user) {
        if (Yii::$app->authManager?->checkAccess($user->id, 'tactics_create')) {
          $viewParams['users'][$user->id] = $user->userName;
        }
      }
    }

    return $this->render('editor', $viewParams);
  }

  /**
   * @param ActiveQuery<Article> $query
   * @throws Throwable
   */
  protected function renderArticlesTable(ActiveQuery $query): string
  {
    $dataProvider = new ActiveDataProvider(\compact('query'));
    $dataProvider->setSort(
      new Sort([
        'defaultOrder' => ['data_add' => \SORT_DESC],
      ]),
    );

    $readyCount = $this->articlesQuery
      ->andWhere([
        'data_active' => 0,
        'ready_to_publish' => true,
      ])->count();

    if ($readyCount === '0') {
      $readyCount = null;
    }

    return $this->render('index', \compact('dataProvider', 'readyCount'));
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['tactics_create'], methods: ['POST', 'GET'])]
  public function actionDelete(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Гайд не найден');
    }

    if (!$article->canEdit) {
      throw new ForbiddenHttpException('Нельзя удалить чужой гайд');
    }

    if ($article->data_active || $article->ready_to_publish) {
      Yii::$app->session->setFlash('error', 'Нельзя удалить гайд не являющийся черновиком');
      return $this->redirect('/admin/tactics');
    }

    $article->delete();
    $article->clearShowCache();
    $article->clearUnpublishedCache();

    return $this->redirect('/admin/tactics/drafts');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['tactics_manage'], methods: ['POST', 'GET'])]
  public function actionSchedule(string $id): Response|string
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Гайд не найден');
    }

    $schedule = new PublishSchedule([
      'target_id' => $article->base_id,
      'target_type' => 'show',
      'announce' => 0,
      'notify' => 0,
    ]);

    if ($this->request->isPost) {
      $post = $this->request->post();
      $schedule->load($post);

      if ($schedule->save()) {
        $article->clearUnpublishedCache();
        return $this->redirect('/admin/tactics/ready-to-publish');
      }
    }

    $article->setScenario(Article::SCENARIO_PUBLISH);
    $isValid = $article->validate();

    return $this->render('schedule', \compact('article', 'schedule', 'isValid'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['tactics_manage'], methods: ['POST', 'GET'])]
  public function actionUnschedule(string $id): Response
  {
    $article = Article::findOne($id);

    if (!$article) {
      throw new NotFoundHttpException('Гайд не найден');
    }

    if ($article->schedule) {
      $article->schedule->delete();
      $article->clearUnpublishedCache();
    }

    return $this->redirect('/admin/tactics/ready-to-publish');
  }
}
