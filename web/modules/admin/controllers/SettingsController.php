<?php

namespace frontend\modules\admin\controllers;

use Throwable;
use common\models\Banword;
use frontend\traits\Access;
use frontend\components\View;
use yii\data\ArrayDataProvider;
use yii\web\{Request, Response};
use common\models\BannedEmailDomain;
use frontend\modules\admin\AdminController;

/**
 * @property-read View $view
 * @property-read Request $request
 * @noinspection PhpUnused
 */
class SettingsController extends AdminController
{
  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET', 'POST'])]
  public function actionIndex(): Response|string
  {
    return $this->actionEmailBans();
  }

  /**
   * @throws Throwable
   */
  #[Access(permissions: ['admin_acces_rbac'], methods: ['GET', 'POST'])]
  public function actionEmailBans(): Response|string
  {
    $this->view->title = 'Запрещённые почтовые домены ⋅ Админка';

    /** @var list<BannedEmailDomain> $domainsQuery */
    $domainsQuery = BannedEmailDomain::find()
      ->orderBy(['domain' => \SORT_ASC])
      ->all();

    $dataProvider = new ArrayDataProvider([
      'allModels' => $domainsQuery,
    ]);
    $dataProvider->setPagination(false);

    $newDomain = new BannedEmailDomain();

    if ($this->request->isPost) {
      $newDomain->load($this->request->post());
      if ($newDomain->save()) {
        return $this->redirect($this->request->referrer ?? '/admin/settings');
      }
    }

    return $this->render('email-bans', \compact('dataProvider', 'newDomain'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET', 'POST'])]
  public function actionDeleteEmailBan(string $id): void
  {
    $domain = BannedEmailDomain::findOne($id);
    $domain?->delete();
    $this->redirect($this->request->referrer ?? '/admin/settings');
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET', 'POST'])]
  public function actionBanwords(): Response|string
  {
    $this->view->title = 'Чёрный список слов ⋅ Админка';

    /** @var list<Banword> $banwords */
    $banwords = Banword::find()
      ->orderBy(['word' => \SORT_ASC])
      ->all();

    $dataProvider = new ArrayDataProvider([
      'allModels' => $banwords,
    ]);
    $dataProvider->setPagination(false);

    $newBanword = new Banword();

    if ($this->request->isPost) {
      $newBanword->load($this->request->post());
      if ($newBanword->save()) {
        return $this->redirect($this->request->referrer ?? '/admin/settings/banwords');
      }
    }

    return $this->render('banwords', \compact('dataProvider', 'newBanword'));
  }

  /**
   * @throws Throwable
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET', 'POST'])]
  public function actionDeleteBanword(string $id): void
  {
    $banword = Banword::findOne($id);
    $banword?->delete();
    $this->redirect($this->request->referrer ?? '/admin/settings/banwords');
  }
}
