<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use common\models\User;
use frontend\traits\Access;
use yii\web\NotFoundHttpException;
use frontend\modules\ajax\ApiController;
use yii\base\{Exception, InvalidConfigException};

/**
 * @noinspection PhpUnused
 */

class RbacController extends ApiController
{
  /**
   * @return array{
   *     result: bool
   * }
   * @throws Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['POST'])]
  public function actionAssign(string $role, string $permission): array
  {
    $auth = Yii::$app->authManager;
    if (!$auth) {
      throw new InvalidConfigException('Компонент AuthManager не существует!');
    }
    $value = $this->request->post('value');
    $roleModel = $auth->getRole($role);
    $permissionModel = $auth->getPermission($permission);

    if (!$roleModel || !$permissionModel) {
      return ['result' => false];
    }

    if ($value) {
      $auth->addChild($roleModel, $permissionModel);
    } else {
      $auth->removeChild($roleModel, $permissionModel);
    }

    return ['result' => true];
  }

  /**
   * @return array{
   *     users: array<array{
   *         userId: int,
   *         userName: string,
   *         avatar: string,
   *         profileUrl: string
   *     }>
   * }
   * @throws NotFoundHttpException
   * @throws \Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['GET'])]
  public function actionUsers(string $id): array
  {
    $rbac = Yii::$app->authManager;
    if (!$rbac) {
      throw new InvalidConfigException('Компонент AuthManager не существует!');
    }
    $role = $rbac->getRole($id);

    if (!$role) {
      throw new NotFoundHttpException('Роль не найдена');
    }

    $ids = $rbac->getUserIdsByRole($role->name);
    /** @var User[] $userModels */
    $userModels = User::find()
      ->where(['id' => $ids])
      ->all();

    $users = [];
    foreach ($userModels as $user) {
      $users[] = [
        'userId' => $user->id,
        'userName' => $user->userName,
        'avatar' => $user->getAvatar(44),
        'profileUrl' => $user->profileUrl,
      ];
    }

    return \compact('users');
  }

  /**
   * @return array{
   *     success: bool
   * }
   * @throws \Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['POST'])]
  public function actionAddRole(): array
  {
    $roleName = $this->request->post('roleName');
    $userId = $this->request->post('userId');
    $auth = Yii::$app->authManager;
    if (!$auth) {
      throw new InvalidConfigException('Компонент AuthManager не существует!');
    }

    $role = $auth->getRole($roleName);
    $user = User::findOne($userId);

    $success = false;
    if ($role && $user) {
      $auth->assign($role, $userId);
      $success = true;
    }

    return \compact('success');
  }

  /**
   * @return array{
   *     success: bool
   * }
   * @noinspection PhpUnused
   * @throws InvalidConfigException
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['POST'])]
  public function actionRemoveRole(): array
  {
    $roleName = $this->request->post('roleName');
    $userId = $this->request->post('userId');
    $rbac = Yii::$app->authManager;
    if (!$rbac) {
      throw new InvalidConfigException('Компонент AuthManager не существует!');
    }

    $role = $rbac->getRole($roleName);
    $user = User::findOne($userId);

    $success = $role && $user && $rbac->revoke($role, $userId);

    return \compact('success');
  }

  /**
   * @return array{
   *     success: bool
   * }
   * @throws \Exception
   * @noinspection PhpUnused
   */
  #[Access(permissions: ['admin_access_rbac'], methods: ['POST'])]
  public function actionAddUser(): array
  {
    $login = $this->request->post('login');
    $roleName = $this->request->post('roleName');
    $rbac = Yii::$app->authManager;
    if (!$rbac) {
      throw new InvalidConfigException('Компонент AuthManager не существует!');
    }
    $role = $rbac->getRole($roleName);
    $user = User::findOne(['user_name' => $login]);

    $success = false;
    if ($role && $user) {
      $rbac->assign($role, $user->id);
      $success = true;
    }

    return \compact('success');
  }
}
