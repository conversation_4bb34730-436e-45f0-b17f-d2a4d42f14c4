<?php

namespace frontend\modules\ajax\controllers;

use Yii;
use Exception;
use Throwable;
use yii\helpers\Html;
use frontend\traits\Access;
use yii\base\InvalidConfigException;
use common\og_generators\LogoGenerator;
use frontend\modules\ajax\ApiController;
use common\exceptions\ControlFlowException;
use common\widgets\formatters\PresentationFormatter;
use yii\web\{UploadedFile, NotFoundHttpException, ForbiddenHttpException};
use common\helpers\{SgHelper, SgTextHelper, SgStringHelper, SectionsHelper};
use common\models\{Article, GameTitle, GameRating, GameRequest, ArticleGame, GameRequestAttachment};

use function Sentry\captureException;

/**
 * @noinspection PhpUnused
 */

class VideoController extends ApiController
{
  /**
   * @return mixed[]
   * @noinspection PhpUnused
   * @throws InvalidConfigException
   */
  #[Access(methods: ['GET', 'POST'])]
  public function actionListForUpload(): array
  {
    if ($this->request->isPost) {
      $video = $this->request->post('file');
      $newName = $this->request->post('new_name');

      if (!$video) {
        $this->response->setStatusCode(400, 'Пустое поле file');
        return ['success' => false, 'message' => 'Пустое поле file'];
      }

      if (!$newName) {
        $this->response->setStatusCode(400, 'Пустое поле new_name');
        return ['success' => false, 'message' => 'Пустое поле new_name'];
      }

      Article::updateAll(['video_ftp' => null, 'data_file' => $newName], ['video_ftp' => $video]);

      return ['success' => true];
    }

    return Article::find()
      ->select('video_ftp')
      ->where(['not', ['video_ftp' => null]])
      ->andWhere(['data_active' => 1])
      ->column();
  }

  /**
   * @return array{
   *     success: bool,
   *     redirect?: string,
   *     errors?: array<string, array<string, array<string>>>
   * }
   * @throws Throwable
   */
  #[Access(permissions: ['video_create'], methods: ['POST'])]
  public function actionSave(): array
  {
    $post = $this->request->post();

    $article = new Article();
    $id = $post['Article']['base_id'];
    if ($id) {
      $article = Article::findOne($id);

      if (!$article) {
        throw new NotFoundHttpException('Видео не найдено');
      }

      if (!$article->canEdit) {
        throw new ForbiddenHttpException('Нельзя редактировать чужое видео');
      }
    }

    $gameTitle = new GameTitle();
    $gameRating = new GameRating();
    $articleGames = [];

    $isNew = $article->isNewRecord;

    /** @noinspection BadExceptionsProcessingInspection */
    try {
      $gameIds = $post['GameTitle']['GameId'] ?? [];
      if (isset($post['Article']['audio_file'])) {
        unset($post['Article']['audio_file']);
      }
      if (!isset($post['Article']['show_section_in_telegram'])) {
        $post['Article']['show_section_in_telegram'] = 0;
      }
      if (!isset($post['Article']['press_release'])) {
        $post['Article']['press_release'] = 0;
      }
      $article->load($post);

      $skipValidation = false;
      if (isset($post['submit_article_draft'])) {
        $skipValidation = true;
        $article->setScenario(Article::SCENARIO_DRAFT);
      }

      if (!empty($gameIds)) {
        foreach ($gameIds as $gameId) {
          $gameTitle = GameTitle::findOne(['GameId' => $gameId]);

          if ($gameTitle) {
            $articleGames[] = $gameTitle->GameId;

            if ($article->data_type === 'review') {
              $gameRating = $gameTitle->info->rating ?? new GameRating(['GameId' => $gameTitle->GameId]);
              $gameRating->load($post);

              if (!isset($post['GameRating']['StopChoice'])) {
                $gameRating->StopChoice = 0;
              }

              if (!$skipValidation && ($gameRating->StopRating === 0)) {
                $gameRating->addError('StopgameRating', 'Оценка игры не указана');
              }

              if (!empty($post['Game']['GameInfo']) && $gameTitle->info) {
                $gameTitle->info->GameInfo = $post['Game']['GameInfo'];
                $gameTitle->info->save();
              }
            }
          } elseif (!$skipValidation) {
            $gameTitle = new GameTitle(['GameId' => $gameId]);
            $gameTitle->addError('GameId', 'Игра с таким названием не найдена в базе');
          }
        }
      }

      if (!$skipValidation && \in_array($article->data_type, ['review', 'preview'])) {
        if (empty($articleGames) && $gameTitle) {
          $gameTitle->addError('GameId', 'Игра не указана');
        }

        if (($article->data_platform === '') && ($article->data_type === 'review')) {
          $article->addError('data_platform', 'Актуальная платформа не указана');
        }
      }

      $poster = UploadedFile::getInstance($article, 'data_logo_original');
      if ($poster) {
        $paths = SgHelper::getUploadPaths('articles');
        $poster->saveAs($paths['path']);
        $article->data_logo_original = $paths['url'];
      }

      $poster = UploadedFile::getInstance($article, 'data_logo_crop');
      if ($poster) {
        $paths = SgHelper::getUploadPaths('articles');
        $poster->saveAs($paths['path']);
        $article->data_logo_crop = $paths['url'];
      }

      if (!empty($article->data_logo_original)
        && ($article->data_logo_original !== $article->getOldAttribute('data_logo'))
        && ($article->isAttributeChanged('data_title')
          || $article->isAttributeChanged('data_short_text')
          || $article->isAttributeChanged('data_logo_original')
          || $article->isAttributeChanged('user_id')
          || $article->isAttributeChanged('data_type'))
      ) {
        $oldLogo = $article->getOldAttribute('data_logo');
        if ($oldLogo) {
          $oldLogo = \str_replace([Yii::$app->params['domain.images'], 'https://images.stopgame.ru'], '', $oldLogo);
          if (\is_array($oldLogo)) {
            $oldLogo = \implode('', $oldLogo);
          }
          $oldLogo = (string)Yii::getAlias('@root/images' . $oldLogo);
          if (\file_exists($oldLogo) && !\is_dir($oldLogo)) {
            \unlink($oldLogo);
          }
        }
        $paths = SgHelper::getUploadPaths('articles');
        new LogoGenerator([
          'target' => $article,
          'path' => $paths['path']
        ])->run();
        $article->data_logo = $paths['url'];
      }

      if (empty($article->video_attr)) {
        $article->video_attr = 'b:0;';
      }
      if (!empty($article->video_ftp)) {
        $article->video_ftp = (string)\preg_replace('/https?:\/\/my.stopgame.ru/', '', $article->video_ftp);
      } else {
        $article->video_ftp = null;
      }

      if ($article->data_type === 'podcast') {
        $file = UploadedFile::getInstance($article, 'audio_file');

        if ($file) {
          $translit = SgStringHelper::translit($article->title);
          $fileName = \strtolower(\stripslashes("$translit.$file->extension"));
          $filePath = "/home/<USER>/tools/podcasts/uploads/$fileName";
          $file->saveAs($filePath);
          $article->audio_file = $fileName;
        }
      }

      if (!$article->data_active) {
        $article->data_active = 0;
        $article->ready_to_publish = (int)isset($post['submit_article_publish']);
      } else {
        $article->setScenario(Article::SCENARIO_PUBLISH);
      }

      /* @phpstan-ignore-next-line */
      if (!$article->user_id && $article->isNewRecord && ($article->user_id !== '0')) {
        $article->user_id = (int)Yii::$app->user->id;
      }

      // @phpstan-ignore-next-line
      if ($article->user_id === '0') {
        $article->user_id = null;
      }

      $article->data_text = new PresentationFormatter([
        'title' => $article->data_title,
        'data' => $article->editor_js_content ?? '[]',
        'page' => 'all',
      ])();

      $saveRating = false;
      if ($gameRating->isAttributeChanged('StopRating') || $gameRating->isAttributeChanged('StopChoice')) {
        $saveRating = true;
      }

      if (!SectionsHelper::isVideo($article->data_type ?? 'nonexistent')) {
        $article->data_url = 'video';
      }

      if ($article->hasErrors() || $gameTitle?->hasErrors() || $gameRating->hasErrors()
        || !$article->validate() || !$article->save()
        || ($saveRating && (!$gameRating->validate() || !$gameRating->save()))
      ) {
        throw new ControlFlowException('Не удалось сохранить видео');
      }

      $file = UploadedFile::getInstanceByName('subtitles');
      if ($file !== null) {
        $mail = Yii::$app->mailer
          ->compose()
          ->setFrom('<EMAIL>')
          ->setTo('<EMAIL>')
          ->setSubject('Субтитры к «' . SgTextHelper::cut($article->title, ellipsis: true) . '»')
          ->attach($file->tempName, ['fileName' => $file->name]);

        if (!empty($article->video_external)) {
          $mail->setHtmlBody(Html::a('Ссылка на Youtube', $article->video_external));
        }

        $mail->send();
      } elseif (!empty($post['subtitles_link'])) {
        $mail = Yii::$app->mailer
          ->compose()
          ->setFrom('<EMAIL>')
          ->setTo('<EMAIL>')
          ->setSubject('Субтитры к «' . SgTextHelper::cut($article->title, ellipsis: true) . '»');

        $html = '';
        if (!empty($article->video_external)) {
          $html .= Html::a('Ссылка на Youtube', $article->video_external) . '<br>';
        }

        $html .= Html::a('Ссылка на субтитры', $post['subtitles_link']);
        $mail->setHtmlBody($html);
        $mail->send();
      }

      $article->refresh();

      $requestIds = $post['game_request_id'] ?? [];
      $requestTitles = $post['game_request_title'] ?? [];

      GameRequestAttachment::deleteAll([
        'target_type' => 'show',
        'target_id' => $article->base_id,
      ]);

      ArticleGame::deleteAll([
        'article_id' => $article->base_id,
      ]);

      // Привязываем запросы на добавление игр
      foreach ($requestTitles as $requestTitle) {
        $foundGame = GameTitle::findOne(['GameName' => $requestTitle]);

        // Если уже есть игра с таким названием, то просто привязываем игру
        if ($foundGame) {
          $articleGames[] = $foundGame->GameId;
        } else {
          $foundRequest = GameRequest::findOne(['title' => $requestTitle]);

          // Если уже есть запрос с таким названием
          if ($foundRequest) {
            // И он уже обработан — привязываем игру
            if ($foundRequest->game_id) {
              $articleGames[] = $foundRequest->game_id;
              // И он ещё не обработан — привязываем материал к запросу
            } else {
              $requestIds[] = $foundRequest->id;
            }
            // Если запроса нет — создаём его и привязываем материал к запросу
          } else {
            $request = new GameRequest([
              'title' => $requestTitle,
              'user_id' => Yii::$app->user->id,
            ]);
            $request->save();
            $request->user?->recalculateCredibility();
            $requestIds[] = $request->id;
          }
        }
      }

      $requestIds = \array_unique($requestIds);
      foreach ($requestIds as $requestId) {
        $attachment = new GameRequestAttachment([
          'request_id' => $requestId,
          'target_type' => 'show',
          'target_id' => $article->base_id,
        ]);
        $attachment->save();
      }

      $articleGames = \array_unique($articleGames);
      foreach ($articleGames as $gameId) {
        $articleGame = new ArticleGame([
          'article_id' => $article->base_id,
          'game_id' => $gameId,
        ]);
        $articleGame->save();
      }

      if (\count($article->dirtyAttributes) > 0) {
        $article->save();
      }

      $article->clearShowCache();
      $article->clearUnpublishedCache();

      $url = '/admin/video/drafts';
      if ($article->ready_to_publish) {
        $url = '/admin/video/ready-to-publish';
      } elseif ($article->data_active) {
        $url = '/admin/video';
      }
      $autosaveId = $isNew ? 'new' : $article->base_id;
      Yii::$app->session->setFlash('remove-autosave', 'article-form-' . $autosaveId);
      return [
        'success' => true,
        'redirect' => $url,
      ];
    } catch (Exception $exception) {
      captureException($exception);
      return [
        'success' => false,
        'errors' => [
          'Article' => $article->errors,
          'GameTitle' => $gameTitle->errors ?? [],
          'GameRating' => $gameRating->errors,
        ],
      ];
    }
  }
}
